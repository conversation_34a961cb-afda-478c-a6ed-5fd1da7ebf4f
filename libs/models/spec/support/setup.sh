cat > $(cd $(dirname ${BASH_SOURCE[0]}) && pwd)/../../src/gvis/index.ts <<EOF
export * from './graphParser';
export * from './color';
export * from './graphchart.data.link';
export * from './graphchart.data.node';
export * from './chartSetting';
EOF

cat > $(cd $(dirname ${BASH_SOURCE[0]}) && pwd)/../../src/utils/index.ts <<EOF
export * from './backoff';
export * from './resettable';
export * from './format-validator';
export * from './helper-functions';
export * from './module-import-guard';
export * from './typedef-helper';
export * from './constants';
EOF