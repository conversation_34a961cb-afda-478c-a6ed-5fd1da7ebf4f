cat > $(cd $(dirname ${BASH_SOURCE[0]}) && pwd)/../../src/gvis/index.ts <<EOF
export * from './graphParser';
export * from './color';
export * from './graphchart.data.link';
export * from './graphchart.data.node';
export * from './chartSetting';
export {
    BaseApi,
    BaseData,
    BaseMouseEvent,
    BaseKeyboardEvent,
    ChartSetting,
    BaseStyle,
    BarChartSetting,
    BarChart,
    BarChartDataPoint,
    BarChartData,
    BarChartEventHandlers,
    EChartsMouseEvent,
    BarChartEvent,
    EBarChartsOptions,
    BarChartRender,
    BarChartStyle,
    GeospatialChart,
    GraphChartSetting,
    GraphChart,
    ExternalGraph,
    GraphChartData,
    GraphDataIDSet,
    DroppedItems,
    GraphDataInternalData,
    ExternalLink,
    AddLinkOption,
    GraphChartDataLink,
    ExternalNode,
    GraphChartDataNode,
    GraphChartEvent,
    GraphChartLayout,
    GraphChartRenderCanvas,
    GraphChartRender,
    GraphChartRenderEngineBase,
    GraphChartRenderSVG,
    GraphChartRenderWebgl,
    GraphStyle as GVISGraphStyle, // GraphStyle conflicts with a class name of a topology model
    ItemsData,
    ItemsDataLink,
    ItemsDataNode,
    LineChartSetting,
    LineChart,
    SeriesDataPoint,
    LineChartData,
    LineChartEventHandlers,
    LineChartEvent,
    LineChartRender,
    ELineChartsOptions,
    LineChartRenderEngine,
    LineChartStyle,
    PieChartSetting,
    PieChartDataPoint,
    PieChart,
    PieChartEventHandlers,
    PieChartEvent,
    EPieChartsOptions,
    PieChartRender,
    PieChartStyle,
    Color,
    GException,
    builtinIcons,
    otherIcons,
    ICON,
    Localization,
    GraphParserFormat,
    GraphParser,
    ArrayParser,
    Utils,
} from 'gvis';
EOF

cat > $(cd $(dirname ${BASH_SOURCE[0]}) && pwd)/../../src/utils/index.ts <<EOF
export * from './backoff';
export * from './resettable';
export * from './format-validator';
export * from './helper-functions';
export * from './module-import-guard';
export * from './typedef-helper';
export * from './constants';
export * from './gsql-code-mode';
EOF