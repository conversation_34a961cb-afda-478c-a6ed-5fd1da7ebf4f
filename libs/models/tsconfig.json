{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "module": "commonjs", "outDir": "./dist/out-tsc", "sourceMap": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "declaration": true, "declarationDir": "dist", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2015", "downlevelIteration": true, "typeRoots": ["node_modules/@types"], "lib": ["es2017", "dom"], "esModuleInterop": true, "paths": {"jszip": ["node_modules/jszip/dist/jszip.min.js"], "@app/*": ["src/app/*"], "@assets/*": ["src/assets/*"], "@environments/*": ["src/environments/*"]}}}