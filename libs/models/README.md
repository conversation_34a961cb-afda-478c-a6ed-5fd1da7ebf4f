# Shared Models

The Shared Models are extracted from Graph Studio, Graph Admin Portal, Insights and GShell applications. It provides reusable data models for better maintenance and development consistency.

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

If you haven't installed the dependencies run `yarn`.

Run `yarn build` to build the project. The build artifacts will be stored in the `dist/` directory.

### Development

In the project directory, run `yarn link:models`.

The app will automatically link the models.

### Running unit tests

Run `yarn test:local` to execute the unit tests by jasmine.
