import { DataType } from '../data';
import { Graph } from '../topology';

import { Atom, AtomJson } from './atom.model';
import { BaseCondition, ConditionJson } from './condition.model';
import { BaseExpression, ExpressionJson } from './expression.model';
import {
  AtomFactory, ConditionFactory, ExpressionFactory,
  FactoryProducer, atomDict, condDict, exprDict
} from './factory';

/**
 * Parse from JSON.
 *
 * @export
 * @param {(AtomJson | ExpressionJson | ConditionJson)} json
 * @param {Graph} [schema]
 * @returns {(Atom | BaseExpression | BaseCondition)}
 */
export function parseExprJson(
  json: AtomJson | ExpressionJson | ConditionJson,
  schema?: Graph
): Atom | BaseExpression | BaseCondition {
  if (json !== undefined) {
    const classType = json.type;
    // Atom type.
    if (
      classType.includes('Accum') ||
      classType.includes('Constant') ||
      classType === 'AliasReference' ||
      classType.includes('Param') ||
      classType.includes('Variable')
    ) {
      const factory = FactoryProducer.getFactory(AtomFactory);
      let attributeType: DataType;

      if (
        (classType === 'AttrVariable' || classType === 'AttrVariableWithAlias')
        && (<AtomJson>json).value
        && schema
      ) {
        schema.getAllEdgeTypes().forEach(
          type => schema.getEdge(type).attributes.forEach(
            attr => {
              if (`${type}.${attr.name}` === (<AtomJson>json).value) {
                return attributeType = attr.type.getDataType();
              }
            }
          )
        );
        schema.getAllVertexTypes().forEach(
          (type) => {
            schema.getVertex(type).attributes.forEach(
              attr => {
                if (`${type}.${attr.name}` === (<AtomJson>json).value) {
                  return attributeType = attr.type.getDataType();
                }
              }
            );

            if (schema.getVertex(type).primaryId) {
              if (`${type}.${schema.getVertex(type).primaryId.name}` === (<AtomJson>json).value) {
                return attributeType = schema.getVertex(type).primaryId.type.getDataType();
              }
            }
          }
        );
      }

      return factory.getAtom(
        atomDict[classType],
        (<AtomJson>json).value,
        attributeType
      );
    // Expression type.
    } else if (classType.includes('Expression')) {
      const factory = FactoryProducer.getFactory(ExpressionFactory);
      const operands = (<ExpressionJson>json).operands.map(
        operand => (<Atom | BaseExpression>parseExprJson(operand, schema))
      );

      return factory.getExpression(
        exprDict[classType],
        operands,
        (<ExpressionJson>json).operator
      );
    // Condition type.
    } else {
      const factory = FactoryProducer.getFactory(ConditionFactory);
      const operands = (<ConditionJson>json).operands.map(
        operand => parseExprJson(operand, schema)
      );

      return factory.getCondition(
        condDict[classType],
        operands,
        (<ConditionJson>json).operator
      );
    }
  }
}
