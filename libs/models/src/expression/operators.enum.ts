/**
 * Const enum of arithmatic operators.
 *
 * @export
 * @enum {string}
 */
export enum ArithmeticOperator {
  Plus = '+',
  Minus = '-',
  Multiply = '*',
  Divide = '/',
  Power = '^',
  Mod = '%'
}

/**
 * Const enum of relational operators.
 *
 * @export
 * @enum {string}
 */
export enum RelationalOperator {
  Equal = '==',
  NotEqual = '!=',
  Larger = '>',
  LargerOrEqual = '>=',
  Smaller = '<',
  SmallerOrEqual = '<='
}

/**
 * Const enum of predicate operators.
 *
 * @export
 * @enum {string}
 */
export enum PredicateOperator {
  IsNumeric = 'IS NUMERIC',
  IsEmpty = 'IS EMPTY',
  In = 'IN',
  BetweenAnd = 'BETWEEN AND',
  And = 'AND',
  Or = 'OR',
  Not = 'NOT'
}

/**
 * Const enum of functions.
 *
 * @export
 * @enum {string}
 */
export enum FunctionType {
  ToFloat = 'to_float',
  ToInt = 'to_int',
  ToDatetime = 'to_datetime',
  Concat = 'concat',
  TokenLen = 'token_len',
  Sqrt = 'sqrt',
  Count = 'COUNT(*)',
  DistinctCount = 'DISTINCT COUNT',
  Sum = 'SUM',
  Min = 'MIN',
  Max = 'MAX',
  Avg = 'AVG',
  Collect = 'COLLECT',
  DistinctCollect = 'DISTINCT COLLECT'
}

/**
 * Const enum of currently supported functions and operators.
 *
 * @export
 * @enum {string}
 */
export const SupportedOperatorAndFunctionTypes = [
  ArithmeticOperator.Plus,
  ArithmeticOperator.Minus,
  ArithmeticOperator.Multiply,
  ArithmeticOperator.Divide,
  ArithmeticOperator.Power,
  ArithmeticOperator.Mod,
  RelationalOperator.Equal,
  RelationalOperator.NotEqual,
  RelationalOperator.Larger,
  RelationalOperator.LargerOrEqual,
  RelationalOperator.Smaller,
  RelationalOperator.SmallerOrEqual,
  PredicateOperator.IsNumeric,
  PredicateOperator.IsEmpty,
  PredicateOperator.In,
  PredicateOperator.BetweenAnd,
  PredicateOperator.And,
  PredicateOperator.Or,
  PredicateOperator.Not,
  FunctionType.ToFloat,
  FunctionType.ToInt,
  FunctionType.ToDatetime,
  FunctionType.Concat,
  FunctionType.TokenLen,
  FunctionType.Sqrt,
  FunctionType.Count,
  FunctionType.DistinctCount,
  FunctionType.Sum,
  FunctionType.Min,
  FunctionType.Max,
  FunctionType.Avg
];
