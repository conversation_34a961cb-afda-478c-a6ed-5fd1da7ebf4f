import { DataType } from '../data';
import { FormatValidator } from '../utils';

import {
  AttrVariable, BoolAccum, BoolConstant,
  IntAccum, IntConstant, LoadingVariable,
  RealAccum, RealConstant, StringConstant,
  StringAccum
} from './atom.model';

describe('AtomModel', () => {

  describe('with IntConstant', () => {

    it('should construct an integer type constant with an integer value successfully', () => {
      const intConstant = new IntConstant(20);
      expect(intConstant.value).toBe(20);
      expect(intConstant.valueType).toBe(DataType.Int);
    });

    it('should get type "Integer" with getType function', () => {
      const intConstant = new IntConstant(1);
      expect(intConstant.getType()).toBe(DataType.Int);
    });

    it('should return the correct string with toString function', () => {
      const intConstant = new IntConstant(20);
      expect(intConstant.toString()).toBe('20');
    });

    it('should return the correct JSON content with toJson function', () => {
      const intConstant = new IntConstant(20);
      const json = intConstant.toJson();
      expect(json.type).toBe('IntConstant');
      expect(json.value).toBe(intConstant.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const intConstant = new IntConstant(20);
      spyOn(FormatValidator, 'isInt').and.returnValue({ success: true, message: '' });
      const result = intConstant.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check when trying to construct an integer type constant with a non-integer value', () => {
      const wrongCons = new IntConstant(12.12);
      spyOn(FormatValidator, 'isInt').and.returnValue({ success: false, message: 'Should be integer.' });
      const result = wrongCons.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Should be integer.']);
    });

    it('should return the correct value with the evaluate function', () => {
      const intConstant = new IntConstant(1);
      expect(intConstant.evaluate().applicable).toBeTruthy();
      expect(intConstant.evaluate().value).toBe(1);
    });
  });

  describe('with RealConstant', () => {

    it('should construct a real type constant with a real value successfully', () => {
      const realConstant = new RealConstant(12.12);
      expect(realConstant.value).toBe(12.12);
      expect(realConstant.valueType).toBe(DataType.Real);
    });

    it('should get type "Real" with getType function', () => {
      const realConstant = new RealConstant(1);
      expect(realConstant.getType()).toBe(DataType.Real);
    });

    it('should return the correct string with toString function', () => {
      const realConstant = new RealConstant(1);
      expect(realConstant.toString()).toBe('1');
    });

    it('should return the correct JSON content with toJson function', () => {
      const realConstant = new RealConstant(12.0);
      const json = realConstant.toJson();
      expect(json.type).toBe('RealConstant');
      expect(json.value).toBe(realConstant.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const realConstant = new RealConstant(1.1);
      spyOn(FormatValidator, 'isReal').and.returnValue({ success: true, message: '' });
      const result = realConstant.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check', () => {
      const neg = new RealConstant(0);
      spyOn(FormatValidator, 'isReal').and.returnValue({ success: false, message: 'Should be real number.' });
      const result = neg.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Should be real number.']);
    });

    it('should return the correct value with the evaluate function', () => {
      const realConstant = new RealConstant(1.1);
      expect(realConstant.evaluate().applicable).toBeTruthy();
      expect(realConstant.evaluate().value).toBe(1.1);
    });
  });

  describe('with StringConstant', () => {

    it('should construct a string type constant with a string value successfully', () => {
      const stringConstant = new StringConstant('test');
      expect(stringConstant.value).toBe('test');
      expect(stringConstant.valueType).toBe(DataType.String);
    });

    it('should get type "String" with getType function', () => {
      const stringConstant = new StringConstant('test');
      expect(stringConstant.getType()).toBe(DataType.String);
    });

    it('should return the correct string with toString function', () => {
      const stringConstant = new StringConstant('test');
      expect(stringConstant.toString()).toBe('"test"');
    });

    it('should return the correct JSON content with toJson function', () => {
      const stringConstant = new StringConstant('test');
      const json = stringConstant.toJson();
      expect(json.type).toBe('StringConstant');
      expect(json.value).toBe(stringConstant.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const stringConstant = new StringConstant('test');
      const result = stringConstant.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should return the correct value with the evaluate function', () => {
      const stringConstant = new StringConstant('a');
      expect(stringConstant.evaluate().applicable).toBeTruthy();
      expect(stringConstant.evaluate().value).toBe('a');
    });
  });

  describe('with BoolConstant', () => {

    it('should construct a boolean type constant with a boolean value successfully', () => {
      const boolConstant = new BoolConstant(true);
      expect(boolConstant.value).toBe(true);
      expect(boolConstant.valueType).toBe(DataType.Bool);
    });

    it('should get type "Boolean" with getType function', () => {
      const boolConstant = new BoolConstant(true);
      expect(boolConstant.getType()).toBe(DataType.Bool);
    });

    it('should return the correct string with toString function', () => {
      const boolConstant = new BoolConstant(true);
      expect(boolConstant.toString()).toBe('true');
    });

    it('should return the correct JSON content with toJson function', () => {
      const boolConstant = new BoolConstant(true);
      const json = boolConstant.toJson();
      expect(json.type).toBe('BoolConstant');
      expect(json.value).toBe(boolConstant.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const boolConstant = new BoolConstant(true);
      spyOn(FormatValidator, 'isBoolean').and.returnValue({ success: true, message: '' });
      const result = boolConstant.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check', () => {
      const negVal = new BoolConstant(true);
      spyOn(FormatValidator, 'isBoolean').and.returnValue({ success: false, message: 'Should be "true" or "false".' });
      const result = negVal.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Should be "true" or "false".']);
    });

    it('should return the correct value with the evaluate function', () => {
      const boolConstant = new BoolConstant(true);
      expect(boolConstant.evaluate().applicable).toBeTruthy();
      expect(boolConstant.evaluate().value).toBe(true);
    });
  });

  describe('with LoadingVariable', () => {

    it('should construct a column variable successfully', () => {
      const loadingVar = new LoadingVariable(0);
      expect(loadingVar.value).toBe(0);
      expect(loadingVar.valueType).toBe(DataType.String);
    });

    it('should get type "String" with getType function', () => {
      const loadingVar = new LoadingVariable(0);
      expect(loadingVar.getType()).toBe(DataType.String);
    });

    it('should return the correct string with toString function', () => {
      const loadingVar = new LoadingVariable(0);
      expect(loadingVar.toString()).toBe(`$${loadingVar.value}`);
    });

    it('should return the correct JSON content with toJson function', () => {
      const loadingVar = new LoadingVariable(0);
      const json = loadingVar.toJson();
      expect(json.type).toBe('LoadingVariable');
      expect(json.value).toBe(loadingVar.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const loadingVar = new LoadingVariable(0);
      spyOn(FormatValidator, 'isUInt').and.returnValue({ success: true, message: '' });
      const result = loadingVar.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check', () => {
      const negVal = new LoadingVariable(-1);
      spyOn(FormatValidator, 'isUInt').and.returnValue({ success: false, message: 'Should be non-negative integer.' });
      const result = negVal.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Should be non-negative integer.']);
    });

    it('should return the correct value with the evaluate function', () => {
      const loadingVar = new LoadingVariable(0);
      expect(loadingVar.evaluate().applicable).toBeFalsy();
      expect(loadingVar.evaluate().value).toBeUndefined();
    });
  });

  describe('with AttrVariable', () => {

    it('should construct a attribute name variable successfully', () => {
      const attrVar = new AttrVariable('id', DataType.Int);
      expect(attrVar.value).toBe('id');
      expect(attrVar.valueType).toBe(DataType.Int);
    });

    it('should get type of the attribute value with getType function', () => {
      const attrVar = new AttrVariable('id', DataType.Int);
      expect(attrVar.getType()).toBe(attrVar.valueType);
    });

    it('should return the correct string with toString function', () => {
      const attrVar = new AttrVariable('id', DataType.Int);
      expect(attrVar.toString()).toBe('id');
    });

    it('should return the correct JSON content with toJson function', () => {
      const attrVar = new AttrVariable('id', DataType.Int);
      const json = attrVar.toJson();
      expect(json.value).toBe(attrVar.value);
      expect(json.type).toBe('AttrVariable');
    });

    it('should pass the semantic check', () => {
      const attrVar = new AttrVariable('id', DataType.Int);
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true, message: '' });
      const result = attrVar.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should return the correct value with the evaluate function', () => {
      const attrVar = new AttrVariable('name', DataType.String);
      const graphNodes = {
        exType: 'vType',
        attrs: {
          'name': 'abc'
        }
      };
      expect(attrVar.evaluate(<any>graphNodes).applicable).toBeTruthy();
      expect(attrVar.evaluate(<any>graphNodes).value).toBe('abc');
    });

    it('should not return the value with the evaluate function with complex variable', () => {
      const attrVar = new AttrVariable('name', DataType.Unknown);
      const graphNodes = {
        exType: 'vType',
        attrs: {
          'name': ['a', 'b', 'c']
        }
      };
      expect(attrVar.evaluate(<any>graphNodes).applicable).toBeFalsy();
      expect(attrVar.evaluate(<any>graphNodes).value).toBeUndefined();
    });
  });

  describe('with IntAccum', () => {

    it('should construct an integer type accumulator with an integer value successfully', () => {
      const intAccum = new IntAccum('@intAccum');
      expect(intAccum.value).toBe('@intAccum');
      expect(intAccum.valueType).toBe(DataType.Int);
    });

    it('should get type "Integer" with getType function', () => {
      const intAccum = new IntAccum('@intAccum');
      expect(intAccum.getType()).toBe(DataType.Int);
    });

    it('should return the correct string with toString function', () => {
      const intAccum = new IntAccum('@intAccum');
      expect(intAccum.toString()).toBe('@intAccum');
    });

    it('should return the correct JSON content with toJson function', () => {
      const intAccum = new IntAccum('@intAccum');
      const json = intAccum.toJson();
      expect(json.type).toBe('IntAccum');
      expect(json.value).toBe(intAccum.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const intAccum = new IntAccum('@intAccum');
      spyOn(FormatValidator, 'isInt').and.returnValue({ success: true, message: '' });
      const result = intAccum.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check when trying to construct an accumulator with an invalid name', () => {
      const wrongAccum = new IntAccum('intAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: false, message: 'Valid accumulator should start with one "@".' });
      const result = wrongAccum.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Valid accumulator should start with one "@".']);
    });

    it('should return the correct value with the evaluate function', () => {
      const intAccum = new IntAccum('@intAccum');
      const graphNodes = {
        attrs: {
          '@intAccum': 1
        }
      };
      expect(intAccum.evaluate(<any>graphNodes).applicable).toBeTruthy();
      expect(intAccum.evaluate(<any>graphNodes).value).toBe(1);
    });

    it('should not return value with the evaluate function with wrong value type', () => {
      const intAccum = new IntAccum('@intAccum');
      const graphNodes = {
        attrs: {
          '@intAccum': '1'
        }
      };
      expect(intAccum.evaluate(<any>graphNodes).applicable).toBeFalsy();
      expect(intAccum.evaluate(<any>graphNodes).value).toBeUndefined();
    });
  });

  describe('with RealAccum', () => {

    it('should construct a real type accumulator with a real value successfully', () => {
      const realAccum = new RealAccum('@realAccum');
      expect(realAccum.value).toBe('@realAccum');
      expect(realAccum.valueType).toBe(DataType.Real);
    });

    it('should get type "Real" with getType function', () => {
      const realAccum = new RealAccum('@realAccum');
      expect(realAccum.getType()).toBe(DataType.Real);
    });

    it('should return the correct string with toString function', () => {
      const realAccum = new RealAccum('@realAccum');
      expect(realAccum.toString()).toBe('@realAccum');
    });

    it('should return the correct JSON content with toJson function', () => {
      const realAccum = new RealAccum('@realAccum');
      const json = realAccum.toJson();
      expect(json.type).toBe('RealAccum');
      expect(json.value).toBe(realAccum.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const realAccum = new RealAccum('@realAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: true, message: '' });
      const result = realAccum.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check when trying to construct an accumulator with an invalid name', () => {
      const wrongAccum = new RealAccum('intAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: false, message: 'Valid accumulator should start with one "@".' });
      const result = wrongAccum.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Valid accumulator should start with one "@".']);
    });

    it('should return the correct value with the evaluate function', () => {
      const realAccum = new RealAccum('@realAccum');
      const graphNodes = {
        attrs: {
          '@realAccum': 1.1
        }
      };
      expect(realAccum.evaluate(<any>graphNodes).applicable).toBeTruthy();
      expect(realAccum.evaluate(<any>graphNodes).value).toBe(1.1);
    });

    it('should not return value with the evaluate function with wrong value type', () => {
      const realAccum = new RealAccum('@realAccum');
      const graphNodes = {
        attrs: {
          '@realAccum': '1'
        }
      };
      expect(realAccum.evaluate(<any>graphNodes).applicable).toBeFalsy();
      expect(realAccum.evaluate(<any>graphNodes).value).toBeUndefined();
    });
  });

  describe('with StringAccum', () => {

    it('should construct a string type accumulator with a string value successfully', () => {
      const stringAccum = new StringAccum('@stringAccum');
      expect(stringAccum.value).toBe('@stringAccum');
      expect(stringAccum.valueType).toBe(DataType.String);
    });

    it('should get type "String" with getType function', () => {
      const stringAccum = new StringAccum('@stringAccum');
      expect(stringAccum.getType()).toBe(DataType.String);
    });

    it('should return the correct string with toString function', () => {
      const stringAccum = new StringAccum('@stringAccum');
      expect(stringAccum.toString()).toBe('@stringAccum');
    });

    it('should return the correct JSON content with toJson function', () => {
      const stringAccum = new StringAccum('@stringAccum');
      const json = stringAccum.toJson();
      expect(json.type).toBe('StringAccum');
      expect(json.value).toBe(stringAccum.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const stringAccum = new StringAccum('@stringAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: true, message: '' });
      const result = stringAccum.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check when trying to construct an accumulator with an invalid name', () => {
      const wrongAccum = new StringAccum('stringAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: false, message: 'Valid accumulator should start with one "@".' });
      const result = wrongAccum.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Valid accumulator should start with one "@".']);
    });

    it('should return the correct value with the evaluate function', () => {
      const stringAccum = new StringAccum('@stringAccum');
      const graphNodes = {
        attrs: {
          '@stringAccum': 'abc'
        }
      };
      expect(stringAccum.evaluate(<any>graphNodes).applicable).toBeTruthy();
      expect(stringAccum.evaluate(<any>graphNodes).value).toBe('abc');
    });

    it('should not return value with the evaluate function with wrong value type', () => {
      const stringAccum = new StringAccum('@stringAccum');
      const graphNodes = {
        attrs: {
          '@stringAccum': 1
        }
      };
      expect(stringAccum.evaluate(<any>graphNodes).applicable).toBeFalsy();
      expect(stringAccum.evaluate(<any>graphNodes).value).toBeUndefined();
    });
  });

  describe('with BoolAccum', () => {

    it('should construct a boolean type accumulator with a boolean value successfully', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      expect(boolAccum.value).toBe('@boolAccum');
      expect(boolAccum.valueType).toBe(DataType.Bool);
    });

    it('should get type "Boolean" with getType function', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      expect(boolAccum.getType()).toBe(DataType.Bool);
    });

    it('should return the correct string with toString function', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      expect(boolAccum.toString()).toBe('@boolAccum');
    });

    it('should return the correct JSON content with toJson function', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      const json = boolAccum.toJson();
      expect(json.type).toBe('BoolAccum');
      expect(json.value).toBe(boolAccum.value);
    });

    it('should pass the semantic check with semanticCheck function', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: true, message: '' });
      const result = boolAccum.semanticCheck();
      expect(result.success).toBeTruthy();
      expect(result.errors).toEqual([]);
    });

    it('should fail in semantic check when trying to construct an accumulator with an invalid name', () => {
      const wrongAccum = new BoolAccum('boolAccum');
      spyOn(FormatValidator, 'isAccum').and.returnValue({ success: false, message: 'Valid accumulator should start with one "@".' });
      const result = wrongAccum.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.errors).toEqual(['Valid accumulator should start with one "@".']);
    });

    it('should return the correct value with the evaluate function', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      const graphNodes = {
        attrs: {
          '@boolAccum': true
        }
      };
      expect(boolAccum.evaluate(<any>graphNodes).applicable).toBeTruthy();
      expect(boolAccum.evaluate(<any>graphNodes).value).toBe(true);
    });

    it('should not return value with the evaluate function with wrong value type', () => {
      const boolAccum = new BoolAccum('@boolAccum');
      const graphNodes = {
        attrs: {
          '@boolAccum': '1'
        }
      };
      expect(boolAccum.evaluate(<any>graphNodes).applicable).toBeFalsy();
      expect(boolAccum.evaluate(<any>graphNodes).value).toBeUndefined();
    });
  });
});
