import { DataType } from '../data';

import { <PERSON><PERSON>, Atom<PERSON>son } from './atom.model';
import {
  ArithmeticExpression, ArithmeticExpressionInWhereClause,
  BaseExpression, ExpressionJson, FunctionExpression, NullExpression
} from './expression.model';
import { ExpressionValidateResult } from './expression-validator';
import { ArithmeticOperator, FunctionType } from './operators.enum';

class MockAtom extends Atom {
  value: any = '1';
  valueType = DataType.String;
  className = 'StringConstant';
  sCheck = true;

  getType(): DataType {
    return this.valueType;
  }

  toString(): string {
    if (this.valueType === DataType.String) {
      return `"${this.value}"`;
    }
    return `${this.value}`;
  }

  toJson(): AtomJson {
    return {
      type: this.className,
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.sCheck,
      errors: []
    };
  }

  evaluate(nodeOrLink: any): {
    applicable: boolean,
    value?: boolean | number | string
  } {
    return {
      applicable: true,
      value: this.value
    };
  }
}

class MockFunctionExpression extends BaseExpression {
  operandsTypes = [];
  type = DataType.Real;
  sCheck = true;

  constructor() {
    super([new MockAtom()], FunctionType.ToFloat);
  }

  getType(): DataType {
    return this.type;
  }

  toString(): string {
    return `${this.operator}(${this.operands})`;
  }

  toJson(): ExpressionJson {
    return {
      type: 'FunctionExpression',
      operator: this.operator,
      operands: [{
        type: 'StringConstant',
        value: '1'
      }]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.sCheck,
      errors: []
    };
  }

  evaluate(nodeOrLink: any): {
    applicable: boolean,
    value?: boolean | number | string
  } {
    return {
      applicable: false
    };
  }
}

describe('ExpressionModel', () => {

  const nodes = [];

  describe('with NullExpression', () => {

    it('should construct a null expression to handle undefined', () => {
      const nullExpr = new NullExpression();
      expect(nullExpr.operands).toBeUndefined();
      expect(nullExpr.operator).toBeUndefined();
    });

    it('should test getType function', () => {
      const nullExpr = new NullExpression();
      expect(nullExpr.getType()).toBe(DataType.Unknown);
    });

    it('should test toString function', () => {
      const nullExpr = new NullExpression();
      expect(nullExpr.toString()).toEqual('');
    });

    it('should test toJson function', () => {
      const nullExpr = new NullExpression();
      expect(nullExpr.toJson()).toEqual({
        type: 'NullExpression',
        operands: []
      });
    });

    it('should test semanticCheck function', () => {
      const nullExpr = new NullExpression();
      const check = nullExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Please choose expression type.']);
    });

    it('should test evaluate function', () => {
      const nullExpr = new NullExpression();
      const evalResult = nullExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeFalsy();
      expect(evalResult.value).toBeUndefined();
    });
  });

  describe('with FunctionExpression', () => {

    it('should construct a function expression with an atom successfully', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      expect(funcExpr.operands).toEqual([atom]);
      expect(funcExpr.operator).toBe(FunctionType.ToInt);
      expect(funcExpr.operandsTypes).toBeDefined();
    });

    it('should construct a function expression with an expression successfully', () => {
      const expr = new MockFunctionExpression();
      const funcExpr = new FunctionExpression([expr], FunctionType.ToInt);
      expect(funcExpr.operands).toEqual([expr]);
      expect(funcExpr.operator).toBe(FunctionType.ToInt);
      expect(funcExpr.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      expect(funcExpr.getType()).toBe(DataType.Int);
    });

    it('should test toString function', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      expect(funcExpr.toString()).toBe('to_int("1")');
    });

    it('should test toJson function', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      const json = funcExpr.toJson();
      expect(json.type).toBe('FunctionExpression');
      expect(json.operator).toBe(FunctionType.ToInt);
      expect(json.operands).toEqual([atom.toJson()]);
    });

    it('should pass semantic check', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      const check = funcExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should fail in semantic check since the operands has semantic error', () => {
      const atom = new MockAtom();
      atom.sCheck = false;
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      const check = funcExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual([]);
    });

    it('should fail in semantic check since the number of operands does not match the number of parameters the function requires', () => {
      const atom = new MockAtom();
      const funcExpr = new FunctionExpression([atom, atom], FunctionType.ToInt);
      const check = funcExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Expect 1 argument(s), but get 2.']);
    });

    it('should pass semantic check since int type operand is compatible if real type parameter is required', () => {
      const atom = new MockAtom();
      atom.valueType = DataType.Int;
      const funcExpr = new FunctionExpression([atom], FunctionType.Sqrt);
      const check = funcExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should fail in semantic check since the operand type is incompatible to the parameter type', () => {
      const atom = new MockAtom();
      atom.valueType = DataType.Real;
      const funcExpr = new FunctionExpression([atom], FunctionType.ToInt);
      const check = funcExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Expression1 should be String, not Real.']);
    });

    it('should test evaluate function', () => {
      const nullExpr = new NullExpression();
      const evalResult = nullExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeFalsy();
      expect(evalResult.value).toBeUndefined();
    });
  });

  describe('with ArithmeticExpression', () => {

    const atom = new MockAtom();
    atom.value = 1;
    atom.valueType = DataType.Int;
    atom.className = 'IntConstant';

    it('should construct an arithmetic expression with 2 atoms successfully', () => {
      const arithExpr = new ArithmeticExpression([atom, atom], ArithmeticOperator.Plus);
      expect(arithExpr.operands).toEqual([atom, atom]);
      expect(arithExpr.operator).toBe(ArithmeticOperator.Plus);
      expect(arithExpr.operandsTypes).toBeDefined();
    });

    it('should construct an arithmetic expression with 2 expression successfully', () => {
      const expr = new MockFunctionExpression();
      const arithExpr = new ArithmeticExpression([expr, expr], ArithmeticOperator.Plus);
      expect(arithExpr.operands).toEqual([expr, expr]);
      expect(arithExpr.operator).toBe(ArithmeticOperator.Plus);
      expect(arithExpr.operandsTypes).toBeDefined();
    });

    it('should construct an arithmetic expression with 1 atom and one expression successfully', () => {
      const expr = new MockFunctionExpression();
      const arithExpr = new ArithmeticExpression([atom, expr], ArithmeticOperator.Plus);
      expect(arithExpr.operands).toEqual([atom, expr]);
      expect(arithExpr.operator).toBe(ArithmeticOperator.Plus);
      expect(arithExpr.operandsTypes).toBeDefined();
    });

    it('should test getType function with same type operands', () => {
      const arithExpr = new ArithmeticExpression([atom, atom], ArithmeticOperator.Plus);
      expect(arithExpr.getType()).toBe(DataType.Int);
    });

    it('should test getType function with different type operands', () => {
      const realAtom = new MockAtom();
      realAtom.value = 1.1;
      realAtom.valueType = DataType.Real;
      realAtom.className = 'RealConstant';
      const arithExpr = new ArithmeticExpression([atom, realAtom], ArithmeticOperator.Divide);
      expect(arithExpr.getType()).toBe(DataType.Real);
    });

    it('should test toString function', () => {
      const arithExpr = new ArithmeticExpression([atom, atom], ArithmeticOperator.Plus);
      expect(arithExpr.toString()).toBe('(1 + 1)');
    });

    it('should test toJson function', () => {
      const arithExpr = new ArithmeticExpression([atom, atom], ArithmeticOperator.Plus);
      const json = arithExpr.toJson();
      expect(json.type).toBe('ArithmeticExpression');
      expect(json.operator).toBe(ArithmeticOperator.Plus);
      expect(json.operands).toEqual([atom.toJson(), atom.toJson()]);
    });

    it('should pass the semantic check', () => {
      const arithExpr = new ArithmeticExpression([atom, atom], ArithmeticOperator.Plus);
      const check = arithExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should fail the semantic check since the operand has semantic error', () => {
      const negAtom = new MockAtom();
      negAtom.sCheck = false;
      const negExpr = new ArithmeticExpression([atom, negAtom], ArithmeticOperator.Plus);
      const check = negExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual([]);
    });

    it('should pass the semantic check when types of 2 operands are different, but both belong to numeric', () => {
      const realAtom = new MockAtom();
      realAtom.valueType = DataType.Real;
      const arithExpr = new ArithmeticExpression([atom, realAtom], ArithmeticOperator.Minus);
      const check = arithExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should pass the semantic check when 2 string concat with + operator', () => {
      const a = new MockAtom();
      const arithExpr = new ArithmeticExpression([a, a], ArithmeticOperator.Plus);
      const check = arithExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should fail the semantic check since the operands cannot be operated', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      let negExpr = new ArithmeticExpression([atom, boolAtom], ArithmeticOperator.Minus);
      let check = negExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Integer and Boolean are incompatible for operator "-".']);

      const realAtom = new MockAtom();
      boolAtom.valueType = DataType.Real;
      negExpr = new ArithmeticExpression([atom, boolAtom], ArithmeticOperator.Mod);
      check = negExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Both operands must be integers for operator "%".']);
    });

    it('should test evaluate function with "+" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 1;
      b.valueType = DataType.String;
      b.value = '1';
      const arithExpr1 = new ArithmeticExpression([a, a], ArithmeticOperator.Plus);
      const evalResult1 = arithExpr1.evaluate(<any>nodes);
      const arithExpr2 = new ArithmeticExpression([b, b], ArithmeticOperator.Plus);
      const evalResult2 = arithExpr2.evaluate(<any>nodes);
      expect(evalResult1.applicable).toBeTruthy();
      expect(evalResult1.value).toBe(2);
      expect(evalResult2.applicable).toBeTruthy();
      expect(evalResult2.value).toBe('11');
    });

    it('should test evaluate function with "-" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 2;
      b.valueType = DataType.Int;
      b.value = 1;
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Minus);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeTruthy();
      expect(evalResult.value).toBe(1);
    });

    it('should test evaluate function with "*" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 2;
      b.valueType = DataType.Int;
      b.value = 3;
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Multiply);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeTruthy();
      expect(evalResult.value).toBe(6);
    });

    it('should test evaluate function with "/" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 1;
      b.valueType = DataType.Int;
      b.value = 0;
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Divide);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeFalsy();
      expect(evalResult.value).toBeUndefined();
    });

    it('should test evaluate function with "%" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 2;
      b.valueType = DataType.Int;
      b.value = 3;
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Mod);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeTruthy();
      expect(evalResult.value).toBe(2);
    });

    it('should test evaluate function with "^" operator', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 2;
      b.valueType = DataType.Int;
      b.value = 3;
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Power);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeTruthy();
      expect(evalResult.value).toBe(8);
    });

    it('should test evaluate function with non-applicable operands', () => {
      const a = new MockAtom();
      const b = new MockAtom();
      a.valueType = DataType.Int;
      a.value = 2;
      spyOn(b, 'evaluate').and.returnValue({
        applicable: false
      });
      const arithExpr = new ArithmeticExpression([a, b], ArithmeticOperator.Power);
      const evalResult = arithExpr.evaluate(<any>nodes);
      expect(evalResult.applicable).toBeFalsy();
      expect(evalResult.value).toBeUndefined();
    });
  });

  describe('with ArithmeticExpressionInWhereClause', () => {

    const atom = new MockAtom();
    const intAtom = new MockAtom();
    intAtom.valueType = DataType.Int;

    it('should fail in semantic check directly error occurs in super semantic check', () => {
      const negExpr = new ArithmeticExpressionInWhereClause([atom, atom], ArithmeticOperator.Divide);
      const check = negExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['String and String are incompatible for operator "/".']);
    });

    it('should pass the semantic check if 2 operands are numeric type', () => {
      const arithExpr = new ArithmeticExpressionInWhereClause([intAtom, intAtom], ArithmeticOperator.Plus);
      const check = arithExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });

    it('should fail in semantic check if 2 operands are not numeric type when using + operator', () => {
      const negExpr = new ArithmeticExpressionInWhereClause([atom, atom], ArithmeticOperator.Plus);
      const check = negExpr.semanticCheck();
      expect(check.success).toBeFalsy();
      expect(check.errors).toEqual(['Only numeric type operands can be used with + operator in WHERE clause.']);
    });

    it('should pass the semantic check when it is not + operation', () => {
      const arithExpr = new ArithmeticExpressionInWhereClause([intAtom, intAtom], ArithmeticOperator.Divide);
      const check = arithExpr.semanticCheck();
      expect(check.success).toBeTruthy();
      expect(check.errors).toEqual([]);
    });
  });
});
