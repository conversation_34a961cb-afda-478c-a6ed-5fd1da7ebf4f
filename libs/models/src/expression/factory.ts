import { DataType } from '../data';

import {
  Atom, AttrVariable, BoolAccum, BoolConstant, BoolParam,
  IntAccum, IntConstant, IntParam, LoadingVariable, RealAccum,
  RealConstant, RealParam, StringAccum, StringConstant, StringParam,
  DatetimeParam, AttrVariableWithAlias, AliasReference, DatetimeConstant
} from './atom.model';
import {
  AndCondition, BaseCondition, BetweenAndCondition,
  ComparisonCondition, InCondition, IsCondition,
  NotCondition, NullCondition, OrCondition
} from './condition.model';
import {
  ArithmeticOperator, FunctionType,
  PredicateOperator, RelationalOperator
} from './operators.enum';
import {
  ArithmeticExpression, ArithmeticExpressionInWhereClause,
  ArithmeticExpressionInAggregationFunction,
  BaseExpression, FunctionExpression, NullExpression
} from './expression.model';

export abstract class BaseFactory { }

/**
 * Class for atom factory.
 *
 * @export
 * @class AtomFactory
 * @extends {BaseFactory}
 */
export class AtomFactory extends BaseFactory {
  getAtom<A extends Atom>(
    a: new (value?: number | string | boolean, valueType?: DataType) => A,
    value?: number | string | boolean,
    valueType?: DataType
  ): A {
    return new a(value, valueType);
  }
}

/**
 * Class for expression factory.
 *
 * @export
 * @class ExpressionFactory
 * @extends {BaseFactory}
 */
export class ExpressionFactory extends BaseFactory {
  getExpression<E extends BaseExpression>(
    e: new (
      operands: Array<Atom | BaseExpression>,
      operator?: ArithmeticOperator | FunctionType
    ) => E,
    operands: Array<Atom | BaseExpression>,
    operator?: ArithmeticOperator | FunctionType
  ): E {
    return new e(operands, operator);
  }
}

/**
 * Class for condition factory.
 *
 * @export
 * @class ConditionFactory
 * @extends {BaseFactory}
 */
export class ConditionFactory extends BaseFactory {
  getCondition<C extends BaseCondition>(
    c: new (
      operands: Array<Atom | BaseExpression | BaseCondition>,
      operator?: RelationalOperator | PredicateOperator
    ) => C,
    operands: Array<Atom | BaseExpression | BaseCondition>,
    operator?: RelationalOperator | PredicateOperator
  ): C {
    return new c(operands, operator);
  }
}

export const atomDict = {
  IntConstant: IntConstant,
  RealConstant: RealConstant,
  StringConstant: StringConstant,
  BoolConstant: BoolConstant,
  DatetimeConstant: DatetimeConstant,
  IntParam: IntParam,
  RealParam: RealParam,
  StringParam: StringParam,
  DatetimeParam: DatetimeParam,
  BoolParam: BoolParam,
  IntAccum: IntAccum,
  RealAccum: RealAccum,
  StringAccum: StringAccum,
  BoolAccum: BoolAccum,
  AttrVariable: AttrVariable,
  AttrVariableWithAlias: AttrVariableWithAlias,
  LoadingVariable: LoadingVariable,
  AliasReference: AliasReference,
};

export const exprDict = {
  NullExpression: NullExpression,
  FunctionExpression: FunctionExpression,
  ArithmeticExpression: ArithmeticExpression,
  ArithmeticExpressionInWhereClause: ArithmeticExpressionInWhereClause,
  ArithmeticExpressionInAggregationFunction: ArithmeticExpressionInAggregationFunction,
};

export const condDict = {
  NullCondition: NullCondition,
  ComparisonCondition: ComparisonCondition,
  IsCondition: IsCondition,
  InCondition: InCondition,
  BetweenAndCondition: BetweenAndCondition,
  NotCondition: NotCondition,
  AndCondition: AndCondition,
  OrCondition: OrCondition
};

export namespace FactoryProducer {
  const factories = new Map<typeof BaseFactory, BaseFactory>([
    [AtomFactory, new AtomFactory()],
    [ConditionFactory, new ConditionFactory()],
    [ExpressionFactory, new ExpressionFactory()]
  ]);

  /**
   * Retrieve a factory from its class name.
   *
   * @export
   * @template F
   * @param {new () => F} f
   * @returns {F}
   */
  export function getFactory<F extends BaseFactory>(f: new () => F): F {
    return <F>factories.get(f);
  }
}
