import {
  ArithmeticOperator, FunctionType, PredicateOperator,
  RelationalOperator
} from '.';

export const defaultCondList: string[] = [
  'NullCondition',
  ...Object.values(RelationalOperator),
  ...Object.values(PredicateOperator)
];

export const defaultExprList: string[] = [
  'NullExpression',
  'Function',
  'IntConstant',
  'RealConstant',
  'StringConstant',
  'BoolConstant',
  'DatetimeConstant',
  'IntParam',
  'RealParam',
  'BoolParam',
  'StringParam',
  'DatetimeParam',
  'IntAccum',
  'RealAccum',
  'StringAccum',
  'BoolAccum',
  'LoadingVariable',
  'AliasReference',
  'AttrVariable',
  'AttrVariableWithAlias',
  ...Object.values(ArithmeticOperator)
];

export const defaultFuncList: FunctionType[] = Object.values(FunctionType);

export const defaultOptList: string[] = [
  'NullExpression',
  'Function',
  'IntConstant',
  'RealConstant',
  'StringConstant',
  'BoolConstant',
  'DatetimeConstant',
  'IntParam',
  'RealParam',
  'BoolParam',
  'StringParam',
  'DatetimeParam',
  'IntAccum',
  'RealAccum',
  'StringAccum',
  'AliasReference',
  'BoolAccum',
  'LoadingVariable',
  'AttrVariable',
  'AttrVariableWithAlias',
  ...Object.values(ArithmeticOperator)
];
