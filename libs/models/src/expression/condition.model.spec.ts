import { DataType } from '../data';

import { <PERSON>om, Atom<PERSON>son } from './atom.model';
import { BaseExpression, ExpressionJson } from './expression.model';
import { ExpressionValidateResult } from './expression-validator';
import { FunctionType, PredicateOperator, RelationalOperator } from './operators.enum';
import {
  AndCondition, BaseCondition, BetweenAndCondition,
  ComparisonCondition, ConditionJson, InCondition,
  IsCondition, NullCondition, NotCondition, OrCondition
} from './condition.model';

class Mock<PERSON>tom extends Atom {
  value: any = 0;
  valueType = DataType.String;
  className = 'LoadingVariable';
  sCheck = true;
  errorMsg = '';

  getType(): DataType {
    return this.valueType;
  }

  toString(): string {
    if (this.className === 'LoadingVariable') {
      return `$${this.value}`;
    }
    if (this.valueType === DataType.String) {
      return `"${this.value}"`;
    }
    return `${this.value}`;
  }

  to<PERSON><PERSON>(): AtomJson {
    return {
      type: this.className,
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const arr = [];
    if (!this.sCheck) {
      arr.push(this.errorMsg);
    }
    return {
      success: this.sCheck,
      errors: arr
    };
  }

  evaluate(nodeOrLink: any): {
    applicable: boolean,
    value?: boolean
  } {
    return {
      applicable: true,
      value: this.value
    };
  }
}

class MockFunctionExpression extends BaseExpression {
  operandsTypes = [];
  type = DataType.Real;
  sCheck = true;

  constructor() {
    super([new MockAtom()], FunctionType.ToFloat);
  }

  getType(): DataType {
    return this.type;
  }

  toString(): string {
    return `${this.operator}(${this.operands})`;
  }

  toJson(): ExpressionJson {
    return {
      type: 'FunctionExpression',
      operator: this.operator,
      operands: [this.operands[0].toJson()]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.sCheck,
      errors: []
    };
  }

  evaluate(nodeOrLink: any): {
    applicable: boolean,
    value?: boolean
  } {
    return {
      applicable: false
    };
  }
}

class MockIsCondition extends BaseCondition {
  operandsTypes = [];
  sCheck = true;

  constructor() {
    super([new MockAtom()], PredicateOperator.IsNumeric);
  }

  getType(): DataType {
    return DataType.Bool;
  }

  toString(): string {
    return `(${this.operands[0]} ${this.operator})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'IsCondition',
      operator: this.operator,
      operands: [this.operands[0].toJson()]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.sCheck,
      errors: []
    };
  }

  evaluate(nodeOrLink: any): {
    applicable: boolean,
    value?: boolean
  } {
    return {
      applicable: false
    };
  }
}

describe('ConditionModel', () => {

  const nodes = [];

  describe('with NullCondition', () => {

    it('should construct a null condition successully to handle undefined', () => {
      const nullCond = new NullCondition();
      expect(nullCond.operands).toBeUndefined();
      expect(nullCond.operator).toBeUndefined();
    });

    it('should test getType function', () => {
      const nullCond = new NullCondition();
      expect(nullCond.getType()).toBe(DataType.Unknown);
    });

    it('should test toString function', () => {
      const nullCond = new NullCondition();
      expect(nullCond.toString()).toEqual('');
    });

    it('should test toJson function', () => {
      const nullCond = new NullCondition();
      expect(nullCond.toJson()).toEqual({
        type: 'NullCondition',
        operands: []
      });
    });

    it('should test semanticCheck function', () => {
      const nullCond = new NullCondition();
      expect(nullCond.semanticCheck().success).toBeFalsy();
      expect(nullCond.semanticCheck().errors).toEqual(['Please choose condition type.']);
    });

    it('should test evaluate function', () => {
      const nullCond = new NullCondition();
      expect(nullCond.evaluate(<any>nodes).applicable).toBeFalsy();
      expect(nullCond.evaluate(<any>nodes).value).toBeUndefined();
    });
  });

  describe('with ComparisonCondition', () => {

    const atom = new MockAtom();
    const expr = new MockFunctionExpression();

    it('should construct a comparison condition with 2 atoms successfully', () => {
      const cpCond = new ComparisonCondition([atom, atom], RelationalOperator.Equal);
      expect(cpCond.operands).toEqual([atom, atom]);
      expect(cpCond.operator).toBe(RelationalOperator.Equal);
      expect(cpCond.operandsTypes).toBeDefined();
    });

    it('should construct a comparison condition with 2 expressions successfully', () => {
      const cpCond = new ComparisonCondition([expr, expr], RelationalOperator.Equal);
      expect(cpCond.operands).toEqual([expr, expr]);
      expect(cpCond.operator).toBe(RelationalOperator.Equal);
      expect(cpCond.operandsTypes).toBeDefined();
    });

    it('should construct a comparison condition with an atom and an expression successfully', () => {
      const cpCond = new ComparisonCondition([atom, expr], RelationalOperator.Equal);
      expect(cpCond.operands).toEqual([atom, expr]);
      expect(cpCond.operator).toBe(RelationalOperator.Equal);
      expect(cpCond.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const cpCond = new ComparisonCondition([atom, atom], RelationalOperator.Equal);
      expect(cpCond.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      const cpCond = new ComparisonCondition([atom, atom], RelationalOperator.Equal);
      expect(cpCond.toString()).toBe('($0 == $0)');
    });

    it('should test toJson function', () => {
      const cpCond = new ComparisonCondition([atom, atom], RelationalOperator.Equal);
      const json = cpCond.toJson();
      expect(json.type).toBe('ComparisonCondition');
      expect(json.operator).toBe(RelationalOperator.Equal);
      expect(json.operands[0]).toEqual(atom.toJson());
      expect(json.operands[1]).toEqual(atom.toJson());
    });

    it('should pass the pass the semantic check', () => {
      const cpCond = new ComparisonCondition([atom, atom], RelationalOperator.Equal);
      expect(cpCond.semanticCheck().success).toBeTruthy();
      expect(cpCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand has semantic error', () => {
      const negAtom = new MockAtom();
      negAtom.sCheck = false;
      const cpCond = new ComparisonCondition([negAtom, atom], RelationalOperator.Equal);
      expect(cpCond.semanticCheck().success).toBeFalsy();
      expect(cpCond.semanticCheck().errors).toEqual([]);
    });

    it('should pass semantic check if the operands are boolean', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      const cpCon = new ComparisonCondition([boolAtom, boolAtom], RelationalOperator.Equal);
      expect(cpCon.semanticCheck().success).toBeTruthy();
      expect(cpCon.semanticCheck().errors).toEqual([]);
    });

    it('should pass semantic check if types of operands are different but are both numeric', () => {
      const intAtom = new MockAtom();
      intAtom.valueType = DataType.Int;
      const cpCond = new ComparisonCondition([intAtom, expr], RelationalOperator.Larger);
      expect(cpCond.semanticCheck().success).toBeTruthy();
      expect(cpCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if types of operands are different and not numeric', () => {
      // one of them is not numeric
      const negCon = new ComparisonCondition([atom, expr], RelationalOperator.Equal);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Cannot compare String and Real.']);

      // both are not numeric
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      const failCon = new ComparisonCondition([atom, boolAtom], RelationalOperator.Equal);
      expect(failCon.semanticCheck().success).toBeFalsy();
      expect(failCon.semanticCheck().errors).toEqual(['Cannot compare String and Boolean.']);
    });

    it('should fail semantic check if the operator between two boolean is not "==" or "!="', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      const cpCond = new ComparisonCondition([boolAtom, boolAtom], RelationalOperator.Larger);
      expect(cpCond.semanticCheck().success).toBeFalsy();
      expect(cpCond.semanticCheck().errors).toEqual(['Cannot compare Boolean expressions with > operator.']);
    });

    it('should test evaluate function with operator "=="', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      boolAtom.value = true;
      const cpCond = new ComparisonCondition([boolAtom, boolAtom], RelationalOperator.Equal);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeTruthy();
    });

    it('should test evaluate function with operator "!="', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      boolAtom.value = true;
      const cpCond = new ComparisonCondition([boolAtom, boolAtom], RelationalOperator.NotEqual);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeFalsy();
    });

    it('should test evaluate function with operator ">"', () => {
      const intAtom = new MockAtom();
      intAtom.valueType = DataType.Int;
      intAtom.value = 1;
      const cpCond = new ComparisonCondition([intAtom, intAtom], RelationalOperator.Larger);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeFalsy();
    });

    it('should test evaluate function with operator ">="', () => {
      const intAtom = new MockAtom();
      intAtom.valueType = DataType.Int;
      intAtom.value = 1;
      const cpCond = new ComparisonCondition([intAtom, intAtom], RelationalOperator.LargerOrEqual);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeTruthy();
    });

    it('should test evaluate function with operator "<"', () => {
      const intAtom = new MockAtom();
      intAtom.valueType = DataType.Int;
      intAtom.value = 1;
      const cpCond = new ComparisonCondition([intAtom, intAtom], RelationalOperator.Smaller);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeFalsy();
    });

    it('should test evaluate function with operator "<="', () => {
      const intAtom = new MockAtom();
      intAtom.valueType = DataType.Int;
      intAtom.value = 1;
      const cpCond = new ComparisonCondition([intAtom, intAtom], RelationalOperator.SmallerOrEqual);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(cpCond.evaluate(<any>nodes).value).toBeTruthy();
    });

    it('should test evaluate function with non-applicable operands', () => {
      const intAtom = new MockAtom();
      spyOn(intAtom, 'evaluate').and.returnValue({
        applicable: false
      });
      const cpCond = new ComparisonCondition([intAtom, intAtom], RelationalOperator.SmallerOrEqual);
      expect(cpCond.evaluate(<any>nodes).applicable).toBeFalsy();
      expect(cpCond.evaluate(<any>nodes).value).toBeUndefined();
    });
  });

  describe('with IsCondition', () => {

    const atom = new MockAtom();

    it('should construct an is condition with an atom successfully', () => {
      const isCond = new IsCondition([atom], PredicateOperator.IsEmpty);
      expect(isCond.operands).toEqual([atom]);
      expect(isCond.operator).toBe(PredicateOperator.IsEmpty);
      expect(isCond.operandsTypes).toBeDefined();
    });

    it('should construct an is condition with an expression successfully', () => {
      const expr = new MockFunctionExpression();
      const isCond = new IsCondition([expr], PredicateOperator.IsNumeric);
      expect(isCond.operands).toEqual([expr]);
      expect(isCond.operator).toBe(PredicateOperator.IsNumeric);
      expect(isCond.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const isCond = new IsCondition([atom], PredicateOperator.IsEmpty);
      expect(isCond.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      const isCond = new IsCondition([atom], PredicateOperator.IsEmpty);
      expect(isCond.toString()).toBe('($0 IS EMPTY)');
    });

    it('should test toJson function', () => {
      const isCond = new IsCondition([atom], PredicateOperator.IsEmpty);
      const json = isCond.toJson();
      expect(json.type).toBe('IsCondition');
      expect(json.operator).toBe(PredicateOperator.IsEmpty);
      expect(json.operands[0]).toEqual(atom.toJson());
    });

    it('should pass the semantic check', () => {
      const isCond = new IsCondition([atom], PredicateOperator.IsNumeric);
      expect(isCond.semanticCheck().success).toBeTruthy();
      expect(isCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand has semantic error', () => {
      const negAtom = new MockAtom();
      negAtom.sCheck = false;
      const negCon = new IsCondition([negAtom], PredicateOperator.IsEmpty);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the type of operand is not string', () => {
      const negAtom = new MockAtom();
      negAtom.valueType = DataType.Int;
      const negCon = new IsCondition([negAtom], PredicateOperator.IsEmpty);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Only string type expression is allowed here.']);
    });
  });

  describe('with InCondition', () => {

    const atom = new MockAtom();
    const expr = new MockFunctionExpression();
    const strAtom = new MockAtom();
    strAtom.className = 'StringConstant';
    const intAtom = new MockAtom();
    intAtom.valueType = DataType.Int;
    intAtom.className = 'IntConstant';
    const strToFloatExpr = new MockFunctionExpression();
    strToFloatExpr.operands = [strAtom];

    it('should construct an in condition with an atom', () => {
      // right hand side are atoms
      const inCond = new InCondition([atom, strAtom, strAtom]);
      expect(inCond.operands).toEqual([atom, strAtom, strAtom]);
      expect(inCond.operator).toBe(PredicateOperator.In);
      expect(inCond.operandsTypes).toBeDefined();

      // right hand side are expressions
      const inExpr = new InCondition([atom, strToFloatExpr, expr]);
      expect(inExpr.operands).toEqual([atom, strToFloatExpr, expr]);
      expect(inExpr.operator).toBe(PredicateOperator.In);
      expect(inExpr.operandsTypes).toBeDefined();
    });

    it('should construct an in condition with an expression', () => {
      // right hand side are atoms
      const inCond = new InCondition([expr, intAtom, intAtom]);
      expect(inCond.operands).toEqual([expr, intAtom, intAtom]);
      expect(inCond.operator).toBe(PredicateOperator.In);

      // right hand side are expressions
      const inExpr = new InCondition([expr, expr, expr]);
      expect(inExpr.operands).toEqual([expr, expr, expr]);
      expect(inExpr.operator).toBe(PredicateOperator.In);
    });

    it('should test getType function', () => {
      const inCond = new InCondition([atom, strAtom]);
      expect(inCond.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      // left-hand side: atom, right-hand side: atom
      const inCondLeftAtomRightAtom = new InCondition([atom, strAtom]);
      expect(inCondLeftAtomRightAtom.toString()).toBe('($0 IN ("0"))');

      // left-hand side: expression, right-hand side: atom, expression
      const inCondLeftAtomRightExpr = new InCondition([expr, intAtom, strToFloatExpr]);
      expect(inCondLeftAtomRightExpr.toString()).toBe('(to_float($0) IN (0, to_float("0")))');
    });

    it('should test toJson function', () => {
      const inCond = new InCondition([atom, intAtom, strToFloatExpr]);
      const json = inCond.toJson();
      expect(json.type).toBe('InCondition');
      inCond.operands.forEach((operand, index) => {
        expect(json.operands[index]).toEqual(operand.toJson());
      });
    });

    it('should pass semantic check', () => {
      const inCond = new InCondition([atom, strAtom]);
      expect(inCond.semanticCheck().success).toBeTruthy();
      expect(inCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if one of the values in the set has semantic error', () => {
      const negAtom = new MockAtom();
      negAtom.sCheck = false;
      const negCon = new InCondition([atom, negAtom]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the left-handside is numeric type, but has other type value in the value set', () => {
      const negCon = new InCondition([expr, strAtom, intAtom]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Expression1 should be Numeric, not String.']);
    });

    it('should fail semantic check if the left-hand side is string type, but has other type value in the value set', () => {
      const negCon = new InCondition([atom, intAtom, strToFloatExpr]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors[0]).toEqual('Expression1 should be String, not Integer.');
      expect(negCon.semanticCheck().errors[1]).toEqual('Expression2 should be String, not Real.');
    });

    it('should fail semantic check if the left-hand sied is boolean type', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      const negCon = new InCondition([boolAtom, boolAtom]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Expression cannot be Boolean.']);
    });
  });

  describe('with BetweenAndCondition', () => {

    const atom = new MockAtom();
    const expr = new MockFunctionExpression();
    const strAtom = new MockAtom();
    strAtom.className = 'StringConstant';
    const intAtom = new MockAtom();
    intAtom.valueType = DataType.Int;
    intAtom.className = 'IntConstant';
    const strToFloatExpr = new MockFunctionExpression();
    strToFloatExpr.operands = [strAtom];

    it('should construct between-and condition with an atom', () => {
      const baCond = new BetweenAndCondition([atom, strAtom, strToFloatExpr]);
      expect(baCond.operands).toEqual([atom, strAtom, strToFloatExpr]);
      expect(baCond.operator).toBe(PredicateOperator.BetweenAnd);
      expect(baCond.operandsTypes).toBeDefined();
    });

    it('should construct between-and condition with an expression', () => {
      const baCond = new BetweenAndCondition([expr, intAtom, strToFloatExpr]);
      expect(baCond.operands).toEqual([expr, intAtom, strToFloatExpr]);
      expect(baCond.operator).toBe(PredicateOperator.BetweenAnd);
      expect(baCond.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const baCond = new BetweenAndCondition([atom, strAtom, strAtom]);
      expect(baCond.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      // left-hand side: atom, right-hand side: atom
      const baCondWithAtom = new BetweenAndCondition([atom, strAtom, strAtom]);
      expect(baCondWithAtom.toString()).toBe('($0 BETWEEN "0" AND "0")');

      // left-hand side: expression, right-hand side: atom | expression
      const baCondWithExpr = new BetweenAndCondition([expr, intAtom, strToFloatExpr]);
      expect(baCondWithExpr.toString()).toBe('(to_float($0) BETWEEN 0 AND to_float("0"))');
    });

    it('should test toJson function', () => {
      const baCond = new BetweenAndCondition([atom, strAtom, strAtom]);
      const json = baCond.toJson();
      expect(json.type).toBe('BetweenAndCondition');
      baCond.operands.forEach((operand, index) => {
        expect(json.operands[index]).toEqual(operand.toJson());
      });
    });

    it('should pass semantic check', () => {
      const baCond = new BetweenAndCondition([expr, intAtom, strToFloatExpr]);
      expect(baCond.semanticCheck().success).toBeTruthy();
      expect(baCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand itself has semantic error', () => {
      const negAtom = new MockAtom();
      negAtom.sCheck = false;
      const baCond = new BetweenAndCondition([expr, negAtom, strToFloatExpr]);
      expect(baCond.semanticCheck().success).toBeFalsy();
      expect(baCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the left-hand side is numeric type, but right-hand side has other type', () => {
      const negCon = new BetweenAndCondition([expr, strAtom, strToFloatExpr]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Expression1 should be Numeric, not String.']);
    });

    it('should fail semantic check if the left-hand side is string type, but right-hand side has other type', () => {
      const negCon = new BetweenAndCondition([atom, strAtom, strToFloatExpr]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Expression2 should be String, not Real.']);
    });

    it('should fail semantic check if boolean type is used', () => {
      const boolAtom = new MockAtom();
      boolAtom.valueType = DataType.Bool;
      const negCon = new BetweenAndCondition([boolAtom, intAtom, strToFloatExpr]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual(['Expression cannot be Boolean.']);
    });
  });

  describe('with NotCondition', () => {

    const condition = new MockIsCondition();

    it('should construct a not condition successfully', () => {
      const notCond = new NotCondition([condition]);
      expect(notCond.operands).toEqual([condition]);
      expect(notCond.operator).toBe(PredicateOperator.Not);
      expect(notCond.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const notCond = new NotCondition([condition]);
      expect(notCond.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      const notCond = new NotCondition([condition]);
      expect(notCond.toString()).toBe('(NOT ($0 IS NUMERIC))');
    });

    it('should test toJson function', () => {
      const notCond = new NotCondition([condition]);
      const json = notCond.toJson();
      expect(json.type).toBe('NotCondition');
      expect(json.operands[0]).toEqual(condition.toJson());
    });

    it('should pass semantic check', () => {
      const notCond = new NotCondition([condition]);
      expect(notCond.semanticCheck().success).toBeTruthy();
      expect(notCond.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand itself has semantic error', () => {
      const neg = new MockIsCondition();
      neg.sCheck = false;
      const negCon = new NotCondition([neg]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should test with evaluate function with applicable operands', () => {
      spyOn(condition, 'evaluate').and.returnValue({
        applicable: true,
        value: false
      });
      const notCond = new NotCondition([condition]);
      expect(notCond.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(notCond.evaluate(<any>nodes).value).toBeTruthy();
    });

    it('should test with evalaute function with non-applicable operands', () => {
      spyOn(condition, 'evaluate').and.returnValue({
        applicable: false
      });
      const negCon = new NotCondition([condition]);
      expect(negCon.evaluate(<any>nodes).applicable).toBeFalsy();
      expect(negCon.evaluate(<any>nodes).value).toBeUndefined();
    });
  });

  describe('with AndCondition', () => {

    const condition = new MockIsCondition();

    it('should construct an and condition successfully', () => {
      const andCon = new AndCondition([condition, condition]);
      expect(andCon.operands).toEqual([condition, condition]);
      expect(andCon.operator).toBe(PredicateOperator.And);
      expect(andCon.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const andCon = new AndCondition([condition, condition]);
      expect(andCon.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      const andCon = new AndCondition([condition, condition]);
      expect(andCon.toString()).toBe('(($0 IS NUMERIC) AND ($0 IS NUMERIC))');
    });

    it('should test toJson function', () => {
      const andCon = new AndCondition([condition, condition]);
      const json = andCon.toJson();
      andCon.operands.forEach((operand, index) => {
        expect(json.operands[index]).toEqual(operand.toJson());
      });
    });

    it('should pass semantic check', () => {
      const andCon = new AndCondition([condition, condition]);
      expect(andCon.semanticCheck().success).toBeTruthy();
      expect(andCon.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand itself has semantic error', () => {
      const neg = new MockIsCondition();
      neg.sCheck = false;
      const negCon = new AndCondition([neg, neg]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should test with evalaute function with applicable operands', () => {
      const cond1 = new MockIsCondition();
      const cond2 = new MockIsCondition();
      spyOn(cond1, 'evaluate').and.returnValue({
        applicable: true,
        value: true
      });
      spyOn(cond2, 'evaluate').and.returnValue({
        applicable: true,
        value: false
      });
      const negCon = new AndCondition([cond1, cond2]);
      expect(negCon.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(negCon.evaluate(<any>nodes).value).toBeFalsy();
    });

    it('should test with evalaute function with non-applicable operands', () => {
      const cond1 = new MockIsCondition();
      const cond2 = new MockIsCondition();
      spyOn(cond1, 'evaluate').and.returnValue({
        applicable: false
      });
      spyOn(cond2, 'evaluate').and.returnValue({
        applicable: true,
        value: false
      });
      const negCon = new AndCondition([cond1, cond2]);
      expect(negCon.evaluate(<any>nodes).applicable).toBeFalsy();
      expect(negCon.evaluate(<any>nodes).value).toBeUndefined();
    });
  });

  describe('with OrCondition', () => {

    const condition = new MockIsCondition();

    it('should construct an or condition successfully', () => {
      const orCon = new OrCondition([condition, condition]);
      expect(orCon.operands).toEqual([condition, condition]);
      expect(orCon.operator).toBe(PredicateOperator.Or);
      expect(orCon.operandsTypes).toBeDefined();
    });

    it('should test getType function', () => {
      const orCon = new OrCondition([condition, condition]);
      expect(orCon.getType()).toBe(DataType.Bool);
    });

    it('should test toString function', () => {
      const orCon = new OrCondition([condition, condition]);
      expect(orCon.toString()).toBe('(($0 IS NUMERIC) OR ($0 IS NUMERIC))');
    });

    it('should test toJson function', () => {
      const orCon = new OrCondition([condition, condition]);
      const json = orCon.toJson();
      orCon.operands.forEach((operand, index) => {
        expect(json.operands[index]).toEqual(operand.toJson());
      });
    });

    it('should pass semantic check', () => {
      const orCon = new OrCondition([condition, condition]);
      expect(orCon.semanticCheck().success).toBeTruthy();
      expect(orCon.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand itself has semantic error', () => {
      const neg = new MockIsCondition();
      neg.sCheck = false;
      const negCon = new OrCondition([neg, neg]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should fail semantic check if the operand itself has semantic error', () => {
      const neg = new MockIsCondition();
      neg.sCheck = false;
      const negCon = new OrCondition([neg, neg]);
      expect(negCon.semanticCheck().success).toBeFalsy();
      expect(negCon.semanticCheck().errors).toEqual([]);
    });

    it('should test with evalaute function with applicable operands', () => {
      const cond1 = new MockIsCondition();
      const cond2 = new MockIsCondition();
      spyOn(cond1, 'evaluate').and.returnValue({
        applicable: true,
        value: false
      });
      spyOn(cond2, 'evaluate').and.returnValue({
        applicable: true,
        value: true
      });
      const negCon = new OrCondition([cond1, cond2]);
      expect(negCon.evaluate(<any>nodes).applicable).toBeTruthy();
      expect(negCon.evaluate(<any>nodes).value).toBeTruthy();
    });

    it('should test with evalaute function with non-applicable operands', () => {
      const cond1 = new MockIsCondition();
      const cond2 = new MockIsCondition();
      spyOn(cond1, 'evaluate').and.returnValue({
        applicable: false
      });
      spyOn(cond2, 'evaluate').and.returnValue({
        applicable: true,
        value: false
      });
      const negCon = new OrCondition([cond1, cond2]);
      expect(negCon.evaluate(<any>nodes).applicable).toBeFalsy();
      expect(negCon.evaluate(<any>nodes).value).toBeUndefined();
    });
  });
});
