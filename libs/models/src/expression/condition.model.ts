import { GraphChartDataNode, GraphChartDataLink } from '../gvis';

import { DataType } from '../data';

import { Atom, AtomJson } from './atom.model';
import { EvaluateResult } from './evaluate-result.interface';
import { ExpressionValidator, ExpressionValidateResult } from './expression-validator';
import { BaseExpression, ExpressionJson } from './expression.model';
import { ArithmeticOperator, RelationalOperator, PredicateOperator } from './operators.enum';

/**
 * JSON format for condition.
 *
 * @export
 * @interface ConditionJson
 */
export interface ConditionJson {
  type: string;
  operands: Array<ConditionJson | ExpressionJson | AtomJson>;
  operator?: RelationalOperator | PredicateOperator;
}

/**
 * Base class for condition.
 *
 * @export
 * @abstract
 * @class BaseCondition
 */
export abstract class BaseCondition {
  operands: Array<Atom | BaseExpression | BaseCondition>;
  operator?: RelationalOperator | PredicateOperator;
  operandsTypes: any[][];

  constructor(
    operands: Array<Atom | BaseExpression | BaseCondition>,
    operator?: RelationalOperator | PredicateOperator
  ) {
    this.operands = operands;
    this.operator = operator;
  }

  getType(): DataType {
    return DataType.Bool;
  }

  getReferredVertexOrEdgeTypes(): string[] {
    const types: string[] = [];
    if (this.operands) {
      this.operands.forEach(operand => types.push(...operand.getReferredVertexOrEdgeTypes()));
    }
    return types;
  }

  evaluate(nodeOrLink?: GraphChartDataNode | GraphChartDataLink): EvaluateResult;
  evaluate(): EvaluateResult {
    return {
      applicable: false
    };
  }

  abstract toString(): string;
  abstract toJson(): ConditionJson;
  abstract semanticCheck(): ExpressionValidateResult;
}

/**
 * Class for handling undefined condition.
 *
 * @export
 * @class NullCondition
 * @extends {BaseCondition}
 */
export class NullCondition extends BaseCondition {
  constructor() {
    super(undefined, undefined);
  }

  getType() {
    return DataType.Unknown;
  }

  toString() {
    return '';
  }

  toJson() {
    return {
      type: 'NullCondition',
      operands: []
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: false,
      errors: ['Please choose condition type.']
    };
  }
}

/**
 * Class for comparison condition.
 * String and numeric comparison are supported.
 *
 * @export
 * @class ComparisonCondition
 * @extends {BaseCondition}
 */
export class ComparisonCondition extends BaseCondition {
  operands: Array<Atom | BaseExpression>;
  operator: RelationalOperator;
  operandsTypes = [
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'DatetimeConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'DatetimeParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AliasReference',
      'AttrVariable',
      'AttrVariableWithAlias',
      'Function',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'DatetimeConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'DatetimeParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AliasReference',
      'AttrVariable',
      'AttrVariableWithAlias',
      'Function',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ]
  ];

  constructor(operands: Array<Atom | BaseExpression>, operator: RelationalOperator) {
    super(operands, operator);
  }

  toString(): string {
    return `(${this.operands[0]} ${this.operator} ${this.operands[1]})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'ComparisonCondition',
      operator: this.operator,
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    if (!Object.values(RelationalOperator).includes(this.operator)) {
      return {
        success: false,
        errors: [
          'This expression contains an unsupported condition type. ' +
          'Did you import a solution from a higher version of GraphStudio?'
        ]
      };
    }
    if (
      !this.operands[0].semanticCheck().success ||
      !this.operands[1].semanticCheck().success
    ) {
      return {
        success: false,
        errors: []
      };
    }

    let passSemanticCheck = true;
    const semanticErrors = [];
    const leftOperandType = this.operands[0].getType();
    const rightOperandType = this.operands[1].getType();

    if (
      ExpressionValidator.isBoolean(rightOperandType) &&
      ExpressionValidator.isBoolean(leftOperandType)
    ) {
      // The operators between two boolean operands should be '==' or '!='.
      if (this.operator !== '==' && this.operator !== '!=') {
        passSemanticCheck = false;
        semanticErrors.push(
          `Cannot compare ${leftOperandType.toString()} ` +
          `expressions with ${this.operator} operator.`
        );
      }
    }
    if (
      leftOperandType !== rightOperandType &&
      !(ExpressionValidator.isNumeric(leftOperandType) &&
        ExpressionValidator.isNumeric(rightOperandType))
    ) {
      // Integer type is compatible with real type.
      passSemanticCheck = false;
      semanticErrors.push(
        `Cannot compare ${leftOperandType.toString()} ` +
        `and ${rightOperandType.toString()}.`
      );
    }
    if (
      ExpressionValidator.isVertexOrEdge(leftOperandType) &&
      ExpressionValidator.isVertexOrEdge(rightOperandType)
    ) {
      // The operators between two boolean operands should be '==' or '!='.
      if (this.operator !== '==' && this.operator !== '!=') {
        passSemanticCheck = false;
        semanticErrors.push(
          `Cannot compare ${leftOperandType.toString()} ` +
          `expressions with ${this.operator} operator.`
        );
      }
    }

    return {
      success: passSemanticCheck,
      errors: semanticErrors
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const leftOperandEvalResult = this.operands[0].evaluate(nodeOrLink);
    const rightOperandEvalResult = this.operands[1].evaluate(nodeOrLink);

    if (
      !leftOperandEvalResult.applicable ||
      !rightOperandEvalResult.applicable
    ) {
      return {
        applicable: false
      };
    }

    let evalResult: boolean;
    switch (this.operator) {
      case '==': {
        evalResult = leftOperandEvalResult.value === rightOperandEvalResult.value;
        break;
      }
      case '!=': {
        evalResult = leftOperandEvalResult.value !== rightOperandEvalResult.value;
        break;
      }
      case '>': {
        evalResult = leftOperandEvalResult.value > rightOperandEvalResult.value;
        break;
      }
      case '>=': {
        evalResult = leftOperandEvalResult.value >= rightOperandEvalResult.value;
        break;
      }
      case '<': {
        evalResult = leftOperandEvalResult.value < rightOperandEvalResult.value;
        break;
      }
      case '<=': {
        evalResult = leftOperandEvalResult.value <= rightOperandEvalResult.value;
        break;
      }
    }

    return {
      applicable: true,
      value: evalResult
    };
  }
}

/**
 * Class for IS NUMERIC or IS EMPTY condition.
 *
 * @export
 * @class IsCondition
 * @extends {BaseCondition}
 */
export class IsCondition extends BaseCondition {
  operands: Array<Atom | BaseExpression>;
  operator: PredicateOperator.IsNumeric | PredicateOperator.IsEmpty;
  operandsTypes = [['NullExpression', 'LoadingVariable', 'Function']];

  constructor(
    operands: Array<Atom | BaseExpression>,
    operator: PredicateOperator.IsNumeric | PredicateOperator.IsEmpty
  ) {
    super(operands, operator);
  }

  toString(): string {
    return `(${this.operands[0]} ${this.operator})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'IsCondition',
      operator: this.operator,
      operands: [this.operands[0].toJson()]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    // If semantic checks for the children nodes fail return false as well for this level.
    if (!this.operands[0].semanticCheck().success) {
      return {
        success: false,
        errors: []
      };
    }

    let passSemanticCheck = true;
    const semanticErrors = [];

    if (!ExpressionValidator.isString(this.operands[0].getType())) {
      passSemanticCheck = false;
      semanticErrors.push('Only string type expression is allowed here.');
    }

    return {
      success: passSemanticCheck,
      errors: semanticErrors
    };
  }
}

/**
 * Class for IN condition.
 *
 * @export
 * @class InCondition
 * @extends {BaseCondition}
 */
export class InCondition extends BaseCondition {
  operands: Array<Atom | BaseExpression>;
  operandsTypes = [
    ['NullExpression', 'LoadingVariable', 'Function'],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ]
  ];

  constructor(operands: Array<Atom | BaseExpression>) {
    super(operands, PredicateOperator.In);
  }

  toString(): string {
    return `(${this.operands[0]} IN (` +
      this.operands.slice(1).map(operand => operand.toString()).join(', ') +
      `))`;
  }

  toJson(): ConditionJson {
    return {
      type: 'InCondition',
      operands: this.operands.map(operand => operand.toJson())
    };
  }

  semanticCheck(): ExpressionValidateResult {
    // If semantic checks for the children nodes fail return false as well for this level.
    for (let i = 0; i < this.operands.length; i++) {
      if (!this.operands[i].semanticCheck().success) {
        return {
          success: false,
          errors: []
        };
      }
    }

    let passSemanticCheck = true;
    const semanticErrors = [];
    const baseType = this.operands[0].getType();

    if (ExpressionValidator.isNumeric(baseType)) {
      this.operands.slice(1).forEach((operand, i) => {
        if (!ExpressionValidator.isNumeric(operand.getType())) {
          passSemanticCheck = false;
          semanticErrors.push(`Expression${i + 1} should be Numeric, not ${operand.getType()}.`);
        }
      });
    } else if (ExpressionValidator.isString(baseType)) {
      this.operands.slice(1).forEach((operand, i) => {
        if (!ExpressionValidator.isString(operand.getType())) {
          passSemanticCheck = false;
          semanticErrors.push(`Expression${i + 1} should be String, not ${operand.getType()}.`);
        }
      });
    } else {
      passSemanticCheck = false;
      semanticErrors.push(`Expression cannot be ${baseType.toString()}.`);
    }

    return {
      success: passSemanticCheck,
      errors: semanticErrors
    };
  }
}

/**
 * Class for BETWEEN AND condition.
 *
 * @export
 * @class BetweenAndCondition
 * @extends {BaseCondition}
 */
export class BetweenAndCondition extends BaseCondition {
  operands: Array<Atom | BaseExpression>;
  operandsTypes = [
    ['NullExpression', 'LoadingVariable', 'Function'],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ]
  ];

  constructor(operands: Array<Atom | BaseExpression>) {
    super(operands, PredicateOperator.BetweenAnd);
  }

  toString(): string {
    return `(${this.operands[0]} BETWEEN ${this.operands[1]} AND ${this.operands[2]})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'BetweenAndCondition',
      operands: this.operands.map(operand => operand.toJson())
    };
  }

  semanticCheck(): ExpressionValidateResult {
    for (let i = 0; i < this.operands.length; i++) {
      if (!this.operands[i].semanticCheck().success) {
        // If semantic checks for the children nodes fail return false as well for this level.
        return {
          success: false,
          errors: []
        };
      }
    }

    let passSemanticCheck = true;
    const semanticErrors = [];
    const baseType = this.operands[0].getType();

    if (ExpressionValidator.isNumeric(baseType)) {
      this.operands.slice(1).forEach((operand, i) => {
        if (!ExpressionValidator.isNumeric(operand.getType())) {
          passSemanticCheck = false;
          semanticErrors.push(`Expression${i + 1} should be Numeric, not ${operand.getType()}.`);
        }
      });
    } else if (ExpressionValidator.isString(baseType)) {
      this.operands.slice(1).forEach((operand, i) => {
        if (!ExpressionValidator.isString(operand.getType())) {
          passSemanticCheck = false;
          semanticErrors.push(`Expression${i + 1} should be String, not ${operand.getType()}.`);
        }
      });
    } else {
      passSemanticCheck = false;
      semanticErrors.push(`Expression cannot be ${baseType.toString()}.`);
    }

    return {
      success: passSemanticCheck,
      errors: semanticErrors
    };
  }
}

/**
 * Class for NOT condition.
 *
 * @export
 * @class NotCondition
 * @extends {BaseCondition}
 */
export class NotCondition extends BaseCondition {
  operands: BaseCondition[];
  operandsTypes = [
    [
      'NullCondition',
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.BetweenAnd,
      PredicateOperator.In,
      PredicateOperator.IsEmpty,
      PredicateOperator.IsNumeric,
      PredicateOperator.Not,
      PredicateOperator.And,
      PredicateOperator.Or,
      'Function'
    ]
  ];

  constructor(operands: BaseCondition[]) {
    super(operands, PredicateOperator.Not);
  }

  toString(): string {
    return `(NOT ${this.operands[0]})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'NotCondition',
      operands: [
        this.operands[0].toJson()
      ]
    };
  }

  // Here we assume that unsupported type of operands have been filter out in the component.
  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.operands[0].semanticCheck().success,
      errors: []
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const operandEvalResult = this.operands[0].evaluate(nodeOrLink);
    if (!operandEvalResult.applicable) {
      return {
        applicable: false
      };
    }

    return {
      applicable: true,
      value: !operandEvalResult.value
    };
  }
}

/**
 * Class for AND condition.
 *
 * @export
 * @class AndCondition
 * @extends {BaseCondition}
 */
export class AndCondition extends BaseCondition {
  operands: BaseCondition[];
  operandsTypes = [
    [
      'NullCondition',
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.BetweenAnd,
      PredicateOperator.In,
      PredicateOperator.IsEmpty,
      PredicateOperator.IsNumeric,
      PredicateOperator.Not,
      PredicateOperator.And,
      PredicateOperator.Or,
      'Function'
    ],
    [
      'NullCondition',
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.BetweenAnd,
      PredicateOperator.In,
      PredicateOperator.IsEmpty,
      PredicateOperator.IsNumeric,
      PredicateOperator.Not,
      PredicateOperator.And,
      PredicateOperator.Or,
      'Function'
    ]
  ];

  constructor(operands: BaseCondition[]) {
    super(operands, PredicateOperator.And);
  }

  toString(): string {
    return `(${this.operands[0].toString()} AND ${this.operands[1].toString()})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'AndCondition',
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }

  // Here we assume that unsupported type of operands have been filter out in the component.
  semanticCheck(): ExpressionValidateResult {
    return {
      success:
        this.operands[0].semanticCheck().success &&
        this.operands[1].semanticCheck().success,
      errors: []
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const leftoperandEvalResult = this.operands[0].evaluate(nodeOrLink);
    const rightoperandEvalResult = this.operands[1].evaluate(nodeOrLink);

    if (!leftoperandEvalResult.applicable || !rightoperandEvalResult.applicable) {
      return {
        applicable: false
      };
    }

    return {
      applicable: true,
      value: leftoperandEvalResult.value && rightoperandEvalResult.value
    };
  }
}
/**
 * Class for OR condition.
 *
 * @export
 * @class OrCondition
 * @extends {BaseCondition}
 */
export class OrCondition extends BaseCondition {
  operands: BaseCondition[];
  operandsTypes = [
    [
      'NullCondition',
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.BetweenAnd,
      PredicateOperator.In,
      PredicateOperator.IsEmpty,
      PredicateOperator.IsNumeric,
      PredicateOperator.Not,
      PredicateOperator.And,
      PredicateOperator.Or,
      'Function'
    ],
    [
      'NullCondition',
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.BetweenAnd,
      PredicateOperator.In,
      PredicateOperator.IsEmpty,
      PredicateOperator.IsNumeric,
      PredicateOperator.Not,
      PredicateOperator.And,
      PredicateOperator.Or,
      'Function'
    ]
  ];

  constructor(operands: BaseCondition[]) {
    super(operands, PredicateOperator.Or);
  }

  toString(): string {
    return `(${this.operands[0].toString()} OR ${this.operands[1].toString()})`;
  }

  toJson(): ConditionJson {
    return {
      type: 'OrCondition',
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }

  // Here we assume that unsupported type of operands have been filter out in the component.
  semanticCheck(): ExpressionValidateResult {
    return {
      success:
        this.operands[0].semanticCheck().success &&
        this.operands[1].semanticCheck().success,
      errors: []
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const leftoperandEvalResult = this.operands[0].evaluate(nodeOrLink);
    const rightoperandEvalResult = this.operands[1].evaluate(nodeOrLink);

    if (!leftoperandEvalResult.applicable || !rightoperandEvalResult.applicable) {
      return {
        applicable: false
      };
    }

    return {
      applicable: true,
      value: leftoperandEvalResult.value || rightoperandEvalResult.value
    };
  }
}
