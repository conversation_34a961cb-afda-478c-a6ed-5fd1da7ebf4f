import { DataType } from '../data';

/**
 * Expression validator result.
 *
 * @export
 * @interface ExpressionValidateResult
 */
export interface ExpressionValidateResult {
  success: boolean;
  errors: string[];
}

/**
 * Define helper functions for semantic check
 *
 * @export
 * @namespace ExpressionValidator
 */
export namespace ExpressionValidator {

  /**
   * Check if the data type of the parameter is integer.
   *
   * @export
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isInt(type: DataType): boolean {
    return type === DataType.Int;
  }

  /**
   * Check if the data type of the parameter is float or double.
   *
   * @export
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isReal(type: DataType): boolean {
    return type === DataType.Real;
  }

  /**
   * Check if the data type of the parameter is numeric type.
   *
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isNumeric(type: DataType): boolean {
    return isInt(type) || isReal(type);
  }

  /**
   * Check if the data type of the parameter is string type.
   *
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isString(type: DataType): boolean {
    return type === DataType.String;
  }

  /**
   * Check if the data type of the parameter is boolean type.
   *
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isBoolean(type: DataType): boolean {
    return type === DataType.Bool;
  }

  /**
   * Check if the data type of the parameter is vertex or edge type.
   *
   * @param {DataType} type
   * @returns {boolean}
   */
  export function isVertexOrEdge(type: DataType): boolean {
    return type === DataType.VertexOrEdge;
  }
}
