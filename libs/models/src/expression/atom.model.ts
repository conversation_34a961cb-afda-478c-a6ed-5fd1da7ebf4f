import { GraphChartDataNode, GraphChartDataLink } from '../gvis';

import { DataType } from '../data';
import { FormatValidator, HelperFunctions } from '../utils';

import { EvaluateResult } from './evaluate-result.interface';
import { ExpressionValidateResult } from './expression-validator';

/**
 * JSON format for atom.
 *
 * @export
 * @interface AtomJson
 */
export interface AtomJson {
  type: string;
  value?: number | string | boolean;
  valueType?: DataType;
}

/**
 * Default value of atoms.
 *
 * @export
 * @const AtomDefaultValue
 */
export const AtomDefaultValue = new Map<string, number | string | boolean>([
  ['IntConstant', 0],
  ['RealConstant', 0],
  ['StringConstant', ''],
  ['BoolConstant', true],
  ['DatetimeConstant', ''],
  ['LoadingVariable', 0],
  ['AttrVariable', ''],
  ['AttrVariableWithAlias', ''],
  ['AliasReference', ''],
  ['IntAccum', '@'],
  ['RealAccum', '@'],
  ['StringAccum', '@'],
  ['BoolAccum', '@']
]);

/**
 * Atom includes constant and variable.
 *
 * @export
 * @abstract
 * @class Atom
 */
export abstract class Atom {
  value?: number | string | boolean;
  valueType: DataType;

  constructor(value?: number | string | boolean, valueType?: DataType) {
    this.value = value;
    this.valueType = valueType || DataType.Unknown;
  }

  getType(): DataType {
    return this.valueType;
  }

  getReferredVertexOrEdgeTypes(): string[] {
    return [];
  }

  toString(): string {
    return `${this.value !== undefined ? this.value : ''}`;
  }

  abstract toJson(): AtomJson;
  abstract semanticCheck(): ExpressionValidateResult;
  abstract evaluate(nodeOrLink?: GraphChartDataNode | GraphChartDataLink): EvaluateResult;
}

/**
 * Base class for constant.
 *
 * @export
 * @abstract
 * @class BaseConstant
 * @extends {Atom}
 */
export abstract class BaseConstant extends Atom {
  constructor(value: number | string | boolean, valueType: DataType) {
    super(value, valueType);
  }

  evaluate(): EvaluateResult {
    return {
      applicable: true,
      value: this.value
    };
  }
}

/**
 * Class for integer constant.
 *
 * @export
 * @class IntConstant
 * @extends {BaseConstant}
 */
export class IntConstant extends BaseConstant {
  value: number;

  constructor(value: number) {
    /* istanbul ignore next */
    super(value, DataType.Int);
  }

  toJson(): AtomJson {
    return {
      type: 'IntConstant',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isInt(`${this.value}`);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}

/**
 * Class for real constant.
 *
 * @export
 * @class RealConstant
 * @extends {BaseConstant}
 */
export class RealConstant extends BaseConstant {
  value: number;

  constructor(value: number) {
    /* istanbul ignore next */
    super(value, DataType.Real);
  }

  toJson(): AtomJson {
    return {
      type: 'RealConstant',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isReal(`${this.value}`);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}

/**
 * Class for string constant.
 *
 * @export
 * @class StringConstant
 * @extends {BaseConstant}
 */
export class StringConstant extends BaseConstant {
  value: string;

  constructor(value: string) {
    /* istanbul ignore next */
    super(value, DataType.String);
  }

  toString(): string {
    return '"' +
      this.value
        .replace(/\\/g, '\\\\')
        .replace(/\"/g, '\\"')
        .replace(/\\\\n/g, '\\n')
        .replace(/\\\\t/g, '\\t') + '"';
  }

  toJson(): AtomJson {
    return {
      type: 'StringConstant',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: true,
      errors: []
    };
  }
}

/**
 * Class for boolean constant.
 *
 * @export
 * @class BoolConstant
 * @extends {BaseConstant}
 */
export class BoolConstant extends BaseConstant {
  value: boolean;

  constructor(value: boolean) {
    /* istanbul ignore next */
    super(value, DataType.Bool);
  }

  toJson(): AtomJson {
    return {
      type: 'BoolConstant',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isBoolean(`${this.value}`);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}

export class DatetimeConstant extends BaseConstant {
  value: string;

  constructor(value: string) {
    super(value, DataType.Datetime);
  }

  toString(): string {
    return `to_datetime("${this.value}")`;
  }

  toJson(): AtomJson {
    return {
      type: 'DatetimeConstant',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isDatetime(this.value);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}

/**
 * Base class for variable.
 *
 * @export
 * @abstract
 * @class BaseVariable
 * @extends {Atom}
 */
export abstract class BaseVariable extends Atom {
  constructor(value: number | string, valueType: DataType) {
    super(value, valueType);
  }
}

/**
 * Class for column variable in a loading job.
 *
 * @export
 * @class LoadingVariable
 * @extends {BaseVariable}
 */
export class LoadingVariable extends BaseVariable {
  value: number | string;

  constructor(value: number) {
    /* istanbul ignore next */
    super(value, DataType.String);
  }

  toString(): string {
    // E.g. if value is 0, then the output string is '$0'.
    return `$${this.value}`;
  }

  toJson(): AtomJson {
    return {
      type: 'LoadingVariable',
      value: this.value
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isUInt(`${this.value}`);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }

  evaluate(): EvaluateResult {
    return {
      applicable: false
    };
  }
}

/**
 * Class for attribute name variable of a vertex or edge.
 *
 * @export
 * @class AttrVariable
 * @extends {BaseVariable}
 */
export class AttrVariable extends BaseVariable {
  value: string;

  constructor(value: string, valueType: DataType) {
    /* istanbul ignore next */
    super(value, valueType);
  }

  getReferredVertexOrEdgeTypes(): string[] {
    const sep = (<string>this.value).indexOf('.');
    const type = (<string>this.value).slice(0, sep);
    return [type];
  }

  toJson(): AtomJson {
    return {
      type: 'AttrVariable',
      value: this.value,
      valueType: this.valueType
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: this.value.length > 0,
      errors: this.value.length > 0 ? [] : ['Attribute name cannot be empty.']
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const attrName = this.value.slice(this.value.indexOf('.') + 1);
    const attrValue = HelperFunctions.retrieveAttributeAndAccumValue(nodeOrLink.attrs, attrName);
    if (['string', 'number', 'boolean'].includes(typeof attrValue)) {
      return {
        applicable: true,
        value: attrValue
      };
    } else {
      return {
        applicable: false
      };
    }
  }
}

/**
 * Class for attribute name variable of a remote vertex or edge.
 *
 * @export
 * @abstract
 * @class AttrVariableWithAlias
 * @extends {AttrVariable}
 */
export abstract class AttrVariableWithAlias extends AttrVariable {
  constructor(value: string, valueType: DataType) {
    super(value, valueType);
  }

  toJson(): AtomJson {
    return {
      type: 'AttrVariableWithAlias',
      value: this.value,
      valueType: this.valueType
    };
  }
}

/**
 * Base class for accumulator.
 *
 * @export
 * @abstract
 * @class BaseAccum
 * @extends {Atom}
 */
export abstract class BaseAccum extends Atom {
  value: string;

  constructor(value: string, valueType: DataType) {
    super(value, valueType);
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isAccum(this.value);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}

/**
 * Class for integer accumulator.
 *
 * @export
 * @class IntAccum
 * @extends {BaseAccum}
 */
export class IntAccum extends BaseAccum {
  constructor(value: string) {
    /* istanbul ignore next */
    super(value, DataType.Int);
  }

  toJson(): AtomJson {
    return {
      type: 'IntAccum',
      value: this.value
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const attrValue = HelperFunctions.retrieveAttributeAndAccumValue(nodeOrLink.attrs, this.value);
    if (typeof attrValue === 'number') {
      return {
        applicable: true,
        value: attrValue
      };
    } else {
      return {
        applicable: false
      };
    }
  }
}

/**
 * Class for real accumulator.
 *
 * @export
 * @class RealAccum
 * @extends {BaseAccum}
 */
export class RealAccum extends BaseAccum {
  constructor(value: string) {
    /* istanbul ignore next */
    super(value, DataType.Real);
  }

  toJson(): AtomJson {
    return {
      type: 'RealAccum',
      value: this.value
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const attrValue = HelperFunctions.retrieveAttributeAndAccumValue(nodeOrLink.attrs, this.value);
    if (typeof attrValue === 'number') {
      return {
        applicable: true,
        value: attrValue
      };
    } else {
      return {
        applicable: false
      };
    }
  }
}

/**
 * Class for string accumulator.
 *
 * @export
 * @class StringAccum
 * @extends {BaseAccum}
 */
export class StringAccum extends BaseAccum {
  constructor(value: string) {
    /* istanbul ignore next */
    super(value, DataType.String);
  }

  toJson(): AtomJson {
    return {
      type: 'StringAccum',
      value: this.value
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const attrValue = HelperFunctions.retrieveAttributeAndAccumValue(nodeOrLink.attrs, this.value);
    if (typeof attrValue === 'string') {
      return {
        applicable: true,
        value: attrValue
      };
    } else {
      return {
        applicable: false
      };
    }
  }
}

/**
 * Class for boolean accumulator.
 *
 * @export
 * @class BoolAccum
 * @extends {BaseAccum}
 */
export class BoolAccum extends BaseAccum {
  constructor(value: string) {
    /* istanbul ignore next */
    super(value, DataType.Bool);
  }

  toJson(): AtomJson {
    return {
      type: 'BoolAccum',
      value: this.value
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const attrValue = HelperFunctions.retrieveAttributeAndAccumValue(nodeOrLink.attrs, this.value);
    if (typeof attrValue === 'boolean') {
      return {
        applicable: true,
        value: attrValue
      };
    } else {
      return {
        applicable: false
      };
    }
  }
}

/**
 * Base class for parameter.
 *
 * @abstract
 * @class BaseParam
 * @extends {Atom}
 */
export abstract class BaseParam extends Atom {
  value: string;

  constructor(value: string, valueType: DataType) {
    super(value, valueType);
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isName(this.value);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }

  evaluate(): EvaluateResult {
    return {
      applicable: false
    };
  }

  toString(): string {
    return `{{${this.value !== undefined ? this.value : ''}}}`;
  }
}

/**
 * Class for integer parameter.
 *
 * @class IntParam
 * @extends {BaseParam}
 */
export class IntParam extends BaseParam {
  constructor(value: string) {
    super(value, DataType.Int);
  }

  toJson(): AtomJson {
    return {
      type: 'IntParam',
      value: this.value
    };
  }
}

/**
 * Class for real parameter.
 *
 * @class RealParam
 * @extends {BaseParam}
 */
export class RealParam extends BaseParam {
  constructor(value: string) {
    super(value, DataType.Real);
  }

  toJson(): AtomJson {
    return {
      type: 'RealParam',
      value: this.value
    };
  }
}

/**
 * Class for string paramter.
 *
 * @class StringParam
 * @extends {BaseParam}
 */
export class StringParam extends BaseParam {
  constructor(value: string) {
    super(value, DataType.String);
  }

  toJson(): AtomJson {
    return {
      type: 'StringParam',
      value: this.value
    };
  }
}

/**
 * Class for  datetime paramter.
 *
 * @export
 * @class DatetimeParam
 * @extends {BaseParam}
 */
export class DatetimeParam extends BaseParam {
  constructor(value: string) {
    super(value, DataType.Datetime);
  }

  toJson(): AtomJson {
    return {
      type: 'DatetimeParam',
      value: this.value
    };
  }
}

/**
 * Class for boolean parameter.
 *
 * @class BoolParam
 * @extends {BaseParam}
 */
export class BoolParam extends BaseParam {
  constructor(value: string) {
    super(value, DataType.Bool);
  }

  toJson(): AtomJson {
    return {
      type: 'BoolParam',
      value: this.value
    };
  }
}

/**
 * Refer to an alias.
 *
 * @export
 * @class AliasReference
 * @extends {Atom}
 */
export class AliasReference extends Atom {
  value: string;

  constructor(value: string) {
    super(value, DataType.VertexOrEdge);
  }

  evaluate(): EvaluateResult {
    return {
      applicable: false
    };
  }

  toJson(): AtomJson {
    return {
      type: 'AliasReference',
      value: this.value
    };
  }

  toString(): string {
    return `${this.value}`;
  }

  semanticCheck(): ExpressionValidateResult {
    const result = FormatValidator.isName(`${this.value}`);
    return {
      success: result.success,
      errors: !result.success ? [result.message] : []
    };
  }
}
