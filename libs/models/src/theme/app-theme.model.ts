/**
 * Application theme details.
 *
 * @export
 * @interface AppTheme
 */
export interface AppTheme {
  href: string;
  accent: string;
  primary: string;
  isDark: boolean;
  isDefault: boolean;
}

// Theme settings.
export const THEMES: AppTheme[] = [{
  href: 'light.css',
  accent: '#009688',
  primary: '#ef6c00',
  isDark: false,
  isDefault: false
}, {
  href: 'dark.css',
  accent: '#009688',
  primary: '#ef6c00',
  isDark: true,
  isDefault: true
}];
