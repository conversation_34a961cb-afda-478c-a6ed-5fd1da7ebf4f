/**
 * Represent global status for notification component.
 *
 * @export
 * @interface GlobalStatus
 */
export interface GlobalStatus {
  catalogLocked?: boolean;
  schemaReady?: boolean;
  uploadSolution?: {
    running: boolean;
  };
  importSolution?: {
    running: boolean;
  };
  updateSchema?: {
    running: boolean;
  };
  clearGraphStore?: {
    running: boolean;
  };
  rebuildGraphStore?: {
    running: boolean;
  };
  installQuery?: {
    running: boolean;
    queries: string[];
    progress?: string;
  };
  loadingJobs?: {
    name: string;
    status: 'ready' | 'loading' | 'completed' | 'terminated';
    progress: number;
  }[];
  notificationList?: {
    message: string;
    type: string;
  }[];
}
