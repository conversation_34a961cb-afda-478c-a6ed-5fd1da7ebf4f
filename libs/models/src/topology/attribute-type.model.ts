import { DataType } from '../data';
import { GraphStudioError } from '../error';

/**
 * GSQL AttributeType json representation interface following the format:
 *   {
 *     "Name": string,
 *     ["FixedSize": int],
 *     ["TupleName": string],
 *     ["KeyTypeName": string],
 *     ["ValueTypeTupleName": string]
 *   }
 *
 * @export
 * @interface GSQLAttributeTypeJson
 */
export interface GSQLAttributeTypeJson {
  Name: string;
  FixedSize?: number;
  TupleName?: string;
  KeyTypeName?: string;
  ValueTypeName?: string;
  ValueTypeTupleName?: string;
}

export interface GSQLUdtJson {
  name: string;
  fields: {
    fieldName: string;
    fieldType: string;
    length?: number;
  }[];
}

export const supportedAttributeTypes: string[] = [
  'INT',
  'UINT',
  'BOOL',
  'FLOAT',
  'DOUBLE',
  'STRING',
  'STRING COMPRESS',
  'DATETIME',
  'LIST',
  'SET',
  'MAP',
  'UDT'
];

export const supportedDiscriminatorAttributeTypes: string[] = [
  'INT',
  'UINT',
  'STRING',
  'DATETIME'
];

export const supportedPrimaryIdTypes: string[] = [
  'STRING',
  'INT',
  'UINT',
  'DATETIME',
  'FLOAT',
  'DOUBLE',
];

export const deprecatedPrimaryIdTypes: string[] = [
  'FLOAT',
  'DOUBLE',
];

export const supportedMapKeyTypes: string[] = [
  'INT',
  'STRING',
  'STRING COMPRESS',
  'DATETIME'
];

export const supportedContainerValueTypes: string[] = [
  'INT',
  'DOUBLE',
  'STRING',
  'STRING COMPRESS',
  'DATETIME',
  'UDT'
];

export const supportedTypesInExpression: string[] = [
  'INT',
  'UINT',
  'BOOL',
  'FLOAT',
  'DOUBLE',
  'STRING',
  'STRING COMPRESS',
  'DATETIME'
];

export const supportedTypesInSizeConfigExpression: string[] = [
  'INT',
  'UINT',
  'FLOAT',
  'DOUBLE'
];

export const supportedTypesForAttributeIndex: string[] = [
  'INT',
  'UINT',
  'STRING',
  'STRING COMPRESS',
  'DATETIME'
];

/**
 * AttributeType describes an attribute type, including type name and other
 * additional properties for non-primitive attribute types. For example,
 * FIXED_BINARY has fixedSize property.
 *
 * @export
 * @class AttributeType
 */
export class AttributeType {
  name: string;
  hasFixedSize: boolean;
  fixedSize: number;
  hasTupleName: boolean;
  tupleName: string;
  hasKeyTypeName: boolean;
  keyTypeName: string;
  hasValueTypeName: boolean;
  valueTypeName: string;
  hasValueTypeTupleName: boolean;
  valueTypeTupleName: string;

  /**
   * Creates an instance of AttributeType.
   *
   * @memberof AttributeType
   */
  constructor() {
    this.name = this.tupleName = this.keyTypeName = this.valueTypeName = this.valueTypeTupleName = '';
    this.fixedSize = -1;
    this.hasFixedSize = this.hasTupleName = this.hasKeyTypeName =
    this.hasValueTypeName = this.hasValueTypeTupleName = false;
  }

  /**
   * Clone the attribute type.
   *
   * @returns {AttributeType}
   * @memberof AttributeType
   */
  clone(): AttributeType {
    const attributeType = new AttributeType();
    attributeType.name = this.name;
    attributeType.hasFixedSize = this.hasFixedSize;
    attributeType.fixedSize = this.fixedSize;
    attributeType.hasTupleName = this.hasTupleName;
    attributeType.tupleName = this.tupleName;
    attributeType.hasKeyTypeName = this.hasKeyTypeName;
    attributeType.keyTypeName = this.keyTypeName;
    attributeType.hasValueTypeName = this.hasValueTypeName;
    attributeType.valueTypeName = this.valueTypeName;
    attributeType.hasValueTypeTupleName = this.hasValueTypeTupleName;
    attributeType.valueTypeTupleName = this.valueTypeTupleName;
    return attributeType;
  }

  /**
   * Load Attribute from GSQL AttributeType json representation.
   *
   * @param {GSQLAttributeTypeJson} attributeTypeJson GSQL AttributeType json representation
   * @returns {AttributeType}
   * @memberof AttributeType
   */
  loadFromGSQLJson(attributeTypeJson: GSQLAttributeTypeJson): AttributeType {
    // Name
    this.name = attributeTypeJson.Name;

    // FixedSize
    if ('FixedSize' in attributeTypeJson) {
      this.hasFixedSize = true;
      this.fixedSize = attributeTypeJson.FixedSize;
    } else {
      this.hasFixedSize = false;
      this.fixedSize = -1;
    }
    // TupleName
    if ('TupleName' in attributeTypeJson) {
      this.hasTupleName = true;
      this.tupleName = attributeTypeJson.TupleName;
    } else {
      this.hasTupleName = false;
      this.tupleName = '';
    }
    // KeyTypeName
    if ('KeyTypeName' in attributeTypeJson) {
      this.hasKeyTypeName = true;
      this.keyTypeName = attributeTypeJson.KeyTypeName;
    } else {
      this.hasKeyTypeName = false;
      this.keyTypeName = '';
    }
    // ValueTypeName
    if ('ValueTypeName' in attributeTypeJson) {
      this.hasValueTypeName = true;
      this.valueTypeName = attributeTypeJson.ValueTypeName;
    } else {
      this.hasValueTypeName = false;
      this.valueTypeName = '';
    }
    // ValueTypeTupleName
    if ('ValueTypeTupleName' in attributeTypeJson) {
      this.hasValueTypeTupleName = true;
      this.valueTypeTupleName = attributeTypeJson.ValueTypeTupleName;
    } else {
      this.hasValueTypeTupleName = false;
      this.valueTypeTupleName = '';
    }

    // TODO: Now we don't support fixed binary.
    if (!supportedAttributeTypes.includes(this.name)) {
      throw new GraphStudioError(
        `${this.name} is advanced attribute type not ` +
        'supported in Graph Studio. Please directly use DDL in GSQL CLI to ' +
        'use this attribute type.'
      );
    }
    if (
      this.hasValueTypeName &&
      !supportedContainerValueTypes.includes(this.valueTypeName)
    ) {
      throw new GraphStudioError(
        `${this.valueTypeName} is advanced attribute container value type ` +
        'not supported in Graph Studio.'
      );
    }
    if (
      this.hasKeyTypeName &&
      !supportedMapKeyTypes.includes(this.keyTypeName)
    ) {
      throw new GraphStudioError(
        `${this.keyTypeName} is advanced map attribute key type ` +
        'not supported in Graph Studio.'
      );
    }

    return this;
  }

  /**
   * Dump AttributeType into GSQL AttributeType json representation.
   *
   * @returns {GSQLAttributeTypeJson}
   * @memberof AttributeType
   */
  dumpToGSQLJson(): GSQLAttributeTypeJson {
    const attributeTypeJson: GSQLAttributeTypeJson = {
      Name: this.name
    };
    if (this.hasFixedSize) {
      attributeTypeJson.FixedSize = this.fixedSize;
    }
    if (this.hasTupleName) {
      attributeTypeJson.TupleName = this.tupleName;
    }
    if (this.hasKeyTypeName) {
      attributeTypeJson.KeyTypeName = this.keyTypeName;
    }
    if (this.hasValueTypeName) {
      attributeTypeJson.ValueTypeName = this.valueTypeName;
    }
    if (this.hasValueTypeTupleName) {
      attributeTypeJson.ValueTypeTupleName = this.valueTypeTupleName;
    }
    return attributeTypeJson;
  }

  /**
   * Get attribute type text.
   * e.g., STRING, SET<DOUBLE>, MAP<INT, STRING>
   *
   * @returns {string}
   * @memberof AttributeType
   */
  toString(): string {
    let result = this.hasTupleName ? this.tupleName : this.name;
    if (this.name === 'MAP') {
      result += `<${this.keyTypeName}, ${
        this.hasValueTypeTupleName ? this.valueTypeTupleName : this.valueTypeName
      }>`;
    }
    if (['LIST', 'SET'].includes(this.name)) {
      result += `<${this.hasValueTypeTupleName ? this.valueTypeTupleName : this.valueTypeName}>`;
    }
    return result;
  }


  /**
   * Get the element value type.
   * For primitive types, return the name.
   * For SET and LIST, return the valueTypeName.
   * For MAP, return MAP<keyTypeName, valueTypeName>.
   *
   * @returns {string}
   * @memberof AttributeType
   */
  getValueType(): string {
    if (this.name === 'MAP') {
      return this.toString();
    }
    if (['LIST', 'SET'].includes(this.name)) {
      return this.hasValueTypeTupleName ? this.valueTypeTupleName : this.valueTypeName;
    }
    return this.hasTupleName ? this.tupleName : this.name;
  }


  /**
   * Get the data type of this attibute.
   *
   * @returns {DataType}
   * @memberof AttributeType
   */
  getDataType(): DataType {
    switch (this.name) {
      case 'INT':
      case 'UINT':
        return DataType.Int;
      case 'STRING':
      case 'STRING COMPRESS':
        return DataType.String;
      case 'FLOAT':
      case 'DOUBLE':
        return DataType.Real;
      case 'BOOL':
        return DataType.Bool;
      case 'DATETIME':
        return DataType.Datetime;
    }
  }
}
