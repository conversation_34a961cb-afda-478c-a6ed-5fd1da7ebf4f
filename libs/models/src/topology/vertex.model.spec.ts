import { GraphStudioError } from '../error';

import { Vertex, GSQLVertexJson } from './vertex.model';
import { VertexStyle } from './vertex-style.model';

describe('VertexModel', () => {
  let mockNormalVertex: GSQLVertexJson;
  let mockPrimaryKeyVertex: GSQLVertexJson;

  beforeEach(() => {
    mockNormalVertex = {
      Name: 'name',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [
        {
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        }
      ],
      Config: {},
    };

    mockPrimaryKeyVertex = {
      Name: 'name',
      PrimaryId: {
        AttributeName: 'attr1',
        AttributeType: {
          Name: 'INT'
        },
        IsPrimaryKey: true,
        IsUsingNewSyntax: true
      },
      Attributes: [
        {
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'INT'
          },
          IsPrimaryKey: true,
          IsUsingNewSyntax: true
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        }
      ],
      Config: {},
    };
  });

  it('should throw error because it is invalid vertex name.', () => {
    const mockVertexInvalidName: GSQLVertexJson = {
      Name: '#wrong name',
      PrimaryId: {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [],
      Config: {}
    };

    const vertex = new Vertex();
    expect(() => vertex.loadFromGSQLJson(mockVertexInvalidName))
    .toThrow(new GraphStudioError(`Invalid vertex name "#wrong name". ` +
      'Valid name starts with a letter, followed by letters or digits.'));
  });

  it('should throw error because primary ID name is same with vertex type name.', () => {
    const mockVertexInvalidName: GSQLVertexJson = {
      Name: 'attr',
      PrimaryId: {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [],
      Config: {}
    };

    const vertex = new Vertex();
    expect(() => vertex.loadFromGSQLJson(mockVertexInvalidName))
    .toThrow(new GraphStudioError('Vertex type name "attr" cannot be used as its primary ID name.'));
  });

  it('should throw error because attribute name is same with vertex type name.', () => {
    const mockVertexInvalidName: GSQLVertexJson = {
      Name: 'attr',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [
        {
          AttributeName: 'id',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        }
      ],
      Config: {}
    };

    const vertex = new Vertex();
    expect(() => vertex.loadFromGSQLJson(mockVertexInvalidName))
    .toThrow(new GraphStudioError('Vertex type name "attr" cannot be used as its attributes\' names.'));
  });

  it('should throw error because attributes have duplicated names.', () => {
    const mockVertexInvalidName: GSQLVertexJson = {
      Name: 'name',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        }
      ],
      Config: {}
    };

    const vertex = new Vertex();
    expect(() => vertex.loadFromGSQLJson(mockVertexInvalidName))
    .toThrow(new GraphStudioError('More than one attributes have name "attr".'));
  });

  it(
    'should throw error because attribute name is the same as primary id name' +
    'when primary id is used as attribute', () => {
    const mockVertexInvalidName: GSQLVertexJson = {
      Name: 'v1',
      PrimaryId: {
        AttributeName: 'name',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        }
      ],
      Config: {
        PRIMARY_ID_AS_ATTRIBUTE: true
      }
    };

    const vertex = new Vertex();
    expect(() => vertex.loadFromGSQLJson(mockVertexInvalidName))
    .toThrow(new GraphStudioError('Primary ID "name" is used as an attribute, ' +
      'so "name" cannot be used as another attribute\'s name.'));
  });

  it('should dump to json correctly', () => {

    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);

    expect(vertex.name).toEqual('name');
    expect(vertex.attributes.length).toEqual(2);
    expect(vertex.attributes[0].name).toEqual('attr1');
    expect(vertex.attributes[1].name).toEqual('attr2');
    expect(vertex.primaryId.name).toEqual('id');

    const vertexJson = vertex.dumpToGSQLJson();
    expect(vertexJson.Name).toEqual('name');
    expect(vertexJson.PrimaryId.AttributeName).toEqual('id');
    expect(vertexJson.Attributes.length).toEqual(2);
    expect(vertexJson.Attributes[0].AttributeName).toEqual('attr1');
    expect(vertexJson.Attributes[1].AttributeName).toEqual('attr2');
    expect(Object.keys(vertexJson.Config).length).toEqual(0);
  });

  it('should dump to json correctly', () => {
    const vertex = new Vertex();
    const vertexJson = vertex.dumpToGSQLJson();
    expect(Object.keys(vertexJson.Config).length).toEqual(1);
    expect(vertexJson.Config['STATS']).toBe('OUTDEGREE_BY_EDGETYPE');
  });

  it('should get vertex vis without style', () => {

    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);

    const vertexVis = vertex.getVis();
    expect(Object.keys(vertexVis.attributes).length).toEqual(3);
    expect(vertexVis.attributes['(PRIMARY ID) id']).toEqual('INT');
    expect(vertexVis.attributes['attr1']).toEqual('INT');
    expect(vertexVis.attributes['attr2']).toEqual('STRING COMPRESS');
    expect(vertexVis.style).toBeUndefined();
    expect(Object.keys(vertexVis.other).length).toEqual(0);
  });

  it('should get vertex vis with style', () => {

    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);
    vertex.style = new VertexStyle();
    vertex.style.loadFromDBJson({
      x: 0.4,
      y: 0.5,
      fillColor: '#123456',
      icon: 'img-001',
      other: {}
    });

    const vertexVis = vertex.getVis();
    expect(Object.keys(vertexVis.attributes).length).toEqual(3);
    expect(vertexVis.attributes['(PRIMARY ID) id']).toEqual('INT');
    expect(vertexVis.attributes['attr1']).toEqual('INT');
    expect(vertexVis.attributes['attr2']).toEqual('STRING COMPRESS');
    expect(vertexVis.style.x).toBe(0.4);
    expect(vertexVis.style.y).toBe(0.5);
    expect(vertexVis.style.fillColor).toBe('#123456');
    expect(vertex.style.icon).toBe('img-001');
    expect(Object.keys(vertexVis.other).length).toEqual(1);
    expect(vertexVis.other.fixed).toBeTruthy();
  });

  it('should get vertex vis without style', () => {

    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);

    const vertexVis = vertex.getVis();
    expect(Object.keys(vertexVis.attributes).length).toEqual(3);
    expect(vertexVis.attributes['(PRIMARY ID) id']).toEqual('INT');
    expect(vertexVis.attributes['attr1']).toEqual('INT');
    expect(vertexVis.attributes['attr2']).toEqual('STRING COMPRESS');
    expect(vertexVis.style).toBeUndefined();
    expect(Object.keys(vertexVis.other).length).toEqual(0);
  });

  it('should clone a new vertex that is not same instance as old one', () => {

    const oldVertex = new Vertex();
    oldVertex.loadFromGSQLJson(mockNormalVertex);
    const newVertex = oldVertex.clone();
    newVertex.name = 'newName';
    newVertex.primaryId.name = 'newName';

    expect(oldVertex.name).toBe('name');
    expect(newVertex.name).toBe('newName');
    expect(oldVertex.primaryId.name).toBe('id');
    expect(newVertex.primaryId.name).toBe('newName');
  });

  it('should not pass semantic check because primary id is invalid', () => {
    const vertex = new Vertex();
    vertex.name = 'newName';
    vertex.primaryId.name = '#wrong name';

    expect(vertex.semanticCheck().success).toBeFalsy();
    expect(vertex.semanticCheck().message).toBe('Invalid PrimaryID name "#wrong name". ' +
    'Valid name starts with a letter, followed by letters or digits.');
  });

  it('should not pass semantic check because attribute name is invalid', () => {
    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);
    vertex.attributes[0].name = '#wrong name';

    expect(vertex.semanticCheck().success).toBeFalsy();
    expect(vertex.semanticCheck().message).toBe('Invalid attribute name "#wrong name". ' +
    'Valid name starts with a letter, followed by letters or digits.');
  });

  it('should fail semantic check since vertex created using new syntax is not supported', () => {
    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockNormalVertex);
    vertex.compositeKeys = ['attr1', 'attr2'];
    vertex.primaryId.name = 'attr1_attr2';
    vertex.primaryId.isUsingNewSyntax = true;
    vertex.primaryId.isPartOfCompositeKey = true;
    vertex.attributes[0].isUsingNewSyntax = true;
    vertex.attributes[0].isPartOfCompositeKey = true;
    vertex.attributes[1].isUsingNewSyntax = true;
    vertex.attributes[1].isPartOfCompositeKey = true;

    expect(vertex.semanticCheck().success).toBeFalsy();
    expect(vertex.semanticCheck().message).toEqual(
      'Composite Key is not supported. ' +
      'You must use PRIMARY_ID or single PRIMARY KEY when creating your schema.'
    );
  });

  it('should pass semantic check since vertex created using new syntax is supported', () => {
    const vertex = new Vertex();
    vertex.loadFromGSQLJson(mockPrimaryKeyVertex);
   
    expect(vertex.semanticCheck().success).toBeTruthy();
  });
});
