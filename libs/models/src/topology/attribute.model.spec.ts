import { GraphStudioError } from '../error';

import { Attribute, GSQLAttributeJson } from './attribute.model';

describe('AttributeModel', () => {

  it('should test clone and result should have the same values.', () => {
    const mockAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        FixedSize: 0,
        ValueTypeName: 'DOUBLE',
        Name: 'DOUBLE'
      },
      Nullable: true,
      DefaultValue: '34',
      Alias: '',
      IsPrimaryKey: false,
      IsPartOfCompositeKey: false,
      PrimaryIdAsAttribute: true,
      HasIndex: true,
      IndexName: 'index1',
      IsUsingNewSyntax: false,
      IsDiscriminator: false
    };

    const attribute = new Attribute().loadFromGSQLJson(mockAttribute);
    expect(attribute.clone()).toEqual(attribute);

    const mockAttribute2: GSQLAttributeJson = {
      AttributeName: 'my_attribute2',
      AttributeType: {
        FixedSize: 0,
        ValueTypeName: 'DOUBLE',
        Name: 'DOUBLE'
      },
      Nullable: true,
      DefaultValue: '45',
      Alias: '',
      IsPrimaryKey: false,
      IsPartOfCompositeKey: false,
      PrimaryIdAsAttribute: false,
      HasIndex: true,
      IndexName: 'index1',
      IsUsingNewSyntax: true,
      IsDiscriminator: true
    };

    const attribute2 = new Attribute().loadFromGSQLJson(mockAttribute2);
    expect(attribute2.clone()).toEqual(attribute2);
  });

  it('should throw error because it is invalid attribute name.', () => {
    const mockIntAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: '#wrong name',
      AttributeType: {
        Name: 'INT'
      }
    };

    const attribute = new Attribute();
    expect(() => attribute.loadFromGSQLJson(mockIntAttributeInvalidName))
      .toThrow(new GraphStudioError('Invalid attribute name "#wrong name". ' +
        'Valid name starts with a letter, followed by letters or digits.'));
  });

  it('should contain only attribute name and type, other attributes do not exist', () => {
    const mockIntAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'INT'
      }
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttribute);

    expect(attribute.name).toEqual('my_attribute');
    expect(attribute.type.name).toEqual('INT');
    expect(attribute.hasNullable).toBeFalsy();
    expect(attribute.nullable).toBeFalsy();
    expect(attribute.hasAlias).toBeFalsy();
    expect(attribute.alias).toEqual('');
    expect(attribute.hasDefaultValue).toBeFalsy();
    expect(attribute.defaultValue).toEqual('');
    expect(attribute.hasIndex).toBeFalsy();
    expect(attribute.indexName).toBeUndefined();

    const intAttributeJson = attribute.dumpToGSQLJson();
    expect(intAttributeJson.AttributeName).toEqual('my_attribute');
    expect(intAttributeJson.AttributeType.Name).toEqual('INT');
    expect(Object.keys(intAttributeJson).length).toEqual(2);
  });

  it('should contain nullable and default value', () => {
    const mockDoubleAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'DOUBLE'
      },
      Nullable: true,
      DefaultValue: '34'
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockDoubleAttribute);

    expect(attribute.name).toEqual('my_attribute');
    expect(attribute.type.name).toEqual('DOUBLE');
    expect(attribute.hasNullable).toBeTruthy();
    expect(attribute.nullable).toBeTruthy();
    expect(attribute.hasAlias).toBeFalsy();
    expect(attribute.alias).toEqual('');
    expect(attribute.hasDefaultValue).toBeTruthy();
    expect(attribute.defaultValue).toEqual('34');
    expect(attribute.hasIndex).toBeFalsy();
    expect(attribute.indexName).toBeUndefined();

    const doubleAttributeJson = attribute.dumpToGSQLJson();
    expect(doubleAttributeJson.AttributeName).toEqual('my_attribute');
    expect(doubleAttributeJson.AttributeType.Name).toEqual('DOUBLE');
    expect(doubleAttributeJson.Nullable).toBeTruthy();
    expect(doubleAttributeJson.DefaultValue).toEqual('34');
    expect(Object.keys(doubleAttributeJson).length).toEqual(4);
  });

  it('should contain only attribute name, type, and isDiscriminator', () => {
    const mockIntAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'INT'
      },
      IsDiscriminator: true
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttribute);

    expect(attribute.name).toEqual('my_attribute');
    expect(attribute.type.name).toEqual('INT');
    expect(attribute.hasNullable).toBeFalsy();
    expect(attribute.nullable).toBeFalsy();
    expect(attribute.hasAlias).toBeFalsy();
    expect(attribute.alias).toEqual('');
    expect(attribute.hasDefaultValue).toBeFalsy();
    expect(attribute.defaultValue).toEqual('');
    expect(attribute.hasIndex).toBeFalsy();
    expect(attribute.indexName).toBeUndefined();
    expect(attribute.isDiscriminator).toBeTruthy();

    const intAttributeJson = attribute.dumpToGSQLJson();
    expect(intAttributeJson.AttributeName).toEqual('my_attribute');
    expect(intAttributeJson.AttributeType.Name).toEqual('INT');
    expect(intAttributeJson.IsDiscriminator).toBeTruthy();
    expect(Object.keys(intAttributeJson).length).toEqual(3);
  });

  it('should contain attribute index flag and index name', () => {
    const mockAttributeWithIndex: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'INT'
      },
      HasIndex: true,
      IndexName: 'my_index'
    };
    const mockAttributeWithoutIndex: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'UINT'
      },
      HasIndex: false
    };

    const attributeWithIndex = new Attribute();
    attributeWithIndex.loadFromGSQLJson(mockAttributeWithIndex);
    const attributeWithoutIndex = new Attribute();
    attributeWithoutIndex.loadFromGSQLJson(mockAttributeWithoutIndex);

    expect(attributeWithIndex.name).toEqual('my_attribute');
    expect(attributeWithIndex.type.name).toEqual('INT');
    expect(attributeWithIndex.hasNullable).toBeFalsy();
    expect(attributeWithIndex.nullable).toBeFalsy();
    expect(attributeWithIndex.hasAlias).toBeFalsy();
    expect(attributeWithIndex.alias).toEqual('');
    expect(attributeWithIndex.hasDefaultValue).toBeFalsy();
    expect(attributeWithIndex.defaultValue).toEqual('');
    expect(attributeWithIndex.hasIndex).toBeTruthy();
    expect(attributeWithIndex.indexName).toEqual('my_index');

    expect(attributeWithoutIndex.name).toEqual('my_attribute');
    expect(attributeWithoutIndex.type.name).toEqual('UINT');
    expect(attributeWithoutIndex.hasNullable).toBeFalsy();
    expect(attributeWithoutIndex.nullable).toBeFalsy();
    expect(attributeWithoutIndex.hasAlias).toBeFalsy();
    expect(attributeWithoutIndex.alias).toEqual('');
    expect(attributeWithoutIndex.hasDefaultValue).toBeFalsy();
    expect(attributeWithoutIndex.defaultValue).toEqual('');
    expect(attributeWithoutIndex.hasIndex).toBeFalsy();
    expect(attributeWithoutIndex.indexName).toBeUndefined();

    const attributeJSONWithIndex = attributeWithIndex.dumpToGSQLJson();
    expect(attributeJSONWithIndex.HasIndex).toBeTruthy();
    expect(attributeJSONWithIndex.IndexName).toEqual('my_index');

    const attributeJSONWithoutIndex = attributeWithoutIndex.dumpToGSQLJson();
    expect(attributeJSONWithoutIndex.HasIndex).toBeFalsy();
    expect(attributeJSONWithoutIndex.IndexName).toBeUndefined();
  });

  it('should fail validation because of invalid default value.', () => {
    // invalid double
    const mockDoubleAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'DOUBLE'
      },
      Nullable: true,
      DefaultValue: 'src'
    };

    let attribute = new Attribute();
    attribute.loadFromGSQLJson(mockDoubleAttribute);

    let result = attribute.semanticCheck();
    expect(result.success).toBeFalsy();
    expect(result.message).toBe('Attribute "my_attribute"\'s default value "src" ' +
      'is invalid DOUBLE.');

    // invalid int
    const mockIntAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'INT'
      },
      Nullable: true,
      DefaultValue: 'src'
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttribute);

    result = attribute.semanticCheck();
    expect(result.success).toBeFalsy();
    expect(result.message).toBe('Attribute "my_attribute"\'s default value "src" ' +
      'is invalid INT.');

    // invalid uint
    const mockUIntAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'UINT'
      },
      Nullable: true,
      DefaultValue: '-2'
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockUIntAttribute);

    result = attribute.semanticCheck();
    expect(result.success).toBeFalsy();
    expect(result.message).toBe('Attribute "my_attribute"\'s default value "-2" ' +
      'is invalid UINT.');

    // invalid bool
    const mockBoolAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'BOOL'
      },
      Nullable: true,
      DefaultValue: '-2'
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockBoolAttribute);

    result = attribute.semanticCheck();
    expect(result.success).toBeFalsy();
    expect(result.message).toBe('Attribute "my_attribute"\'s default value "-2" ' +
      'is invalid BOOL.');

    // invalid datetime
    const mockDatetimeAttribute: GSQLAttributeJson = {
      AttributeName: 'my_attribute',
      AttributeType: {
        Name: 'DATETIME'
      },
      Nullable: true,
      DefaultValue: 'src'
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockDatetimeAttribute);

    result = attribute.semanticCheck();
    expect(result.success).toBeFalsy();
    expect(result.message).toBe('Attribute "my_attribute"\'s default value "src" ' +
      'is invalid DATETIME.');
  });

  describe('should fail validation because of unneeded default value', () => {
    it('with LIST type attribute', () => {
      const mockListAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'LIST',
        },
        DefaultValue: 'yes'
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockListAttribute);

      const result = attribute.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.message).toEqual('LIST cannot have default value.');
    });

    it('with SET type attribute', () => {
      const mockSetAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'SET',
        },
        DefaultValue: 'yes'
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockSetAttribute);

      const result = attribute.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.message).toEqual('SET cannot have default value.');
    });

    it('with MAP type attribute', () => {
      const mockMapAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'MAP',
        },
        DefaultValue: 'yes'
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockMapAttribute);

      const result = attribute.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.message).toEqual('MAP cannot have default value.');
    });

    it('with UDT type attribute', () => {
      const mockUdtAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'UDT',
        },
        DefaultValue: 'yes'
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockUdtAttribute);

      const result = attribute.semanticCheck();
      expect(result.success).toBeFalsy();
      expect(result.message).toEqual('UDT cannot have default value.');
    });
  });

  it('should not pass semantic check because it is invalid attribute name.', () => {
    const mockIntAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttributeInvalidName);
    attribute.name = '#wrong name';

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toBe('Invalid attribute name "#wrong name". ' +
      'Valid name starts with a letter, followed by letters or digits.',
      'Construct attribute error');
  });

  it('should not pass semantic check because it is invalid attribute type.', () => {
    const mockIntAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttributeInvalidName);
    attribute.type.name = '';

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toBe('Please select attribute type for ' +
      'attribute "name".');
  });

  it('should not pass semantic check because it is invalid discriminator attribute type.', () => {
    let mockIntAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'SET'
      },
      IsDiscriminator: true
    };

    let attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttributeInvalidName);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toBe('Please select discriminator attribute type for ' +
      'attribute "name".');

    mockIntAttributeInvalidName = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'MAP'
      },
      IsDiscriminator: true
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttributeInvalidName);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toBe('Please select discriminator attribute type for ' +
      'attribute "name".');

    mockIntAttributeInvalidName = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'BOOL'
      },
      IsDiscriminator: true
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockIntAttributeInvalidName);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toBe('Please select discriminator attribute type for ' +
      'attribute "name".');
  });

  describe('should not pass semantic check since no container value type', () => {
    it('with LIST type attribute', () => {
      const mockListAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'LIST'
        }
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockListAttribute);

      expect(attribute.semanticCheck().success).toBeFalsy();
      expect(attribute.semanticCheck().message).toEqual('Please select value type for LIST attribute "attr".');
    });

    it('with SET type attribute', () => {
      const mockSetAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'SET'
        }
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockSetAttribute);

      expect(attribute.semanticCheck().success).toBeFalsy();
      expect(attribute.semanticCheck().message).toEqual('Please select value type for SET attribute "attr".');
    });

    it('with MAP type attribute', () => {
      const mockMapAttribute: GSQLAttributeJson = {
        AttributeName: 'attr',
        AttributeType: {
          Name: 'MAP'
        }
      };

      const attribute = new Attribute();
      attribute.loadFromGSQLJson(mockMapAttribute);

      expect(attribute.semanticCheck().success).toBeFalsy();
      expect(attribute.semanticCheck().message).toEqual('Please select value type for MAP attribute "attr".');
    });
  });

  it('should not pass semantic check since MAP attibute has no key type', () => {
    const mockMapAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'MAP',
        ValueTypeName: 'STRING'
      }
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockMapAttribute);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toEqual('Please select key type for MAP attribute "attr".');
  });

  it('shoud not pass semantic check since UDT attribute has not sepcified the tuple name', () => {
    const mockUdtAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'UDT'
      }
    };

    const attribute = new Attribute();
    attribute.loadFromGSQLJson(mockUdtAttribute);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toEqual('Please select user defined tuple name.');
  });

  it('should not pass semantic check since UDT value type attribute has not specified the tuple name', () => {
    // MAP type attribute
    const mockMapAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'MAP',
        KeyTypeName: 'INT',
        ValueTypeName: 'UDT'
      }
    };

    let attribute = new Attribute();
    attribute.loadFromGSQLJson(mockMapAttribute);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toEqual('Please select user defined value tuple name.');

    // List type attribute
    const mockListAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'LIST',
        ValueTypeName: 'UDT'
      }
    };

    attribute = new Attribute();
    attribute.loadFromGSQLJson(mockListAttribute);

    expect(attribute.semanticCheck().success).toBeFalsy();
    expect(attribute.semanticCheck().message).toEqual('Please select user defined value tuple name.');
  });

  it('should load from and dump to attribute correctly', () => {
    const mockIntAttributeName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      },
      Alias: 'a'
    };

    const attribute = new Attribute().loadFromGSQLJson(mockIntAttributeName);
    expect(attribute.hasAlias).toBeTruthy();
    expect(attribute.alias).toBe('a');

    const attributeJson = attribute.dumpToGSQLJson();
    expect(attributeJson.Alias).toBe('a');
  });

  it('should pass semantic check.', () => {
    // Int attribute
    const mockIntAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      },
      DefaultValue: '-3'
    };
    const attributeInt = new Attribute().loadFromGSQLJson(mockIntAttributeInvalidName);
    expect(attributeInt.semanticCheck().success).toBeTruthy();

    // Uint attribute
    const mockUintAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'UINT'
      },
      DefaultValue: '3'
    };
    const attributeUint = new Attribute().loadFromGSQLJson(mockUintAttributeInvalidName);
    expect(attributeUint.semanticCheck().success).toBeTruthy();

    // Float attribute
    const mockFloatAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'FLOAT'
      },
      DefaultValue: '3.5'
    };
    const attributeFloat = new Attribute().loadFromGSQLJson(mockFloatAttributeInvalidName);
    expect(attributeFloat.semanticCheck().success).toBeTruthy();

    // Bool attribute
    const mockBoolAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'BOOL'
      },
      DefaultValue: 'true'
    };
    const attributeBool = new Attribute().loadFromGSQLJson(mockBoolAttributeInvalidName);
    expect(attributeBool.semanticCheck().success).toBeTruthy();

    // Datetime attribute
    const mockDatetimeAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'DATETIME'
      },
      DefaultValue: '2017-10-12 11:22:33'
    };
    const attributeDate = new Attribute().loadFromGSQLJson(mockDatetimeAttributeInvalidName);
    expect(attributeDate.semanticCheck().success).toBeTruthy();

    // String attribute
    const mockStringAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      },
      DefaultValue: 'src'
    };
    const attributeString = new Attribute().loadFromGSQLJson(mockStringAttributeInvalidName);
    expect(attributeString.semanticCheck().success).toBeTruthy();

    // Discriminator attributes
    let mockDiscriminatorAttributeInvalidName: GSQLAttributeJson = {
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      },
      DefaultValue: 'src',
      IsDiscriminator: true
    };
    let discriminatorAttr = new Attribute().loadFromGSQLJson(mockDiscriminatorAttributeInvalidName);
    expect(discriminatorAttr.semanticCheck().success).toBeTruthy();
    mockDiscriminatorAttributeInvalidName = {
      AttributeName: 'number',
      AttributeType: {
        Name: 'INT'
      },
      DefaultValue: '0',
      IsDiscriminator: true
    };
    discriminatorAttr = new Attribute().loadFromGSQLJson(mockDiscriminatorAttributeInvalidName);
    expect(discriminatorAttr.semanticCheck().success).toBeTruthy();
    mockDiscriminatorAttributeInvalidName = {
      AttributeName: 'number',
      AttributeType: {
        Name: 'UINT'
      },
      DefaultValue: '0',
      IsDiscriminator: true
    };
    discriminatorAttr = new Attribute().loadFromGSQLJson(mockDiscriminatorAttributeInvalidName);
    expect(discriminatorAttr.semanticCheck().success).toBeTruthy();
    mockDiscriminatorAttributeInvalidName = {
      AttributeName: 'date',
      AttributeType: {
        Name: 'DATETIME'
      },
      DefaultValue: '2020-01-01',
      IsDiscriminator: true
    };
    discriminatorAttr = new Attribute().loadFromGSQLJson(mockDiscriminatorAttributeInvalidName);
    expect(discriminatorAttr.semanticCheck().success).toBeTruthy();

    // List attribute
    const mockListAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'LIST',
        ValueTypeName: 'STRING'
      }
    };

    const attributeList = new Attribute().loadFromGSQLJson(mockListAttribute);
    expect(attributeList.semanticCheck().success).toBeTruthy();

    // List attribute with UDT type value
    const mockListAttribute1: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'LIST',
        ValueTypeName: 'UDT',
        ValueTypeTupleName: 'testTuple'
      }
    };

    const attributeList1 = new Attribute().loadFromGSQLJson(mockListAttribute);
    expect(attributeList1.semanticCheck().success).toBeTruthy();

    // Set attribute
    const mockSetAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'SET',
        ValueTypeName: 'STRING'
      }
    };

    const attributeSet = new Attribute().loadFromGSQLJson(mockSetAttribute);
    expect(attributeSet.semanticCheck().success).toBeTruthy();

    // Set attribute with UDT type value
    const mockSetAttribute1: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'SET',
        ValueTypeName: 'UDT',
        ValueTypeTupleName: 'testTuple'
      }
    };

    const attributeSet1 = new Attribute().loadFromGSQLJson(mockSetAttribute1);
    expect(attributeSet1.semanticCheck().success).toBeTruthy();

    // MAP attribute
    const mockMapAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'MAP',
        KeyTypeName: 'INT',
        ValueTypeName: 'STRING'
      }
    };

    const attributeMap = new Attribute().loadFromGSQLJson(mockMapAttribute);
    expect(attributeMap.semanticCheck().success).toBeTruthy();

    // MAP attribute with UDT type value
    const mockMapAttribute1: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'MAP',
        KeyTypeName: 'INT',
        ValueTypeName: 'UDT',
        ValueTypeTupleName: 'testTuple'
      }
    };

    const attributeMap1 = new Attribute().loadFromGSQLJson(mockMapAttribute1);
    expect(attributeMap1.semanticCheck().success).toBeTruthy();

    // UDT attribute
    const mockUdtAttribute: GSQLAttributeJson = {
      AttributeName: 'attr',
      AttributeType: {
        Name: 'UDT',
        TupleName: 'testTuple'
      }
    };

    const attributeUdt = new Attribute().loadFromGSQLJson(mockUdtAttribute);
    expect(attributeUdt.semanticCheck().success).toBeTruthy();
  });
});
