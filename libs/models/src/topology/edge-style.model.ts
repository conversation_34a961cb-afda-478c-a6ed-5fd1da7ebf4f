/**
 * Edge style json representation in database.
 *
 * @export
 * @interface DBEdgeStyleJson
 */
export interface DBEdgeStyleJson {
  fillColor: string;
  other: {[key: string]: any};
}

/**
 * EdgeStyle describes the style of an edge type, including it's color etc.
 *
 * @export
 * @class EdgeStyle
 */
export class EdgeStyle {
  fillColor: string;
  other: {[key: string]: any};

  /**
   * Creates an instance of EdgeStyle.
   *
   * @memberof EdgeStyle
   */
  constructor() {
    this.fillColor = undefined;
    this.other = {};
  }

  /**
   * Clone edge style.
   *
   * @returns {EdgeStyle}
   * @memberof EdgeStyle
   */
  clone(): EdgeStyle {
    const edgeStyle = new EdgeStyle();
    edgeStyle.fillColor = this.fillColor;
    if (this.other) {
      edgeStyle.other = JSON.parse(JSON.stringify(this.other));
    } else {
      edgeStyle.other = {};
    }
    return edgeStyle;
  }

  /**
   * Set properties from DB edge style json.
   *
   * @param {DBEdgeStyleJson} edgeStyle
   * @returns {EdgeStyle}
   * @memberof EdgeStyle
   */
  loadFromDBJson(edgeStyle: DBEdgeStyleJson): EdgeStyle {
    this.fillColor = edgeStyle.fillColor;
    this.other = edgeStyle.other;
    return this;
  }

  /**
   * Dump EdgeStyle into DB edge style json representation.
   *
   * @returns {DBEdgeStyleJson}
   * @memberof EdgeStyle
   */
  dumpToDBJson(): DBEdgeStyleJson {
    return {
      fillColor: this.fillColor,
      other: this.other
    };
  }
}
