import { GSQLAttributeJson } from './attribute.model';
import { GSQLEdgeJson } from './edge.model';
import { GSQLVertexJson } from './vertex.model';

/**
 * The schema change type.
 * Raw type: Initial type that contains both global and local vertex and edge types.
 * Global: Global schema change job.
 * Local: Local schema change job.
 *
 * @export
 * @enum {number}
 */
export enum SchemaChangeType {
  Raw = 'raw',
  Global = 'global',
  Local = 'local'
}

export interface AlterVertexAttributeIndex {
  attributeName: string;
  indexName: string;
}

/**
 * Altering one vertex or edge type.
 * Supported changes:
 * (1. Rename the vertex or edge (not supported in GSQL yet))
 * 2. Drop attributes.
 * 3. Add new attributes.
 *
 * @export
 * @interface AlterVertexOrEdge
 */
export interface AlterVertexOrEdge {
  name: string;
  // rename?: string;
  dropAttributes: string[];
  addAttributes: GSQLAttributeJson[];
  dropIndexAttributes: AlterVertexAttributeIndex[];
  addIndexAttributes: AlterVertexAttributeIndex[];
}

export interface GraphRelatedGlobalSchemaChange {
  graphName: string;
  dropVertexTypes: string[];
  dropEdgeTypes: string[];
  addVertexTypes: string[];
  addEdgeTypes: string[];
}

/**
 * Schema change contains three sections:
 * 1. Alter edge types.
 * 2. Alter vertex types.
 * 3. Drop old edge types.
 * 4. Drop old vertex types.
 * 5. Add new vertex types.
 * 6. Add new edge types.
 * The sections follow the above order. Here we assume if we alter a vertex or edge type,
 * it won't be dropped (otherwise it should only appear in dropping list, not in altering list)
 *
 * @export
 * @interface SchemaChange
 */
export interface SchemaChange {
  alterEdgeTypes: AlterVertexOrEdge[];
  alterVertexTypes: AlterVertexOrEdge[];
  dropEdgeTypes: string[];
  dropVertexTypes: string[];
  addVertexTypes: GSQLVertexJson[];
  addEdgeTypes: GSQLEdgeJson[];
  schemaChangeType?: string;
  graphs?: GraphRelatedGlobalSchemaChange[];
}
