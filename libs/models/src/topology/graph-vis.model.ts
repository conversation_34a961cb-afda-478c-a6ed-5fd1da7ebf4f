import { DBEdgeStyleJson } from './edge-style.model';
import { DBVertexStyleJson } from './vertex-style.model';

/**
 * Visualization of one node in Graph Chart.
 *
 * @export
 * @interface NodeVis
 */
export interface NodeVis {
  attributes: {
    [attributeName: string]: string | number | boolean
  };
  vertex_id: string;
  vertex_type: string;
  style?: DBVertexStyleJson;
  other: {
    [otherInfoKey: string]: any
  };
}

/**
 * Visualization of one link in Graph Chart.
 *
 * @export
 * @interface LinkVis
 */
export interface LinkVis {
  attributes: {
    [attributeName: string]: string | number | boolean
  };
  edge_type: string;
  directed: boolean;
  from_id: string;
  from_type: string;
  to_id: string;
  to_type: string;
  style?: DBEdgeStyleJson;
  other: {
    [otherInfoKey: string]: any
  };
}

/**
 * Visualization of the graph in Graph Chart.
 *
 * @export
 * @interface GraphVis
 */
export interface GraphVis {
  nodes: NodeVis[];
  links: LinkVis[];
}
