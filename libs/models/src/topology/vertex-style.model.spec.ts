import { VertexStyle, DBVertexStyleJson } from './vertex-style.model';

describe('VertexStyleModel', () => {

  it('should successfully load from and dump into DBVertexStyleJson', () => {
    const mockVertexStyle: DBVertexStyleJson = {
      fillColor: '#123456',
      x: 0.3,
      y: 0.45,
      icon: 'img-001',
      other: {}
    };

    const vertexStyle = new VertexStyle();
    vertexStyle.loadFromDBJson(mockVertexStyle);

    expect(vertexStyle.fillColor).toEqual('#123456');
    expect(vertexStyle.x).toEqual(0.3);
    expect(vertexStyle.y).toEqual(0.45);
    expect(vertexStyle.icon).toEqual('img-001');

    const vertexStyleJson = vertexStyle.dumpToDBJson();
    expect(vertexStyleJson.fillColor).toEqual('#123456');
    expect(vertexStyleJson.x).toEqual(0.3);
    expect(vertexStyleJson.y).toEqual(0.45);
    expect(vertexStyleJson.icon).toEqual('img-001');
  });

  it('should successfully load from and dump into DBVertexStyleJson without icon', () => {
    const mockVertexStyle: DBVertexStyleJson = {
      fillColor: '#123456',
      x: 0.3,
      y: 0.45,
      other: {}
    };

    const vertexStyle = new VertexStyle();
    vertexStyle.loadFromDBJson(mockVertexStyle);

    expect(vertexStyle.fillColor).toEqual('#123456');
    expect(vertexStyle.x).toEqual(0.3);
    expect(vertexStyle.y).toEqual(0.45);
    expect(vertexStyle.icon).toBeUndefined();

    const vertexStyleJson = vertexStyle.dumpToDBJson();
    expect(vertexStyleJson.fillColor).toEqual('#123456');
    expect(vertexStyleJson.x).toEqual(0.3);
    expect(vertexStyleJson.y).toEqual(0.45);
    expect(vertexStyleJson.icon).toBeUndefined();
  });

  it('should clone vertex style correctly', () => {
    const mockVertexStyle: DBVertexStyleJson = {
      fillColor: '#123456',
      x: 0.3,
      y: 0.45,
      other: {
        icon: '124.png'
      }
    };

    const vertexStyle = new VertexStyle();
    vertexStyle.loadFromDBJson(mockVertexStyle);

    const cloneVertexStyleWithOther = vertexStyle.clone();
    expect(cloneVertexStyleWithOther.fillColor).toBe('#123456');
    expect(cloneVertexStyleWithOther.other['icon']).toBe('124.png');

    vertexStyle.other = undefined;
    const cloneVertexStyleWithoutOther = vertexStyle.clone();
    expect(cloneVertexStyleWithoutOther.fillColor).toBe('#123456');
    expect(JSON.stringify(cloneVertexStyleWithoutOther.other)).toBe('{}');
  });
});
