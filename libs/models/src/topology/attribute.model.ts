import { GraphStudioError } from '../error';
import { FormatValidator, ValidateResult } from '../utils';

import {
  AttributeType,
  GSQLAttributeTypeJson,
  supportedAttributeTypes,
  supportedContainerValueTypes,
  supportedDiscriminatorAttributeTypes,
  supportedMapKeyTypes
} from './attribute-type.model';

/**
 * GSQL Attribute json representation interface following the format:
 *   {
 *     "AttributeName": string,
 *     "AttributeType": GSQLAttributeTypeJson,
 *     ["Nullable": boolean],
 *     ["DefaultValue": string],
 *     ["Alias": string]
 *   }
 *
 * @export
 * @interface GSQLAttributeJson
 */
export interface GSQLAttributeJson {
  AttributeName: string;
  AttributeType: GSQLAttributeTypeJson;
  Nullable?: boolean;
  DefaultValue?: string;
  Alias?: string;
  IsPrimaryKey?: boolean;
  IsPartOfCompositeKey?: boolean;
  PrimaryIdAsAttribute?: boolean;
  HasIndex?: boolean;
  IndexName?: string;
  IsUsingNewSyntax?: boolean;
  IsDiscriminator?: boolean;
}

/**
 * Attribute describes a vertex attribute or edge attribute, including the
 * attribute's name, type and additional properties like default value and alias.
 *
 * @export
 * @class Attribute
 */
export class Attribute {
  name: string;
  type: AttributeType;
  hasNullable: boolean;
  nullable: boolean;
  hasDefaultValue: boolean;
  defaultValue: string;
  hasAlias: boolean;
  alias: string;
  isPrimaryKey: boolean;
  isPartOfCompositeKey: boolean;
  primaryIdAsAttribute: boolean;
  hasIndex: boolean;
  isUsingNewSyntax: boolean;
  indexName?: string;
  isDiscriminator: boolean;

  /**
   * Creates an instance of Attribute.
   *
   * @memberof Attribute
   */
  constructor() {
    this.name = '';
    this.type = new AttributeType();
    this.hasNullable = this.hasDefaultValue = this.hasAlias = this.nullable = false;
    this.defaultValue = this.alias = '';
    this.isPrimaryKey = this.isPartOfCompositeKey = this.primaryIdAsAttribute = false;
    this.hasIndex = false;
    this.isUsingNewSyntax = false;
    this.isDiscriminator = false;
  }

  /**
   * Clone the attribute.
   *
   * @returns {Attribute}
   * @memberof Attribute
   */
  clone(): Attribute {
    const attribute = new Attribute();
    attribute.name = this.name;
    attribute.type = this.type.clone();
    attribute.hasNullable = this.hasNullable;
    attribute.nullable = this.nullable;
    attribute.hasDefaultValue = this.hasDefaultValue;
    attribute.defaultValue = this.defaultValue;
    attribute.hasAlias = this.hasAlias;
    attribute.alias = this.alias;
    attribute.isPrimaryKey = this.isPrimaryKey;
    attribute.isPartOfCompositeKey = this.isPartOfCompositeKey;
    attribute.primaryIdAsAttribute = this.primaryIdAsAttribute;
    attribute.isUsingNewSyntax = this.isUsingNewSyntax;
    attribute.hasIndex = this.hasIndex;
    attribute.indexName = this.indexName;
    attribute.isDiscriminator = this.isDiscriminator;
    return attribute;
  }

  /**
   * Load Attribute from GSQL Attribute json representation.
   *
   * @param {GSQLAttributeJson} attributeJson GSQL Attribute json representation
   * @returns {Attribute}
   * @memberof Attribute
   */
  loadFromGSQLJson(attributeJson: GSQLAttributeJson): Attribute {
    // AttributeName
    this.name = attributeJson.AttributeName;
    const validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      throw new GraphStudioError(
        `Invalid attribute name "${this.name}". ` +
        validateName.message
      );
    }

    // AttributeType
    this.type.loadFromGSQLJson(attributeJson.AttributeType);

    // Nullable
    if ('Nullable' in attributeJson) {
      this.hasNullable = true;
      this.nullable = attributeJson.Nullable;
    } else {
      this.hasNullable = this.nullable = false;
    }
    // DefaultValue
    if ('DefaultValue' in attributeJson) {
      this.hasDefaultValue = true;
      this.defaultValue = attributeJson.DefaultValue;
    } else {
      this.hasDefaultValue = false;
      this.defaultValue = '';
    }
    // Alias
    if ('Alias' in attributeJson) {
      this.hasAlias = true;
      this.alias = attributeJson.Alias;
    } else {
      this.hasAlias = false;
      this.alias = '';
    }

    // IsPrimaryKey
    if ('IsPrimaryKey' in attributeJson) {
      this.isPrimaryKey = attributeJson.IsPrimaryKey;
    }

    // IsPartOfCompositeKey
    if ('IsPartOfCompositeKey' in attributeJson) {
      this.isPartOfCompositeKey = attributeJson.IsPartOfCompositeKey;
    }

    // PrimaryIdAsAttribute
    if ('PrimaryIdAsAttribute' in attributeJson) {
      this.primaryIdAsAttribute = attributeJson.PrimaryIdAsAttribute;
    }

    // Vertex created using PRIMARY KEY or COMPOSITE KEY.
    if ('IsUsingNewSyntax' in attributeJson) {
      this.isUsingNewSyntax = true;
    }

    if ('HasIndex' in attributeJson) {
      this.hasIndex = attributeJson.HasIndex;
      if (this.hasIndex) {
        this.indexName = attributeJson.IndexName;
      }
    }

    if ('IsDiscriminator' in attributeJson) {
      this.isDiscriminator = attributeJson.IsDiscriminator;
    }

    return this;
  }

  /**
   * Dump Attribute into GSQL Attribute json representation.
   *
   * @returns {GSQLAttributeJson}
   * @memberof Attribute
   */
  dumpToGSQLJson(): GSQLAttributeJson {
    const attributeJson: GSQLAttributeJson = {
      AttributeName: this.name,
      AttributeType: this.type.dumpToGSQLJson()
    };
    if (this.hasNullable) {
      attributeJson.Nullable = this.nullable;
    }
    if (this.hasDefaultValue) {
      attributeJson.DefaultValue = this.defaultValue;
    }
    if (this.hasAlias) {
      attributeJson.Alias = this.alias;
    }
    if (this.isPrimaryKey) {
      attributeJson.IsPrimaryKey = this.isPrimaryKey;
    }
    if (this.isPartOfCompositeKey) {
      attributeJson.IsPartOfCompositeKey = this.isPartOfCompositeKey;
    }
    if (this.primaryIdAsAttribute) {
      attributeJson.PrimaryIdAsAttribute = this.primaryIdAsAttribute;
    }
    if (this.isUsingNewSyntax) {
      attributeJson.IsUsingNewSyntax = this.isUsingNewSyntax;
    }
    if (this.hasIndex) {
      attributeJson.HasIndex = this.hasIndex;
      if (this.indexName) {
        attributeJson.IndexName = this.indexName;
      }
    }
    if (this.isDiscriminator) {
      attributeJson.IsDiscriminator = this.isDiscriminator;
    }
    return attributeJson;
  }

  /**
   * Semantic check to be valid attribute.
   *
   * @returns {ValidateResult}
   * @memberof Edge
   */
  semanticCheck(): ValidateResult {
    // 1. Attribute name must be valid name.
    const validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      return {
        success: false,
        message: `Invalid attribute name "${this.name}". ${validateName.message}`
      };
    }
    // 2. Attribute type must be valid type.
    if (this.isDiscriminator) {
      if (!supportedDiscriminatorAttributeTypes.includes(this.type.name)) {
        return {
          success: false,
          message: `Please select discriminator attribute type for attribute "${this.name}".`
        };
      }
    } else {
      if (!supportedAttributeTypes.includes(this.type.name)) {
        return {
          success: false,
          message: `Please select attribute type for attribute "${this.name}".`
        };
      }
    }

    // 3. Cannot have unsupported key type name and value type name.
    if (
      this.type.hasKeyTypeName &&
      !supportedMapKeyTypes.includes(this.type.keyTypeName)
    ) {
      return {
        success: false,
        message: `${this.type.keyTypeName} is unsupported map key type now.`
      };
    }
    if (
      this.type.hasValueTypeName &&
      !supportedContainerValueTypes.includes(this.type.valueTypeName)
    ) {
      return {
        success: false,
        message: `${this.type.valueTypeName} is unsupported attribute container value type now.`
      };
    }
    // 4. Default value validation check
    if (this.hasDefaultValue) {
      if (this.type.name === 'INT') {
        // 1. Validate INT attribute default value.
        const validateDefault = FormatValidator.isInt(this.defaultValue);
        if (!validateDefault.success) {
          return this.returnDefaultValueValidationFailureResult();
        }
      } else if (this.type.name === 'UINT') {
        // 2. Validate UINT attribute default value.
        const validateDefault = FormatValidator.isUInt(this.defaultValue);
        if (!validateDefault.success) {
          return this.returnDefaultValueValidationFailureResult();
        }
      } else if (this.type.name === 'DOUBLE' || this.type.name === 'FLOAT') {
        // 3. Validate real number attribute default value.
        const validateDefault = FormatValidator.isReal(this.defaultValue);
        if (!validateDefault.success) {
          return this.returnDefaultValueValidationFailureResult();
        }
      } else if (this.type.name === 'BOOL') {
        // 4. Validate BOOL attribute default value.
        const validateDefault = FormatValidator.isBoolean(this.defaultValue);
        if (!validateDefault.success) {
          return this.returnDefaultValueValidationFailureResult();
        }
      } else if (this.type.name === 'DATETIME') {
        // 5. Validate DATETIME attribute default value.
        const validateDefault = FormatValidator.isDatetime(this.defaultValue);
        if (!validateDefault.success) {
          return this.returnDefaultValueValidationFailureResult();
        }
      } else if (['LIST', 'SET', 'MAP', 'UDT'].includes(this.type.name)) {
        // 6. List, Set, Map or UDT attribute cannot have default value.
        if (this.hasDefaultValue) {
          return {
            success: false,
            message: `${this.type.name} cannot have default value.`
          };
        }
      }
    }
    // 5. Container type attributes must have container value type.
    if (['LIST', 'SET', 'MAP'].includes(this.type.name)) {
      if (!this.type.hasValueTypeName) {
        return {
          success: false,
          message: `Please select value type for ${this.type.name} attribute "${this.name}".`
        };
      }
    }
    // 6. Map type attributes must have key type.
    if (this.type.name === 'MAP' && !this.type.hasKeyTypeName) {
      return {
        success: false,
        message: `Please select key type for ${this.type.name} attribute "${this.name}".`
      };
    }
    // 7. UDT type attributes must specify the UDT name.
    if (this.type.name === 'UDT' && !this.type.hasTupleName) {
      return {
        success: false,
        message: `Please select user defined tuple name.`
      };
    }
    // 8. UDT value type of a container type attributes must specify the UDT name.
    if (this.type.valueTypeName === 'UDT' && !this.type.hasValueTypeTupleName) {
      return {
        success: false,
        message: `Please select user defined value tuple name.`
      };
    }
    return {
      success: true
    };
  }

  /**
   * Construct default value validation failure result.
   *
   * @private
   * @returns {ValidateResult}
   * @memberof Attribute
   */
  private returnDefaultValueValidationFailureResult(): ValidateResult {
    return {
      success: false,
      message: `Attribute "${this.name}"\'s default value "${this.defaultValue}" ` +
      `is invalid ${this.type.name}.`
    };
  }
}
