/**
 * Vertex style json representation in database.
 *
 * @export
 * @interface DBVertexStyleJson
 */
export interface DBVertexStyleJson {
  fillColor: string;
  x: number;
  y: number;
  icon?: string;
  other: {[key: string]: any};
}

/**
 * VertexStyle describes the style of a vertex type, including it's color,
 * coordinates, etc.
 *
 * @export
 * @class VertexStyle
 */
export class VertexStyle {
  fillColor: string;
  x: number;
  y: number;
  icon: string;
  other: {[key: string]: any};

  /**
   * Creates an instance of VertexStyle.
   *
   * @memberof VertexStyle
   */
  constructor() {
    this.fillColor = this.x = this.y = this.icon = undefined;
    this.other = {};
  }

  /**
   * Clone vertex style.
   *
   * @returns {VertexStyle}
   * @memberof VertexStyle
   */
  clone(): VertexStyle {
    const vertexStyle = new VertexStyle();
    vertexStyle.fillColor = this.fillColor;
    vertexStyle.x = this.x;
    vertexStyle.y = this.y;
    vertexStyle.icon = this.icon;
    if  (this.other) {
      vertexStyle.other = JSON.parse(JSON.stringify(this.other));
    } else {
      vertexStyle.other = {};
    }
    return vertexStyle;
  }

  /**
   * Set properties from DB vertex style json.
   *
   * @param {DBVertexStyleJson} vertexStyle
   * @returns {VertexStyle}
   * @memberof VertexStyle
   */
  loadFromDBJson(vertexStyle: DBVertexStyleJson): VertexStyle {
    this.fillColor = vertexStyle.fillColor;
    this.x = vertexStyle.x;
    this.y = vertexStyle.y;
    this.icon = vertexStyle.icon;
    this.other = vertexStyle.other;
    return this;
  }

  /**
   * Dump VertexStyle into DB vertex style json representation.
   *
   * @returns {DBVertexStyleJson}
   * @memberof VertexStyle
   */
  dumpToDBJson(): DBVertexStyleJson {
    return this.icon ? {
      fillColor: this.fillColor,
      x: this.x,
      y: this.y,
      icon: this.icon,
      other: this.other
    } : {
      fillColor: this.fillColor,
      x: this.x,
      y: this.y,
      other: this.other
    };
  }
}
