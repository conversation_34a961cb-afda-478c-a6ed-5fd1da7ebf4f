import { GraphStudioError } from '../error';

import { Edge, GSQLEdgeJson } from './edge.model';
import { EdgeStyle } from './edge-style.model';

describe('EdgeModel', () => {
  let mockNormalEdge: GSQLEdgeJson;

  beforeEach(() => {
    mockNormalEdge = {
      Name: 'name',
      FromVertexTypeName: 'v1',
      ToVertexTypeName: 'v2',
      IsDirected: true,
      Config: {
        REVERSE_EDGE: 'rev_edge'
      },
      Attributes: [
        {
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        },
        {
          AttributeName: 'attr3',
          AttributeType: {
            Name: 'INT'
          },
          IsDiscriminator: true
        }
      ],
      DiscriminatorCount: 1,
      CompositeDiscriminator: ['attr3'],
      DiscriminatorList: ['attr3']
    };
  });

  it('should test loading from GSQL Json.', () => {
    const edge = new Edge().loadFromGSQLJson(mockNormalEdge);
    expect(edge.fromToVertexTypePairs[0].from).toEqual(mockNormalEdge.FromVertexTypeName);
    expect(edge.fromToVertexTypePairs[0].to).toEqual(mockNormalEdge.ToVertexTypeName);
    expect(edge.directed).toEqual(mockNormalEdge.IsDirected);
    edge.attributes.forEach((attribute, index) => {
      expect(attribute.name).toEqual(mockNormalEdge.Attributes[index].AttributeName);
      expect(attribute.type.name).toEqual(mockNormalEdge.Attributes[index].AttributeType.Name);
      mockNormalEdge.Attributes[index].IsDiscriminator ?
        expect(attribute.isDiscriminator).toBeTruthy() :
        expect(attribute.isDiscriminator).toBeFalsy();
    });
    expect(edge.hasReverseEdge).toBeTruthy();
    expect(edge.reverseEdge).toEqual(mockNormalEdge.Config.REVERSE_EDGE);
    expect(edge.isLocal).toBeFalsy();
    expect(edge.discriminatorCount).toEqual(mockNormalEdge.DiscriminatorCount);
    expect(edge.compositeDiscriminator).toEqual(mockNormalEdge.CompositeDiscriminator);
    expect(edge.discriminatorList).toEqual(mockNormalEdge.DiscriminatorList);
    expect(edge.usage).toEqual(mockNormalEdge.Usage);

    mockNormalEdge.EdgePairs = [{
      From: mockNormalEdge.FromVertexTypeName,
      To: mockNormalEdge.ToVertexTypeName
    }];
    mockNormalEdge.Config = {};
    mockNormalEdge.IsLocal = true;
    mockNormalEdge.DiscriminatorCount = undefined;
    mockNormalEdge.CompositeDiscriminator = undefined;
    mockNormalEdge.DiscriminatorList = undefined;
    const edge2 = new Edge().loadFromGSQLJson(mockNormalEdge);

    expect(edge2.fromToVertexTypePairs[0].from).toEqual(mockNormalEdge.FromVertexTypeName);
    expect(edge2.fromToVertexTypePairs[0].to).toEqual(mockNormalEdge.ToVertexTypeName);
    expect(edge2.hasReverseEdge).toBeFalsy();
    expect(edge2.reverseEdge).toEqual('');
    expect(edge2.isLocal).toBeTruthy();
    expect(edge2.discriminatorCount).toEqual(0);
    expect(edge2.compositeDiscriminator).toEqual([]);
  });

  it('should throw error because it is invalid edge name.', () => {
    const mockEdgeInvalidName: GSQLEdgeJson = {
      Name: '#wrong name',
      FromVertexTypeName: 'v1',
      ToVertexTypeName: 'v2',
      IsDirected: false,
      Attributes: [],
      Config: {}
    };

    const edge = new Edge();
    expect(() => edge.loadFromGSQLJson(mockEdgeInvalidName))
    .toThrow(new GraphStudioError(`Invalid edge name "#wrong name". ` +
      'Valid name starts with a letter, followed by letters or digits.'));
  });

  it('should throw error because attribute name is same with edge type name.', () => {
    const mockEdgeInvalidName: GSQLEdgeJson = {
      Name: 'attr',
      FromVertexTypeName: 'v1',
      ToVertexTypeName: 'v2',
      IsDirected: false,
      Config: {},
      Attributes: [
        {
          AttributeName: 'id',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        }
      ]
    };

    const edge = new Edge();
    expect(() => edge.loadFromGSQLJson(mockEdgeInvalidName))
    .toThrow(new GraphStudioError('Edge type name "attr" cannot be used as its attributes\' names.'));
  });

  it('should throw error because attributes have duplicated names.', () => {
    const mockEdgeInvalidName: GSQLEdgeJson = {
      Name: 'name',
      FromVertexTypeName: 'v1',
      ToVertexTypeName: 'v2',
      IsDirected: false,
      Config: {},
      Attributes: [
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr',
          AttributeType: {
            Name: 'INT'
          }
        }
      ]
    };

    const edge = new Edge();
    expect(() => edge.loadFromGSQLJson(mockEdgeInvalidName))
    .toThrow(new GraphStudioError('More than one attributes have name "attr".'));
  });

  it('should dump to json correctly', () => {
    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge);

    expect(edge.name).toEqual('name');
    expect(edge.attributes.length).toEqual(3);
    expect(edge.attributes[0].name).toEqual('attr1');
    expect(edge.attributes[1].name).toEqual('attr2');
    expect(edge.attributes[2].name).toEqual('attr3');
    expect(edge.fromToVertexTypePairs[0].from).toEqual('v1');
    expect(edge.fromToVertexTypePairs[0].to).toEqual('v2');
    expect(edge.directed).toBeTruthy();
    expect(edge.hasReverseEdge).toBeTruthy();
    expect(edge.reverseEdge).toEqual('rev_edge');
    expect(edge.discriminatorCount).toEqual(1);
    expect(edge.compositeDiscriminator).toEqual(['attr3']);
    expect(edge.discriminatorList).toEqual(['attr3']);

    const edgeJson = edge.dumpToGSQLJson();
    expect(edgeJson.Name).toEqual('name');
    expect(edgeJson.FromVertexTypeName).toEqual('v1');
    expect(edgeJson.ToVertexTypeName).toEqual('v2');
    expect(edgeJson.IsDirected).toBeTruthy();
    expect(edgeJson.Attributes.length).toEqual(3);
    expect(edgeJson.Attributes[0].AttributeName).toEqual('attr1');
    expect(edgeJson.Attributes[1].AttributeName).toEqual('attr2');
    expect(edgeJson.Attributes[2].AttributeName).toEqual('attr3');
    expect(Object.keys(edgeJson.Config).length).toEqual(1);
    expect(edgeJson.Config['REVERSE_EDGE']).toEqual('rev_edge');
    expect(edgeJson.DiscriminatorCount).toEqual(1);
    expect(edgeJson.CompositeDiscriminator).toEqual(['attr3']);
    expect(edgeJson.DiscriminatorList).toEqual(['attr3']);
  });

  it('should set discriminator properties correctly', () => {
    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge);
    edge.discriminatorCount = 0;
    edge.discriminatorList = [];
    edge.compositeDiscriminator = [];
    edge.setDiscriminatorProperties();

    expect(edge.name).toEqual('name');
    expect(edge.attributes.length).toEqual(3);
    expect(edge.attributes[0].name).toEqual('attr1');
    expect(edge.attributes[1].name).toEqual('attr2');
    expect(edge.attributes[2].name).toEqual('attr3');
    expect(edge.fromToVertexTypePairs[0].from).toEqual('v1');
    expect(edge.fromToVertexTypePairs[0].to).toEqual('v2');
    expect(edge.directed).toBeTruthy();
    expect(edge.hasReverseEdge).toBeTruthy();
    expect(edge.reverseEdge).toEqual('rev_edge');
    expect(edge.discriminatorCount).toEqual(1);
    expect(edge.compositeDiscriminator).toEqual(['attr3']);
    expect(edge.discriminatorList).toEqual(['attr3']);
  });

  it('should get edge vis without style', () => {

    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge);

    const edgeVis = edge.getVis();
    expect(Object.keys(edgeVis[0].attributes).length).toBe(4);
    expect(edgeVis[0].attributes['reverse edge']).not.toBeUndefined();
    expect(edgeVis[0].edge_type).toBe('name');
    expect(edgeVis[0].directed).toBeTruthy();
    expect(edgeVis[0].from_id).toBe('v1');
    expect(edgeVis[0].from_type).toBe('v1');
    expect(edgeVis[0].to_id).toBe('v2');
    expect(edgeVis[0].to_type).toBe('v2');
    expect(edgeVis[0].style).toBeUndefined();
  });

  it('should get edge vis with style', () => {

    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge);
    edge.style = new EdgeStyle();
    edge.style.fillColor = '#123456';

    const edgeVis = edge.getVis();
    expect(Object.keys(edgeVis[0].attributes).length).toBe(4);
    expect(edgeVis[0].attributes['reverse edge']).not.toBeUndefined();
    expect(edgeVis[0].edge_type).toBe('name');
    expect(edgeVis[0].directed).toBeTruthy();
    expect(edgeVis[0].from_id).toBe('v1');
    expect(edgeVis[0].from_type).toBe('v1');
    expect(edgeVis[0].to_id).toBe('v2');
    expect(edgeVis[0].to_type).toBe('v2');
    expect(edgeVis[0].style.fillColor).toBe('#123456');
  });

  it('should clone a new edge that is not same instance as old one', () => {

    const oldEdge = new Edge();
    oldEdge.loadFromGSQLJson(mockNormalEdge);
    const newEdge = oldEdge.clone();
    newEdge.directed = false;
    newEdge.attributes[1].name = 'newName';

    expect(oldEdge.directed).toBeTruthy();
    expect(newEdge.directed).toBeFalsy();
    expect(oldEdge.attributes[1].name).toBe('attr2');
    expect(newEdge.attributes[1].name).toBe('newName');
  });

  it('should not pass semantic check because attribute name is invalid', () => {
    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge);
    edge.attributes[0].name = '#wrong name';

    expect(edge.semanticCheck().success).toBeFalsy();
    expect(edge.semanticCheck().message).toBe('Invalid attribute name "#wrong name". ' +
    'Valid name starts with a letter, followed by letters or digits.');
  });

  it('should remove REVERSE_EDGE in edge config', () => {
    const mockNormalEdge1: GSQLEdgeJson = {
      Name: 'name',
      FromVertexTypeName: 'v1',
      ToVertexTypeName: 'v2',
      IsDirected: true,
      Config: {
        REVERSE_EDGE: 'rev_edge'
      },
      Attributes: [
        {
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'INT'
          }
        },
        {
          AttributeName: 'attr2',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        }
      ]
    };

    expect(Object.keys(mockNormalEdge1.Config).length).toBe(1);

    const edge = new Edge();
    edge.loadFromGSQLJson(mockNormalEdge1);
    edge.hasReverseEdge = false;
    edge.reverseEdge = '';

    const edgeJson = edge.dumpToGSQLJson();
    expect(Object.keys(edgeJson.Config).length).toBe(0);
  });
});
