import { Color } from '../gvis';

import { GraphStudioError } from '../error';

import { EdgeStyle } from './edge-style.model';
import { Graph, GSQLGraphJson } from './graph.model';
import { GraphStyle, DBGraphStyleJson } from './graph-style.model';
import { Vertex } from './vertex.model';
import { VertexStyle } from './vertex-style.model';

describe('GraphModel', () => {

  it('should throw error because it is invalid graph name.', () => {
    const mockGraphInvalidName: GSQLGraphJson = {
      GraphName: '#wrong name',
      VertexTypes: [],
      EdgeTypes: []
    };

    const graph = new Graph();
    expect(() => graph.loadFromGSQLJson(mockGraphInvalidName))
    .toThrow(new GraphStudioError(`Invalid graph name "#wrong name". ` +
      'Valid name starts with a letter, followed by letters or digits.'));
  });

  it('should throw error because vertex or edge has same name as graph.', () => {
    let mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'graph_name',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: []
    };

    const graph = new Graph();
    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Graph name cannot be used as vertex name or edge name.'));

    mockGraph = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'graph_name',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Graph name cannot be used as vertex name or edge name.'));
  });

  it('should throw error because vertex or edge has duplicated name.', () => {
    let mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: []
    };

    const graph = new Graph();
    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Vertex or edge name "v1" cannot be duplicated in one graph.'));

    mockGraph = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'v2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Vertex or edge name "v2" cannot be duplicated in one graph.'));

    mockGraph = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'has',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'has',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Vertex or edge name "has" cannot be duplicated in one graph.'));

    mockGraph = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'has',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {
            REVERSE_EDGE: 'reverse_has'
          },
          Attributes: []
        },
        {
          Name: 'reverse_has',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Vertex or edge name "reverse_has" cannot be duplicated in one graph.'));
  });

  it('should throw error because edge source vertex or target vertex is not in graph.', () => {
    let mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v3',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Edge source vertex type "v3" is not in graph.'));

    mockGraph = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: '*',
          ToVertexTypeName: 'v4',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    expect(() => graph.loadFromGSQLJson(mockGraph))
    .toThrow(new GraphStudioError('Edge target vertex type "v4" is not in graph.'));
  });

  it('should dump to json correctly', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: '*',
          IsDirected: false,
          Config: {},
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    graph.loadFromGSQLJson(mockGraph);

    expect(graph.name).toEqual('graph_name');
    expect(graph.vertexTypes.length).toEqual(2);
    expect(graph.vertexTypes[0].name).toEqual('v1');
    expect(graph.vertexTypes[1].name).toEqual('v2');
    expect(graph.edgeTypes.length).toEqual(2);
    expect(graph.edgeTypes[0].name).toEqual('e1');
    expect(graph.edgeTypes[1].name).toEqual('e2');

    const graphJson = graph.dumpToGSQLJson();
    expect(graphJson.GraphName).toEqual('graph_name');
    expect(graphJson.VertexTypes.length).toEqual(2);
    expect(graphJson.EdgeTypes.length).toEqual(2);
  });

  it('should convert to graph vis correctly', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: '*',
          IsDirected: false,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e3',
          FromVertexTypeName: '*',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e4',
          FromVertexTypeName: '*',
          ToVertexTypeName: '*',
          IsDirected: false,
          Config: {},
          Attributes: []
        },
      ]
    };

    const graph = new Graph();
    graph.loadFromGSQLJson(mockGraph);
    const graphVis = graph.getVis();

    expect(graphVis.nodes.length).toBe(2);
    expect(graphVis.nodes[0].vertex_id).toBe('v1');
    expect(graphVis.nodes[1].vertex_type).toBe('v2');
    expect(graphVis.links.length).toBe(9);
    expect(graphVis.links[0].from_id).toBe('v1');
    expect(graphVis.links[0].to_type).toBe('v2');
    expect(graphVis.links[1].from_id).toBe('v2');
    expect(graphVis.links[1].to_type).toBe('v1');
    expect(graphVis.links[2].from_id).toBe('v2');
    expect(graphVis.links[2].to_type).toBe('v2');
    expect(graphVis.links[3].from_id).toBe('v1');
    expect(graphVis.links[3].to_type).toBe('v1');
    expect(graphVis.links[4].from_id).toBe('v2');
    expect(graphVis.links[4].to_type).toBe('v1');
    expect(graphVis.links[5].from_id).toBe('v1');
    expect(graphVis.links[5].to_type).toBe('v1');
    expect(graphVis.links[6].from_id).toBe('v1');
    expect(graphVis.links[6].to_type).toBe('v2');
    expect(graphVis.links[7].from_id).toBe('v2');
    expect(graphVis.links[7].to_type).toBe('v1');
    expect(graphVis.links[8].from_id).toBe('v2');
    expect(graphVis.links[8].to_type).toBe('v2');
  });

  it('should get new edge style correctly', () => {
    const graph = new Graph();
    const color = new Color();
    spyOn(color, 'getColor').and.returnValue('#123456');
    const edgeStyle = graph.getNewEdgeStyle('edgeName', color);
    expect(color.getColor).toHaveBeenCalled();
    expect(edgeStyle.fillColor).toBe('#123456');
  });

  it('should get new vertex style at position (0, 0) if no position specified in any vertex type', () => {
    const graph = new Graph();
    const color = new Color();
    spyOn(color, 'getColor').and.returnValue('#123456');
    const vertexWithoutStyle = new Vertex();
    graph.vertexTypes.push(vertexWithoutStyle);
    const vertexStyle = graph.getNewVertexStyle('vertexName', color);
    expect(color.getColor).toHaveBeenCalled();
    expect(vertexStyle.fillColor).toBe('#123456');
    expect(vertexStyle.x).toBe(0);
    expect(vertexStyle.y).toBe(0);
  });

  it('should get new vertex style at the right of all the vertices', () => {
    const graph = new Graph();
    const color = new Color();
    spyOn(color, 'getColor').and.returnValue('#123456');
    const vertex1 = new Vertex();
    const vertexStyle1 = new VertexStyle();
    vertexStyle1.x = 0;
    vertexStyle1.y = 100;
    vertex1.style = vertexStyle1;
    const vertex2 = new Vertex();
    const vertexStyle2 = new VertexStyle();
    vertexStyle2.x = 100;
    vertexStyle2.y = 0;
    vertex2.style = vertexStyle2;
    graph.vertexTypes.push(vertex1);
    graph.vertexTypes.push(vertex2);
    const vertexStyle = graph.getNewVertexStyle('vertexName', color);
    expect(color.getColor).toHaveBeenCalled();
    expect(vertexStyle.fillColor).toBe('#123456');
    expect(vertexStyle.x).toBe(250);
    expect(vertexStyle.y).toBe(50);
  });

  it('should apply style to graph, and retrieve style from graph correctly', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: '*',
          IsDirected: false,
          Config: {},
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    graph.loadFromGSQLJson(mockGraph);

    const mockGraphStyle: DBGraphStyleJson = {
      vertexStyles: {
        'v1': {
          fillColor: '#123456',
          x: 0.3,
          y: 0.45,
          icon: 'img-001',
          other: {}
        },
        'v2': {
          fillColor: '#abcdef',
          x: -1,
          y: -0.5,
          icon: 'img-002',
          other: {}
        }
      },
      edgeStyles: {
        'e2': {
          fillColor: '#ffffff',
          other: {}
        }
      }
    };

    const graphStyle = new GraphStyle();
    graphStyle.loadFromDBJson(mockGraphStyle);

    graph.applyGraphStyle(graphStyle);

    expect(graph.vertexTypes[0].style.x).toBe(0.3);
    expect(graph.vertexTypes[0].style.y).toBe(0.45);
    expect(graph.vertexTypes[0].style.fillColor).toBe('#123456');
    expect(graph.vertexTypes[0].style.icon).toBe('img-001');
    expect(graph.vertexTypes[1].style.x).toBe(-1);
    expect(graph.vertexTypes[1].style.y).toBe(-0.5);
    expect(graph.vertexTypes[1].style.fillColor).toBe('#abcdef');
    expect(graph.vertexTypes[1].style.icon).toBe('img-002');
    expect(graph.edgeTypes[0].style).toBeUndefined();
    expect(graph.edgeTypes[1].style.fillColor).toBe('#ffffff');

    graph.applyVertexStyle('v2', new VertexStyle()
    .loadFromDBJson({x: 1, y: 1, fillColor: '#000000', icon: 'img-003', other: {}}));
    expect(graph.vertexTypes[1].style.x).toBe(1);
    expect(graph.vertexTypes[1].style.y).toBe(1);
    expect(graph.vertexTypes[1].style.fillColor).toBe('#000000');
    expect(graph.vertexTypes[1].style.icon).toBe('img-003');

    graph.applyEdgeStyle('e1', new EdgeStyle()
    .loadFromDBJson({fillColor: '#654321', other: {}}));

    const newGraphStyle = graph.getGraphStyle();
    expect(newGraphStyle.vertexStyles.size).toBe(2);
    expect(newGraphStyle.edgeStyles.size).toBe(2);
    expect(newGraphStyle.edgeStyles.get('e1').fillColor).toBe('#654321');
    expect(newGraphStyle.edgeStyles.get('e2').fillColor).toBe('#ffffff');
    expect(newGraphStyle.vertexStyles.get('v1').x).toBe(0.3);
    expect(newGraphStyle.vertexStyles.get('v1').y).toBe(0.45);
    expect(newGraphStyle.vertexStyles.get('v1').fillColor).toBe('#123456');
    expect(newGraphStyle.vertexStyles.get('v1').icon).toBe('img-001');
    expect(newGraphStyle.vertexStyles.get('v2').x).toBe(1);
    expect(newGraphStyle.vertexStyles.get('v2').y).toBe(1);
    expect(newGraphStyle.vertexStyles.get('v2').fillColor).toBe('#000000');
    expect(newGraphStyle.vertexStyles.get('v2').icon).toBe('img-003');
  });

  it('should actually clone one graph that can call methods', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    graph.loadFromGSQLJson(mockGraph);

    const graph2 = graph.clone();

    expect(graph2.vertexTypes.length).toBe(2);
    expect(graph2.edgeTypes.length).toBe(1);
    expect(graph2.vertexTypes[0].name).toBe('v1');
    expect(graph2.vertexTypes[1].name).toBe('v2');
    expect(graph2.edgeTypes[0].name).toBe('e1');
    expect(graph2.dumpToGSQLJson()).toBeTruthy();
  });

  it('should get correct vertex and edge', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {
            'REVERSE_EDGE': 'reverse_e2'
          },
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    graph.loadFromGSQLJson(mockGraph);

    const v1 = graph.getVertex('v1');
    expect(v1.name).toBe('v1');
    const e1 = graph.getEdge('e1');
    expect(e1.fromToVertexTypePairs[0].to).toBe('v2');
    const e2 = graph.getEdge('reverse_e2', true);
    expect(e2.name).toEqual('e2');
    expect(e2.hasReverseEdge).toBe(true);
    expect(e2.reverseEdge).toEqual('reverse_e2');
    expect(e2.attributes).toEqual([]);
    expect(e2.fromToVertexTypePairs[0].from).toBe('v1');
    expect(e2.fromToVertexTypePairs[0].to).toBe('v2');
    expect(graph.getVertex('v3')).toBeUndefined();
    expect(graph.getEdge('e3')).toBeUndefined();
    expect(graph.getAllVertexTypes().length).toBe(2);
    expect(graph.getAllVertexTypes()[0]).toBe('v1');
    expect(graph.getAllVertexTypes()[1]).toBe('v2');
    expect(graph.getAllEdgeTypes().length).toBe(2);
    expect(graph.getAllEdgeTypes()[0]).toBe('e1');
  });

  it('should semantic check error because of invalid default value', () => {
    const mockGraph: GSQLGraphJson = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'my_attribute',
              AttributeType: {
                Name: 'DATETIME'
              },
              Nullable: true,
              DefaultValue: 'src'
            }
          ],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    const graph = new Graph();
    expect(() => graph.loadFromGSQLJson(mockGraph)).toThrow(
      new GraphStudioError('Attribute "my_attribute"\'s ' +
      'default value "src" is invalid DATETIME.'));
  });

  describe('should fail semantic check', () => {
    let graph: Graph;

    beforeEach(() => {
      const mockGraph: GSQLGraphJson = {
        GraphName: 'graph_name',
        VertexTypes: [
          {
            Name: 'v1',
            PrimaryId: {
              AttributeName: 'attr',
              AttributeType: {
                Name: 'INT'
              }
            },
            Attributes: [],
            Config: {}
          }
        ],
        EdgeTypes: [
          {
            Name: 'e1',
            FromVertexTypeName: 'v1',
            ToVertexTypeName: 'v1',
            IsDirected: true,
            Config: {},
            Attributes: []
          }
        ]
      };

      graph = new Graph().loadFromGSQLJson(mockGraph);
    });

    it('because of invalid vertex name', () => {
      graph.getVertex('v1').name = 'wrong name';
      expect(graph.semanticCheck().message).toBe(
        'Invalid vertex name "wrong name". Valid name starts with a letter, followed by letters or digits.');
    });

    it('because of invalid edge name', () => {
      graph.getEdge('e1').name = 'wrong name';
      expect(graph.semanticCheck().message).toBe(
        'Invalid edge name "wrong name". Valid name starts with a letter, followed by letters or digits.');
    });
  });

});
