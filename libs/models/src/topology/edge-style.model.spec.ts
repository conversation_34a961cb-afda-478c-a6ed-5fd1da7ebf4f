import { EdgeStyle, DBEdgeStyleJson } from './edge-style.model';

describe('EdgeStyleModel', () => {

  it('should successfully load from and dump into DBEdgeStyleJson', () => {
    const mockEdgeStyle: DBEdgeStyleJson = {
      fillColor: '#123456',
      other: {}
    };

    const edgeStyle = new EdgeStyle();
    edgeStyle.loadFromDBJson(mockEdgeStyle);

    expect(edgeStyle.fillColor).toEqual('#123456');

    const edgeStyleJson = edgeStyle.dumpToDBJson();
    expect(edgeStyleJson.fillColor).toEqual('#123456');
  });

  it('should clone vertex style correctly', () => {
    const mockEdgeStyle: DBEdgeStyleJson = {
      fillColor: '#123456',
      other: {
        stroke: '2px'
      }
    };

    const edgeStyle = new EdgeStyle();
    edgeStyle.loadFromDBJson(mockEdgeStyle);

    const cloneEdgeStyleWithOther = edgeStyle.clone();
    expect(cloneEdgeStyleWithOther.fillColor).toBe('#123456');
    expect(cloneEdgeStyleWithOther.other['stroke']).toBe('2px');

    edgeStyle.other = undefined;
    const cloneEdgeStyleWithoutOther = edgeStyle.clone();
    expect(cloneEdgeStyleWithoutOther.fillColor).toBe('#123456');
    expect(JSON.stringify(cloneEdgeStyleWithoutOther.other)).toBe('{}');
  });
});
