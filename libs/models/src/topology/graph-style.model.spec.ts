import { GraphStyle, DBGraphStyleJson } from './graph-style.model';

describe('GraphStyleModel', () => {

  it('should successfully load from and dump into DBGraphStyleJson', () => {
    const mockGraphStyle: DBGraphStyleJson = {
      vertexStyles: {
        'v1': {
          fillColor: '#123456',
          x: 0.3,
          y: 0.45,
          icon: 'img-001',
          other: {}
        },
        'v2': {
          fillColor: '#abcdef',
          x: -1,
          y: -0.5,
          icon: 'img-002',
          other: {}
        },
        'v3': {
          fillColor: '#0f0f0f',
          x: 7,
          y: 6,
          other: {}
        }
      },
      edgeStyles: {
        'e1': {
          fillColor: '#ffffff',
          other: {}
        }
      }
    };

    const graphStyle = new GraphStyle();
    graphStyle.loadFromDBJson(mockGraphStyle);

    expect(graphStyle.vertexStyles.size).toBe(3);
    expect(graphStyle.edgeStyles.size).toBe(1);
    expect(graphStyle.vertexStyles.get('v1').fillColor).toBe('#123456');
    expect(graphStyle.vertexStyles.get('v1').x).toBe(0.3);
    expect(graphStyle.vertexStyles.get('v1').y).toBe(0.45);
    expect(graphStyle.vertexStyles.get('v1').icon).toBe('img-001');
    expect(graphStyle.vertexStyles.get('v2').fillColor).toBe('#abcdef');
    expect(graphStyle.vertexStyles.get('v2').x).toBe(-1);
    expect(graphStyle.vertexStyles.get('v2').y).toBe(-0.5);
    expect(graphStyle.vertexStyles.get('v2').icon).toBe('img-002');
    expect(graphStyle.vertexStyles.get('v3').fillColor).toBe('#0f0f0f');
    expect(graphStyle.vertexStyles.get('v3').x).toBe(7);
    expect(graphStyle.vertexStyles.get('v3').y).toBe(6);
    expect(graphStyle.vertexStyles.get('v3').icon).toBeUndefined();
    expect(graphStyle.edgeStyles.get('e1').fillColor).toBe('#ffffff');

    const graphStyleJson: DBGraphStyleJson = graphStyle.dumpToDBJson();
    expect(Object.keys(graphStyleJson.vertexStyles).length).toBe(3);
    expect(Object.keys(graphStyleJson.edgeStyles).length).toBe(1);
    expect(graphStyleJson.vertexStyles['v1'].fillColor).toBe('#123456');
    expect(graphStyleJson.vertexStyles['v1'].x).toBe(0.3);
    expect(graphStyleJson.vertexStyles['v1'].y).toBe(0.45);
    expect(graphStyleJson.vertexStyles['v1'].icon).toBe('img-001');
    expect(graphStyleJson.vertexStyles['v2'].fillColor).toBe('#abcdef');
    expect(graphStyleJson.vertexStyles['v2'].x).toBe(-1);
    expect(graphStyleJson.vertexStyles['v2'].y).toBe(-0.5);
    expect(graphStyleJson.vertexStyles['v2'].icon).toBe('img-002');
    expect(graphStyleJson.vertexStyles['v3'].fillColor).toBe('#0f0f0f');
    expect(graphStyleJson.vertexStyles['v3'].x).toBe(7);
    expect(graphStyleJson.vertexStyles['v3'].y).toBe(6);
    expect(graphStyleJson.vertexStyles['v3'].icon).toBeUndefined();
    expect(graphStyleJson.edgeStyles['e1'].fillColor).toBe('#ffffff');
  });
});
