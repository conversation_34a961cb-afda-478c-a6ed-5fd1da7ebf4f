import { DBEdgeStyleJson, EdgeStyle } from './edge-style.model';
import { DBVertexStyleJson, VertexStyle } from './vertex-style.model';

/**
 * Graph style representation in database.
 *
 * @export
 * @interface DBGraphStyleJson
 */
export interface DBGraphStyleJson {
  vertexStyles: { [vertexType: string]: DBVertexStyleJson };
  edgeStyles: { [edgeType: string]: DBEdgeStyleJson };
}

/**
 * GraphStyle describes the style of a graph, including the style of it's vertex
 * types and edge types.
 *
 * @export
 * @class GraphStyle
 */
export class GraphStyle {
  vertexStyles: Map<string, VertexStyle>;
  edgeStyles: Map<string, EdgeStyle>;

  /**
   * Creates an instance of GraphStyle.
   *
   * @memberof GraphStyle
   */
  constructor() {
    this.vertexStyles = new Map<string, VertexStyle>();
    this.edgeStyles = new Map<string, EdgeStyle>();
  }

  /**
   * Set properties from DB graph style json.
   *
   * @param {DBGraphStyleJson} graphStyle
   * @returns {GraphStyle}
   * @memberof GraphStyle
   */
  loadFromDBJson(graphStyle: DBGraphStyleJson): GraphStyle {
    Object.keys(graphStyle.vertexStyles).forEach(name => {
      const vStyleJson = graphStyle.vertexStyles[name];
      const vStyle = new VertexStyle();
      vStyle.loadFromDBJson(vStyleJson);
      this.vertexStyles.set(name, vStyle);
    });

    Object.keys(graphStyle.edgeStyles).forEach(name => {
      const eStyleJson = graphStyle.edgeStyles[name];
      const eStyle = new EdgeStyle();
      eStyle.loadFromDBJson(eStyleJson);
      this.edgeStyles.set(name, eStyle);
    });

    return this;
  }

  /**
   * Dump GraphStyle into DB graph style json representation.
   *
   * @returns {DBGraphStyleJson}
   * @memberof GraphStyle
   */
  dumpToDBJson(): DBGraphStyleJson {
    const vStyles: { [vertexType: string]: DBVertexStyleJson } = {};
    const eStyles: { [edgeType: string]: DBEdgeStyleJson } = {};

    this.vertexStyles.forEach((vStyle, name) => {
      vStyles[name] = vStyle.dumpToDBJson();
    });

    this.edgeStyles.forEach((eStyle, name) => {
      eStyles[name] = eStyle.dumpToDBJson();
    });

    return {
      vertexStyles: vStyles,
      edgeStyles: eStyles
    };
  }
}
