import { Color } from '../gvis';

import { GraphStudioError } from '../error';
import { FormatValidator, GLOBAL_GRAPH_NAME, ValidateResult } from '../utils';

import { Edge, GSQLEdgeJson } from './edge.model';
import { EdgeStyle } from './edge-style.model';
import { GraphStyle } from './graph-style.model';
import { GraphVis } from './graph-vis.model';
import { GSQLVertexJson, Vertex } from './vertex.model';
import { VertexStyle } from './vertex-style.model';

/**
 * GSQL Graph json representation interface following the format:
 *   {
 *     "GraphName": string,
 *     "VertexTypes": GSQLVertexJson[],
 *     "EdgeTypes": GSQLEdgeJson[]
 *   }
 *
 * @export
 * @interface GSQLGraphJson
 */
export interface GSQLGraphJson {
  GraphName: string;
  VertexTypes: GSQLVertexJson[];
  EdgeTypes: GSQLEdgeJson[];
}

export interface TypeCheckedStatus {
  vertexOrEdgeType: GSQLVertexJson | GSQLEdgeJson;
  selected: boolean;
  disabled?: boolean;
}

/**
 * Graph describes graph schema in TigerGraph system, including the graph's name,
 * vertex types and edge types.
 *
 * @export
 * @class Graph
 */
export class Graph {
  name: string;
  vertexTypes: Vertex[];
  edgeTypes: Edge[];

  /**
   * Creates an instance of Graph.
   *
   * @memberof Graph
   */
  constructor() {
    this.name = 'MyGraph';
    this.vertexTypes = [];
    this.edgeTypes = [];
  }

  /**
   * Clone the graph.
   *
   * @returns {Graph}
   * @memberof Graph
   */
  clone(): Graph {
    const graph = new Graph();
    graph.name = this.name;
    this.vertexTypes.forEach((vertexType) => {
      graph.vertexTypes.push(vertexType.clone());
    });
    this.edgeTypes.forEach((edgeType) => {
      graph.edgeTypes.push(edgeType.clone());
    });
    return graph;
  }

  /**
   * Load Graph from GSQL Graph json representation.
   *
   * @param {GSQLGraphJson} graphJson GSQL Graph json representation
   * @returns {Graph}
   * @memberof Graph
   */
  loadFromGSQLJson(graphJson: GSQLGraphJson): Graph {
    // GraphName
    this.name = graphJson.GraphName;

    // VertexTypes
    this.vertexTypes = (graphJson.VertexTypes).map((v) => {
      const vertex = new Vertex();
      vertex.loadFromGSQLJson(v);
      return vertex;
    });

    // EdgeTypes
    this.edgeTypes = (graphJson.EdgeTypes).map((e) => {
      const edge = new Edge();
      edge.loadFromGSQLJson(e);
      return edge;
    });

    // For the edge types with * but without edge pair list, materialize the pairs.
    this.edgeTypes.forEach(edgeType => {
      let fromTypeList = [edgeType.fromToVertexTypePairs[0].from];
      let toTypeList = [edgeType.fromToVertexTypePairs[0].to];
      if (fromTypeList[0] !== '*' && toTypeList[0] !== '*') {
        return;
      }
      let vertexTypes = this.vertexTypes.map(vertexType => vertexType.name);
      if (!edgeType.isLocal) {
        vertexTypes = this.vertexTypes.filter(vt => !vt.isLocal).map(vertexType => vertexType.name);
      }
      if (fromTypeList[0] === '*') {
        fromTypeList = vertexTypes;
      }
      if (toTypeList[0] === '*') {
        toTypeList = vertexTypes;
      }
      edgeType.fromToVertexTypePairs = [];
      for (const fromType of fromTypeList) {
        for (const toType of toTypeList) {
          edgeType.fromToVertexTypePairs.push({ from : fromType, to: toType });
        }
      }
    });

    // Do semantic check
    const semanticResult = this.semanticCheck();
    if (!semanticResult.success) {
      throw new GraphStudioError(semanticResult.message);
    }

    return this;
  }

  /**
   * Dump Graph into GSQL Graph json representation.
   *
   * @returns {GSQLGraphJson}
   * @memberof Graph
   */
  dumpToGSQLJson(): GSQLGraphJson {
    return {
      GraphName: this.name,
      VertexTypes: this.vertexTypes.map((vertex) => vertex.dumpToGSQLJson()),
      EdgeTypes: this.edgeTypes.map((edge) => edge.dumpToGSQLJson())
    };
  }

  /**
   * Get one vertex given it's type name.
   *
   * @param {string} name
   * @returns {Vertex}
   * @memberof Graph
   */
  getVertex(name: string): Vertex {
    for (const vertex of this.vertexTypes) {
      if (vertex.name === name) {
        return vertex;
      }
    }
  }

  /**
   * Get one edge give it's type name. If reverse is true,
   * then check if the schema edge has a reverse edge with the given name,
   * and return the schema edge.
   *
   * @param {string} name
   * @param {boolean} reverse
   * @returns {Edge}
   * @memberof Graph
   */
  getEdge(name: string, reverse?: boolean): Edge {
    for (const edge of this.edgeTypes) {
      if (
        edge.name === name ||
        reverse && edge.hasReverseEdge && edge.reverseEdge === name
      ) {
        return edge;
      }
    }
  }

  /**
   * Get the graph visualization information.
   *
   * @returns {GraphVis}
   * @memberof Graph
   */
  getVis(): GraphVis {
    const graphVis = {
      nodes: [],
      links: []
    };

    // Add all vertex types vis
    this.vertexTypes.forEach((vertexType) => {
      graphVis.nodes.push(vertexType.getVis());
    });

    // Add all edge types vis
    this.edgeTypes.forEach((edgeType) => {
      // update edge vis
      graphVis.links = graphVis.links.concat(edgeType.getVis() );
    });

    return graphVis;
  }

  /**
   * Apply the vertex style to a vertex type.
   *
   * @param {string} typeName
   * @param {VertexStyle} vStyle
   * @memberof Graph
   */
  applyVertexStyle(typeName: string, vStyle: VertexStyle) {
    this.vertexTypes.forEach((vertexType) => {
      if (vertexType.name === typeName) {
        vertexType.style = vStyle;
      }
    });
  }

  /**
   * Apply the edge style to an edge type.
   *
   * @param {string} typeName
   * @param {EdgeStyle} eStyle
   * @memberof Graph
   */
  applyEdgeStyle(typeName: string, eStyle: EdgeStyle) {
    this.edgeTypes.forEach((edgeType) => {
      if (edgeType.name === typeName) {
        edgeType.style = eStyle;
      }
    });
  }

  /**
   * Apply the graph style to the graph.
   *
   * @param {GraphStyle} gStyle
   * @memberof Graph
   */
  applyGraphStyle(gStyle: GraphStyle) {
    // Apply vertex styles
    gStyle.vertexStyles.forEach((vStyle, name) => {
      this.applyVertexStyle(name, vStyle);
    });
    // Apply edge styles
    gStyle.edgeStyles.forEach((eStyle, name) => {
      this.applyEdgeStyle(name, eStyle);
    });
  }

  /**
   * Get a new vertex style.
   *
   * @param {string} vertexType
   * @param {Color} color
   * @returns {VertexStyle}
   * @memberof Graph
   */
  getNewVertexStyle(vertexType: string, color: Color): VertexStyle {
    const MAX_NUMBER = 10000000000;
    const vertexStyle = new VertexStyle();
    vertexStyle.other = {};
    vertexStyle.fillColor = color.getColor(vertexType);

    // Get the medium y axis coordinate, and maximal x axis coordination.
    // The newly added vertex will be added to the right of the above position by 150px.
    let minX: number = MAX_NUMBER;
    let minY: number = MAX_NUMBER;
    let maxX: number = -MAX_NUMBER;
    let maxY: number = -MAX_NUMBER;

    this.vertexTypes.forEach(vertex => {
      if (vertex.style !== undefined) {
        minX = Math.min(minX, vertex.style.x);
        minY = Math.min(minY, vertex.style.y);
        maxX = Math.max(maxX, vertex.style.x);
        maxY = Math.max(maxY, vertex.style.y);
      }
    });

    // No vertex position set yet.
    if (minX > maxX) {
      vertexStyle.x = vertexStyle.y = 0;
    } else {
      vertexStyle.x = maxX + 150;
      vertexStyle.y = (minY + maxY) / 2;
    }

    return vertexStyle;
  }

  /**
   * Get a new edge style.
   *
   * @param {string} edgeType
   * @param {Color} color
   * @returns {EdgeStyle}
   * @memberof Graph
   */
  getNewEdgeStyle(edgeType: string, color: Color): EdgeStyle {
    const edgeStyle = new EdgeStyle();
    edgeStyle.other = {};
    edgeStyle.fillColor = color.getColor(edgeType);
    return edgeStyle;
  }

  /**
   * Retrieve graph style out of graph.
   *
   * @returns {GraphStyle}
   * @memberof Graph
   */
  getGraphStyle(): GraphStyle {
    const graphStyle = new GraphStyle();
    this.vertexTypes.forEach((vertexType) => {
      if (vertexType.style !== undefined) {
        graphStyle.vertexStyles.set(vertexType.name, vertexType.style);
      }
    });
    this.edgeTypes.forEach((edgeType) => {
      if (edgeType.style !== undefined) {
        graphStyle.edgeStyles.set(edgeType.name, edgeType.style);
      }
    });
    return graphStyle;
  }

  /**
   * Return all vertex type names.
   *
   * @returns {string[]}
   * @memberof Graph
   */
  getAllVertexTypes(): string[] {
    const vTypeNames: string[] = [];
    this.vertexTypes.forEach((vertexType) => {
      vTypeNames.push(vertexType.name);
    });
    return vTypeNames;
  }

  /**
   * Return all edge type names.
   *
   * @returns {string[]}
   * @memberof Graph
   */
  getAllEdgeTypes(): string[] {
    const eTypeNames: string[] = [];
    this.edgeTypes.forEach((edgeType) => {
      eTypeNames.push(edgeType.name);
    });
    return eTypeNames;
  }

  /**
   * Semantic check graph to be valid graph.
   *
   * @returns {ValidateResult}
   * @memberof Graph
   */
  semanticCheck(): ValidateResult {
    // 1. Invalid graph name.
    if (this.name !== GLOBAL_GRAPH_NAME) {
      /**
       * Since graph name cannot be changed after creation,
       * we can only check name cannot be the GLOBAL_GRAPH_NAME when create graph.
       */
      const validateName = FormatValidator.isName(this.name);
      if (!validateName.success) {
        return {
          success: false,
          message: `Invalid graph name "${this.name}". ${validateName.message}`
        };
      }
    }

    // 2. Semantic check each vertex and edge type.
    const names: Map<string, number> = new Map<string, number>();
    const vertexNames: Set<string> = new Set<string>();

    for (const vertex of this.vertexTypes) {
      vertexNames.add(vertex.name);
      let checkResult = vertex.semanticCheck();
      if (!checkResult.success) {
        return checkResult;
      }
      checkResult = this.checkGraphNameAndEntityNameNoConfliction(names, vertex.name);
      if (!checkResult.success) {
        return checkResult;
      }
    }
    for (const edge of this.edgeTypes) {
      let checkResult = edge.semanticCheck();
      if (!checkResult.success) {
        return checkResult;
      }
      checkResult = this.checkGraphNameAndEntityNameNoConfliction(names, edge.name);
      if (!checkResult.success) {
        return checkResult;
      }
      // Also check reverse edge type name, cannot be duplicated with other types.
      if (edge.hasReverseEdge) {
        checkResult = this.checkGraphNameAndEntityNameNoConfliction(names, edge.reverseEdge);
        if (!checkResult.success) {
          return checkResult;
        }
      }
    }

    for (const edge of this.edgeTypes) {
      // 3. Edge from-vertex and to-vertex should be in schema.
      for (const pair of edge.fromToVertexTypePairs) {
        if (!vertexNames.has(pair.from)) {
          return {
            success: false,
            message: `Edge source vertex type "${pair.from}" is not in graph.`
          };
        }
        if (!vertexNames.has(pair.to)) {
          return {
            success: false,
            message: `Edge target vertex type "${pair.to}" is not in graph.`
          };
        }
      }
      // 4. Edge type pair list cannot be empty.
      if (edge.fromToVertexTypePairs.length === 0) {
        return {
          success: false,
          message: `Edge type doesn't have any source and target vertex type.`
        };
      }
    }

    // success
    return {
      success: true
    };
  }

  /**
   * Helper function to check vertex, edge names and graph name don't have conflicts.
   * - Vertices and edges shouldn't have same name with graph.
   * - Vertices and edges shouldn't have same name with each other.
   *
   * @private
   * @param {Map<string, number>} names
   * @param {string} name
   * @returns {ValidateResult}
   * @memberof Graph
   */
  private checkGraphNameAndEntityNameNoConfliction(
    names: Map<string, number>,
    name: string
  ): ValidateResult {
    if (name === this.name) {
      return {
        success: false,
        message: 'Graph name cannot be used as vertex name or edge name.'
      };
    }

    if (!names.has(name)) {
      names.set(name, 1);
    } else {
      names.set(name, names.get(name) + 1);
    }
    if (names.get(name) > 1) {
      return {
        success: false,
        message: `Vertex or edge name "${name}" cannot be duplicated in one graph.`
      };
    }
    return {
      success: true
    };
  }
}
