import { cloneDeep, uniqBy } from 'lodash';

import { GraphStudioError } from '../error';
import { FormatValidator, ValidateResult } from '../utils';

import { Attribute, GSQLAttributeJson } from './attribute.model';
import { EdgeStyle } from './edge-style.model';
import { LinkVis } from './graph-vis.model';

/**
 * GSQL edge pair is the interface representing one pair of vertex types the
 * edge type connects.
 *
 * @export
 * @interface GSQLEdgePair
 */
export interface GSQLEdgePair {
  From: string;
  To: string;
}

/**
 * GSQL Edge json representation interface following the format:
 *   {
 *     "Name": string,
 *     "FromVertexTypeName": string,
 *     "ToVertexTypeName": string,
 *     "IsDirected": boolean,
 *     "Attributes": GSQLAttributeJson[],
 *     "EdgePairs": GSQLEdgePair[],
 *     "Config": { ... },
 *     "DiscriminatorCount": number,
 *     "CompositeDiscriminator": string[];
 *     "EdgePairs": GSQLEdgePair[];
 *     "IsLocal": boolean;
 *     "Usage": string[];
 *   }
 *
 * @export
 * @interface GSQLEdgeJson
 */
export interface GSQLEdgeJson {
  Name: string;
  FromVertexTypeName: string;
  ToVertexTypeName: string;
  IsDirected: boolean;
  Attributes: GSQLAttributeJson[];
  Config: any;
  DiscriminatorCount?: number;
  CompositeDiscriminator?: string[];
  EdgePairs?: GSQLEdgePair[];
  IsLocal?: boolean;
  Usage?: string[];
  DiscriminatorList?: string[];
}

/**
 * Edge describes an edge type in graph schema, including the edge type's name,
 * 'from' vertex type and 'to' vertex type pairs, directed or not, attributes,
 * the number of discriminator attributes(discriminatorCount),
 * the names of the discriminator attributes(compositeDiscriminator)
 * and so on.
 *
 * @export
 * @class Edge
 */
export class Edge {
  name: string;
  fromToVertexTypePairs: { from: string, to: string }[];
  directed: boolean;
  attributes: Attribute[];
  hasReverseEdge: boolean;
  reverseEdge: string;
  config: any;
  style: EdgeStyle;
  isLocal: boolean;
  usage: string[];
  discriminatorCount: number;
  compositeDiscriminator?: string[];
  discriminatorList?: string[];

  /**
   * Creates an instance of Edge.
   *
   * @memberof Edge
   */
  constructor() {
    this.name = '';
    this.fromToVertexTypePairs = [];
    this.reverseEdge = '';
    this.directed = this.hasReverseEdge = false;
    this.attributes = [];
    this.config = {};
    this.style = undefined;
    this.isLocal = true;
    this.usage = undefined;
    this.discriminatorCount = 0;
    this.compositeDiscriminator = [];
    this.discriminatorList = [];
  }

  /**
   * Clone one edge.
   *
   * @returns {Edge}
   * @memberof Edge
   */
  clone(): Edge {
    const edge = new Edge();
    edge.name = this.name;
    edge.fromToVertexTypePairs = cloneDeep(this.fromToVertexTypePairs);
    edge.directed = this.directed;
    this.attributes.forEach((attribute) => {
      edge.attributes.push(attribute.clone());
    });
    edge.hasReverseEdge = this.hasReverseEdge;
    edge.reverseEdge = this.reverseEdge;
    edge.config = JSON.parse(JSON.stringify(this.config));
    if (this.style !== undefined) {
      edge.style = this.style.clone();
    }
    edge.isLocal = this.isLocal;
    edge.usage = this.usage;
    edge.discriminatorCount = this.discriminatorCount;
    edge.compositeDiscriminator = this.compositeDiscriminator;
    edge.discriminatorList = this.discriminatorList;
    return edge;
  }

  /**
   * Load Edge from GSQL Edge json representation.
   *
   * @param {GSQLEdgeJson} edgeJson GSQL Edge json representation
   * @returns {Edge}
   * @memberof Edge
   */
  loadFromGSQLJson(edgeJson: GSQLEdgeJson): Edge {
    // Name
    this.name = edgeJson.Name;

    // If GSQL edge json doesn't contain edge pair, then from edge type and to edge type
    // have to be both not *.
    if (edgeJson.EdgePairs === undefined) {
      this.fromToVertexTypePairs = [{
        from: edgeJson.FromVertexTypeName,
        to: edgeJson.ToVertexTypeName
      }];
    } else {
      this.fromToVertexTypePairs = edgeJson.EdgePairs.map(pair => {
        return {
          from: pair.From,
          to: pair.To
        };
      });
    }

    // IsDirected
    this.directed = edgeJson.IsDirected;

    // Attributes
    this.attributes = (edgeJson.Attributes).map((attr) => {
      const attribute = new Attribute();
      attribute.loadFromGSQLJson(attr);
      return attribute;
    });

    // Config
    this.config = edgeJson.Config;
    if (this.config && 'REVERSE_EDGE' in this.config) {
      this.hasReverseEdge = true;
      this.reverseEdge = this.config.REVERSE_EDGE;
    } else {
      this.hasReverseEdge = false;
      this.reverseEdge = '';
    }

    // Is local
    this.isLocal = !!edgeJson.IsLocal;

    // Discriminator count
    this.discriminatorCount = edgeJson.DiscriminatorCount || 0;

    if (edgeJson.CompositeDiscriminator !== undefined) {
      this.compositeDiscriminator = edgeJson.CompositeDiscriminator;
      this.discriminatorList = cloneDeep(edgeJson.CompositeDiscriminator);
    }

    if (edgeJson.Usage) {
      this.usage = edgeJson.Usage;
    }

    // Do semantic check
    const semanticResult = this.semanticCheck();
    if (!semanticResult.success) {
      throw new GraphStudioError(semanticResult.message);
    }

    return this;
  }

  /**
   * Dump Edge into GSQL Edge json representation.
   *
   * @returns {GSQLEdgeJson}
   * @memberof Edge
   */
  dumpToGSQLJson(): GSQLEdgeJson {
    const edgeJson: GSQLEdgeJson = {
      Name: this.name,
      FromVertexTypeName: '*',
      ToVertexTypeName: '*',
      IsDirected: this.directed,
      Config: this.config,
      Attributes: this.attributes.map((attr) => attr.dumpToGSQLJson()),
      DiscriminatorCount: this.discriminatorCount
    };

    // If there are more than 1 vertex type pair, keep '*' in the source vertex type
    // and target vertex type, otherwise assign the 1st pair to source and target types.
    if (this.fromToVertexTypePairs.length > 1) {
      edgeJson.EdgePairs = this.fromToVertexTypePairs.map(pair => {
        return {
          From: pair.from,
          To: pair.to
        };
      });
      if (uniqBy(this.fromToVertexTypePairs, 'from').length === 1) {
        edgeJson.FromVertexTypeName = this.fromToVertexTypePairs[0].from;
      }
      if (uniqBy(this.fromToVertexTypePairs, 'to').length === 1) {
        edgeJson.ToVertexTypeName = this.fromToVertexTypePairs[0].to;
      }
    } else if (this.fromToVertexTypePairs.length === 1) {
      edgeJson.FromVertexTypeName = this.fromToVertexTypePairs[0].from;
      edgeJson.ToVertexTypeName = this.fromToVertexTypePairs[0].to;
    }

    // Update config w.r.t. reverse edge
    if (this.hasReverseEdge) {
      edgeJson.Config['REVERSE_EDGE'] = this.reverseEdge;
    } else if ('REVERSE_EDGE' in edgeJson.Config) {
      delete edgeJson.Config['REVERSE_EDGE'];
    }

    if (this.compositeDiscriminator && this.compositeDiscriminator.length > 0) {
      edgeJson.CompositeDiscriminator = this.compositeDiscriminator;
      edgeJson.DiscriminatorList = cloneDeep(this.compositeDiscriminator);
    }

    if (this.discriminatorList && this.discriminatorList.length > 0) {
      edgeJson.DiscriminatorList = this.discriminatorList;
    }

    // Update IsLocal flag
    if (this.isLocal) {
      edgeJson.IsLocal = true;
    }

    if (this.usage) {
      edgeJson.Usage = this.usage;
    }

    return edgeJson;
  }

  /**
   * Set discriminator properties.
   */
  setDiscriminatorProperties() {
    this.discriminatorCount = 0;
    this.discriminatorList = [];
    this.compositeDiscriminator = [];
    this.attributes.forEach(attr => {
      if (attr.isDiscriminator) {
        this.discriminatorList.push(attr.name);
        this.compositeDiscriminator.push(attr.name);
        this.discriminatorCount += 1;
      }
    });
  }

  /**
   * Get the edge visualization information.
   *
   * @returns {LinkVis[]}
   * @memberof Edge
   */
  getVis(): LinkVis[] {
    const vis: LinkVis = {
      attributes: {},
      edge_type: this.name,
      directed: this.directed,
      from_id: '*',
      from_type: '*',
      to_id: '*',
      to_type: '*',
      other: {}
    };
    // attributes name and type
    if (this.config['REVERSE_EDGE']) {
      vis.attributes['reverse edge'] = this.config['REVERSE_EDGE'];
    }
    this.attributes.forEach((attr) => vis.attributes[attr.name] = attr.type.toString());
    // style
    if (this.style !== undefined) {
      vis.style = this.style.dumpToDBJson();
    }

    // Collect all edge vis.
    return this.fromToVertexTypePairs.map(pair => {
      vis.from_id = vis.from_type = pair.from;
      vis.to_id = vis.to_type = pair.to;
      return cloneDeep(vis);
    });
  }

  /**
   * Semantic check edge type to be valid edge type.
   *
   * @returns {ValidateResult}
   * @memberof Edge
   */
  semanticCheck(): ValidateResult {
    // 1. Invalid edge type name.
    const validateName = FormatValidator.isName(this.name);
    if (!validateName.success) {
      return {
        success: false,
        message: `Invalid edge name "${this.name}". ` + validateName.message
      };
    }
    // 2. Attribute name cannot be same with edge type name.
    for (const attr of this.attributes) {
      if (attr.name === this.name) {
        return {
          success: false,
          message: `Edge type name "${this.name}" cannot be used as its attributes' names.`
        };
      }
    }
    // 3. Attribute name cannot be duplicated.
    const names: Set<string> = new Set<string>();
    for (let i = 0; i < this.attributes.length; i++) {
      if (names.has(this.attributes[i].name)) {
        return {
          success: false,
          message: `More than one attributes have name "${this.attributes[i].name}".`
        };
      }
      names.add(this.attributes[i].name);
    }

    // 4. Attribute default value must be valid.
    for (const attribute of this.attributes) {
      const validResult = attribute.semanticCheck();
      if (!validResult.success) {
        return validResult;
      }
    }

    return {
      success: true
    };
  }
}
