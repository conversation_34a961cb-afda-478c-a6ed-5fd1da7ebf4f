import { DataType } from '../data';
import { GraphStudioError } from '../error';

import { AttributeType, GSQLAttributeTypeJson } from './attribute-type.model';

describe('AttributeTypeModel', () => {

  it('should throw error because it is advanced attribute type.', () => {
    const mockAdvancedAttributeType: GSQLAttributeTypeJson = {
      Name: 'FIXED_BINARY',
      FixedSize: 6
    };

    const attributeType = new AttributeType();
    expect(() => attributeType.loadFromGSQLJson(mockAdvancedAttributeType))
      .toThrow(new GraphStudioError('FIXED_BINARY is advanced attribute type not ' +
        'supported in Graph Studio. Please directly use DDL in GSQL CLI to ' +
        'use this attribute type.'));
  });

  it('should contain only INT as attribute name, other attributes are not exist', () => {
    const mockIntAttributeType: GSQLAttributeTypeJson = {
      Name: 'INT'
    };

    const attributeType = new AttributeType();
    attributeType.loadFromGSQLJson(mockIntAttributeType);

    expect(attributeType.name).toEqual('INT');
    expect(attributeType.hasFixedSize).toBeFalsy();
    expect(attributeType.fixedSize).toEqual(-1);
    expect(attributeType.hasTupleName).toBeFalsy();
    expect(attributeType.tupleName).toEqual('');
    expect(attributeType.hasKeyTypeName).toBeFalsy();
    expect(attributeType.keyTypeName).toEqual('');
    expect(attributeType.hasValueTypeTupleName).toBeFalsy();
    expect(attributeType.valueTypeTupleName).toEqual('');

    const intAttributeTypeJson = attributeType.dumpToGSQLJson();
    expect(intAttributeTypeJson.Name).toEqual('INT');
    expect(Object.keys(intAttributeTypeJson).length).toEqual(1);
  });

  it('should load udt type attribute correctly', () => {
    const mockAdvancedAttributeType: GSQLAttributeTypeJson = {
      Name: 'UDT',
      TupleName: 'myTuple'
    };

    const attributeType = new AttributeType();
    const type = attributeType.loadFromGSQLJson(mockAdvancedAttributeType);
    expect(type.hasTupleName).toBeTruthy();
    expect(type.name).toEqual('UDT');
    expect(type.tupleName).toEqual('myTuple');
  });

  it('should load udt type value as complex type attribute value correctly.', () => {
    const mockAdvancedAttributeType: GSQLAttributeTypeJson = {
      Name: 'MAP',
      KeyTypeName: 'STRING',
      ValueTypeName: 'UDT',
      ValueTypeTupleName: 'myTuple'
    };

    const attributeType = new AttributeType();
    const type = attributeType.loadFromGSQLJson(mockAdvancedAttributeType);
    expect(type.hasValueTypeTupleName).toBeTruthy();
    expect(type.valueTypeName).toEqual('UDT');
    expect(type.valueTypeTupleName).toEqual('myTuple');
  });

  it('should dump to GSQL attribute with advanced features correctly.', () => {
    const udtType = new AttributeType();
    udtType.name = 'UDT';
    udtType.hasTupleName = true;
    udtType.tupleName = 'myTuple';
    const gsqlUdtType = udtType.dumpToGSQLJson();
    expect(gsqlUdtType.Name).toBe('UDT');
    expect(gsqlUdtType.TupleName).toBe('myTuple');

    const fixedBinaryType = new AttributeType();
    fixedBinaryType.name = 'FIXED_BINARY';
    fixedBinaryType.hasFixedSize = true;
    fixedBinaryType.fixedSize = 6;
    const gsqlFixedBinaryType = fixedBinaryType.dumpToGSQLJson();
    expect(gsqlFixedBinaryType.Name).toBe('FIXED_BINARY');
    expect(gsqlFixedBinaryType.FixedSize).toBe(6);

    const mapType = new AttributeType();
    mapType.name = 'MAP';
    mapType.hasKeyTypeName = true;
    mapType.keyTypeName = 'STRING';
    mapType.hasValueTypeName = true;
    mapType.valueTypeName = 'UDT';
    mapType.hasValueTypeTupleName = true;
    mapType.valueTypeTupleName = 'myTuple';
    const gsqlMapType = mapType.dumpToGSQLJson();
    expect(gsqlMapType.Name).toBe('MAP');
    expect(gsqlMapType.KeyTypeName).toBe('STRING');
    expect(gsqlMapType.ValueTypeName).toBe('UDT');
    expect(gsqlMapType.ValueTypeTupleName).toBe('myTuple');
  });

  describe('should get data type', () => {
    it('with string type', () => {
      const mapType = new AttributeType();
      mapType.name = 'STRING';
      const type = mapType.getDataType();
      expect(type).toBe(DataType.String);
    });

    it('with int type', () => {
      const mapType = new AttributeType();
      mapType.name = 'INT';
      const type = mapType.getDataType();
      expect(type).toBe(DataType.Int);
    });

    it('with real type', () => {
      const mapType = new AttributeType();
      mapType.name = 'FLOAT';
      const type = mapType.getDataType();
      expect(type).toBe(DataType.Real);
    });

    it('with bool type', () => {
      const mapType = new AttributeType();
      mapType.name = 'BOOL';
      const type = mapType.getDataType();
      expect(type).toBe(DataType.Bool);
    });
  });

  describe('should get value type', () => {
    it('with MAP type', () => {
      const attrType = new AttributeType();
      attrType.name = 'MAP';
      attrType.keyTypeName = 'INT';
      attrType.valueTypeName = 'BOOL';
      expect(attrType.getValueType()).toEqual('MAP<INT, BOOL>');
    });

    it('with SET type', () => {
      const attrType = new AttributeType();
      attrType.name = 'SET';
      attrType.valueTypeName = 'STRING';
      expect(attrType.getValueType()).toEqual('STRING');
    });
  });
});
