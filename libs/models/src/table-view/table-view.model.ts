
export class Table {
  name: string;
  headers: string[] = [];
  // Only support sorting on the columns with primitive data.
  sortables: boolean[] = [];
  rows: any[] = [];

  constructor(name: string, headers: string[] = [], rows: any[] = []) {
    this.name = name;
    this.headers = headers;
    this.rows = rows;
  }

  /**
   * Add one row to table.
   *
   * @param {any} row
   * @memberof Table
   */
  addRow(row: any) {
    this.rows.push(row);
  }

  /**
   * Set table header.
   *
   * @param {string[]} headers
   * @memberof Table
   */
  setHeaders(headers: string[]) {
    this.headers = headers;
  }
}
