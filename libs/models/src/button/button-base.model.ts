type MenuPositionY = 'above' | 'below';
type MenuPositionX = 'before' | 'after';
type ThemePalette = 'primary' | 'accent' | 'warn' | '';
type TooltipPosition = 'left' | 'right' | 'above' | 'below' | 'before' | 'after';


/**
 * Base class for button.
 *
 * @export
 * @class ButtonBase
 */
export class ButtonBase {
  key: string;
  tooltip: string;
  disabled: boolean;
  menu: ButtonBase[];
  color: ThemePalette;
  controlType: string;
  menuPositionX: MenuPositionX;
  menuPositionY: MenuPositionY;
  tooltipPosition: TooltipPosition;
  child: ButtonBase;
  show: boolean;
  checked: boolean;

  constructor(options: {
    key?: string,
    tooltip?: string,
    disabled?: boolean,
    menu?: ButtonBase[],
    color?: ThemePalette,
    controlType?: string,
    menuPositionX?: MenuPositionX,
    menuPositionY?: MenuPositionY,
    tooltipPosition?: TooltipPosition,
    child?: ButtonBase,
    show?: boolean,
    checked?: boolean
  } = {}) {
    this.key = options.key || '';
    this.menu = options.menu || [];
    this.color = options.color || '';
    this.disabled = !!options.disabled;
    this.tooltip = options.tooltip || '';
    this.controlType = options.controlType || '';
    this.menuPositionX = options.menuPositionX || 'after';
    this.menuPositionY = options.menuPositionY || 'below';
    this.tooltipPosition = options.tooltipPosition || 'below';
    this.child = options.child;
    this.show = options.show;
    this.checked = options.checked;
  }
}
