import { StrokedIconButton } from './button-stroked-icon.model';

describe('StrokedIconButtonModel', () => {

  it('should construct a stroked icon button from empty config successfully', () => {
    const button = new StrokedIconButton();
    expect(button.controlType).toBe('stroked');
    expect(button.icon).toBe('');
  });

  it('should construct a stroked icon button from config with icon successfully', () => {
    const button = new StrokedIconButton({ icon: 'icon', label: 'iconLabel' });
    expect(button.controlType).toBe('stroked');
    expect(button.icon).toBe('icon');
    expect(button.label).toBe('iconLabel');
  });
});
