import { ButtonBase } from './button-base.model';

/**
 * Checkbox button used in toolbar.
 *
 * @export
 * @class CheckboxButton
 * @extends {ButtonBase}
 */
export class CheckboxButton extends ButtonBase {
  model: { data: boolean };
  label: string;
  controlType = 'checkbox';

  constructor(options: any = {}) {
    super(options);
    this.model = options.model || { data: false };
    this.label = options.label || '';
  }
}
