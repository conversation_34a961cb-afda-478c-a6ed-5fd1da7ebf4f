import { IconButton } from './button-icon.model';

/**
 * Stroked icon button displays a stroked button with icon.
 *
 * @export
 * @class StrokedIconButton
 * @extends {ButtonBase}
 */
export class StrokedIconButton extends IconButton {
  icon: string;
  label: string;
  controlType = 'stroked';

  constructor(options: any = {}) {
    super(options);
    this.icon = options.icon || '';
    this.label = options.label || '';
  }
}
