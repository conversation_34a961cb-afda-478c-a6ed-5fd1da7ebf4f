import { IconButton } from './button-icon.model';

describe('ButtonIconModel', () => {

  it('should construct an icon button from empty config successfully', () => {
    const button = new IconButton();
    expect(button.icon).toBe('');
  });

  it('should construct an icon button from config with icon successfully', () => {
    const button = new IconButton({ icon: 'icon.png' });
    expect(button.icon).toBe('icon.png');
  });
});
