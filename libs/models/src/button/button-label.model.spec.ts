import { LabelButton } from './button-label.model';

describe('ButtonLabelModel', () => {

  it('should construct a label button from empty config successfully', () => {
    const button = new LabelButton();
    expect(button.label).toBe('');
  });

  it('should construct a label button from config with label successfully', () => {
    const button = new LabelButton({ label: 'the_label' });
    expect(button.label).toBe('the_label');
  });
});
