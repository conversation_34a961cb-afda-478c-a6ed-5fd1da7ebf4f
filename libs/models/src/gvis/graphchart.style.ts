/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: GVIS library Graph Chart Style Module.
 *
 * Created on: Nov 3th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/

/**
 * GVIS library Graph Chart Style Module.
 */
export module GraphStyle {
  /**
   * Node style interface.
   */
  export interface GraphChartNodeStyleInterface {
    /** The ID of one or more auras to which the node belongs. Nodes with the same aura ID will be visually grouped together. */
    aura?: string;
    /** Cursor to show when node is hovered. */
    cursor?: string;
    /** Valid values: circle (default), text, roundtext, droplet, rectangle, customShape */
    display?: string;
    fillColor?: string;
    icon?: string;

    lineColor?: string;
    lineDash?: Array<number>;
    lineWidth?: number;
    /** Node opacity. */
    opacity?: number;
    /** @type {number} Node radius */
    radius?: number;
    shadowBlur?: number;
    shadowColor?: string;
    shadowOffsetX?: number;
    shadowOffsetY?: number;
    draggable?: boolean;
    // isInvisible?: boolean;
    // isExpanded?: boolean;
    // isHovered?: boolean;
    // isFocused?: boolean;
    // isLowlighted?: boolean;
    // isLocked?: boolean;
    // isSelected?: boolean;
    [more: string]: any;
  }

  /**
   * Link style interface
   */
  export interface GraphChartLinkStyleInterface {
    cursor?: string;
    /** null or 'U', 'D', 'L', 'R' */
    direction?: string;
    fillColor?: string;
    length?: number;
    lineDash?: Array<number>;
    /** Specifies the width of the line rendered for this link. */
    radius?: number;
    shadowBlur?: number;
    shadowColor?: string;
    shadowOffsetX?: number;
    shadowOffsetY?: number;

    /** @type {number} specifies the force directed layout. */
    strength?: number;

    // isInvisible?: boolean;
    // isHovered?: boolean;
    // isLowlighted?: boolean;
    // isSelected?: boolean;
    [more: string]: any;
  }

  /**
   * Defailt event flag for items(both link and node) in graph chart.
   */
  export interface GraphChartItemStyleFlagInterface {
    isInvisible?: boolean;
    isExpanded?: boolean;
    isHovered?: boolean;
    isLowlighted?: boolean;
    isLocked?: boolean;
    isSelected?: boolean;

    [more: string]: any;
  }

  /**
   * visualization area setting.
   * @type {[type]}
   */
  export const defualtAreaStyle: any = {
    overlayColor: 'rgba(255, 255, 0, 0)',
    fillColor: 'rgba(249, 249, 249, 0)',
    image: null,
    /** Height of the chart. If undefined the chart height will adapt to container element. */
    height: null,
    /** Width of the chart. If undefined the chart width will adapt to container element. */
    width: null,
    /** The maximum chart height. Chart will not resize below this. */
    maxHeight: 10000,
    /** The maximum chart width. The chart will not resize below this */
    maxWidth: 10000,
    /** The minimum chart height. Chart will not resize below this. */
    minHeight: 300,
    /** The minimum chart width. The chart will not resize below this */
    minWidth: 300,
  };

  /**
   * default node base style in graph chart
   * @type {GraphChartNodeStyleInterface}
   */
  export const defaultNodeBaseStyle: GraphChartNodeStyleInterface = {
    aura: undefined as any,
    cursor: null,
    display: 'circle',
    fillColor: '#c7dae0',
    icon: null,
    lineColor: null,
    lineDash: null,
    lineWidth: null,
    opacity: 1,
    radius: 40,
    shadowBlur: 6,
    shadowColor: null,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    aspectRatio: 1,
  };

  /**
   * Detail link base style in graph chart
   * @type {GraphChartLinkStyleInterface}
   */
  export const defaultLinkBaseStyle: GraphChartLinkStyleInterface = {
    cursor: null,
    // direction: 'R',
    fillColor: '#ce6dbd',
    length: 1,
    lineDash: null,
    radius: 3,
    shadowBlur: undefined,
    shadowColor: null,
    shadowOffsetX: undefined,
    shadowOffsetY: undefined,
    strength: 0.5,
  };

  /**
   * The node style object in graph chart.
   */
  export class GraphChartNodeStyles implements GraphChartItemStyleFlagInterface {
    public base: GraphChartNodeStyleInterface;

    public isHovered: boolean;
    public hovered: GraphChartNodeStyleInterface;

    public isSelected: boolean;
    public selected: GraphChartNodeStyleInterface;

    public isLowlighted: boolean;
    public lowlighted: GraphChartNodeStyleInterface;

    public isLocked: boolean;
    public locked: GraphChartNodeStyleInterface;

    public isExpanded: boolean;
    public expanded: GraphChartNodeStyleInterface;

    public isInvisible: boolean;
    public invisible: GraphChartNodeStyleInterface;

    constructor(newStyles: any) {
      try {
        this.base = Object.create(null);

        for (let key in newStyles) {
          if (Object.prototype.hasOwnProperty.call(newStyles, key)) {
            // if (Object.prototype.hasOwnProperty.call(defaultNodeBaseStyle, key)) {
            //   this.base[key] = newStyles[key];
            // } else {
            switch (key) {
              case 'isHovered':
              case 'isSelected':
              case 'isLowlighted':
              case 'isLocked':
              case 'isExpanded':
              case 'isInvisible':
                (this as any)[key] = newStyles[key] || false;
                break;
              case 'hovered':
              case 'selected':
              case 'lowlighted':
              case 'locked':
              case 'expanded':
              case 'invisible':
                (this as any)[key] = (this as any)[key] || Object.create(null);
                for (let _key in newStyles[key]) {
                  if (Object.prototype.hasOwnProperty.call(newStyles[key], _key)) {
                    if (Object.prototype.hasOwnProperty.call(defaultNodeBaseStyle, _key)) {
                      (this as any)[key][_key] = newStyles[key][_key];
                    } else {
                      (this as any)[key][_key] = newStyles[key][_key];
                      // let tmp = new GException(3001, `${_key} is an unknow style attribute.`);
                      // console.warn(tmp.toString());
                    }
                  }
                }
                break;
              default:
                this.base[key] = newStyles[key];
                // let tmp = new GException(3001, `${key} is an unknow style attribute.`);
                // console.warn(tmp.toString());
                break;
              // }
            }
          }
        }
      } catch (err) {
        console.warn(err.toString());
      }
    }
  }

  /**
   * The link style object for graph chart.
   */
  export class GraphChartLinkStyles implements GraphChartItemStyleFlagInterface {
    public base: GraphChartLinkStyleInterface;

    public isHovered: boolean;
    public hovered: GraphChartNodeStyleInterface;

    public isSelected: boolean;
    public selected: GraphChartNodeStyleInterface;

    public isLowlighted: boolean;
    public lowlighted: GraphChartNodeStyleInterface;

    public isInvisible: boolean;
    public invisible: GraphChartNodeStyleInterface;

    constructor(newStyles: any) {
      try {
        this.base = Object.create(null);

        for (let key in newStyles) {
          if (Object.prototype.hasOwnProperty.call(newStyles, key)) {
            // if (Object.prototype.hasOwnProperty.call(defaultLinkBaseStyle, key)) {
            //   this.base[key] = newStyles[key];
            // } else {
            switch (key) {
              case 'isHovered':
              case 'isSelected':
              case 'isLowlighted':
              case 'isInvisible':
                (this as any)[key] = newStyles[key] || false;
                break;
              case 'hovered':
              case 'selected':
              case 'lowlighted':
              case 'invisible':
                (this as any)[key] = (this as any)[key] || Object.create(null);
                for (let _key in newStyles[key]) {
                  if (Object.prototype.hasOwnProperty.call(newStyles[key], _key)) {
                    if (Object.prototype.hasOwnProperty.call(defaultLinkBaseStyle, _key)) {
                      (this as any)[key][_key] = newStyles[key][_key];
                    } else {
                      (this as any)[key][_key] = newStyles[key][_key];
                      // let tmp = new GException(3002, `${_key} is an unknow style attribute.`);
                      // console.warn(tmp.toString());
                    }
                  }
                }
                break;
              default:
                this.base[key] = newStyles[key];
              // let tmp = new GException(3002, `${key} is an unknow style attribute.`);
              // console.warn(tmp.toString());
              // }
            }
          }
        }
      } catch (err) {
        console.warn(err.toString());
      }
    }
  }

  export const defaultNodeStyle = new GraphChartNodeStyles({});
  defaultNodeStyle.base = defaultNodeBaseStyle;

  defaultNodeStyle.hovered = {
    shadowBlur: 15,
    shadowColor: 'rgba(0, 0, 255, 0.9)',
  };

  defaultNodeStyle.selected = {
    shadowBlur: 15,
    shadowColor: 'rgba(0, 0, 255, 0.9)',
    shadowOffsetX: 0,
    shadowOffsetY: 3,
  };

  defaultNodeStyle.lowlighted = {
    opacity: 0.1,
  };

  defaultNodeStyle.locked = {
    // display: 'droplet'
  };

  defaultNodeStyle.expanded = {};

  defaultNodeStyle.invisible = {};

  export const defaultLinkStyle = new GraphChartLinkStyles({});
  defaultLinkStyle.base = defaultLinkBaseStyle;

  defaultLinkStyle.hovered = {
    shadowBlur: 4,
    shadowColor: 'rgba(0, 0, 255, 0.9)',
    shadowOffsetX: 0,
    shadowOffsetY: 0,
  };

  defaultLinkStyle.selected = {
    // fillColor: 'rgba(255, 0, 0, 0.9)',
    // radius: 10,
    shadowBlur: 15,
    shadowColor: 'rgba(0, 0, 255, 0.9)',
  };

  defaultLinkStyle.lowlighted = {
    fillColor: 'rgba(188, 188, 188, 0.1)',
  };

  defaultLinkStyle.invisible = {};

  export const virtualRootNode = {
    id: 'virtual root node which is not existed or visualized',
    exID: 'virtual root node which is not existed or visualized',
    exType: 'virtual root node which is not existed or visualized',
  };
}
