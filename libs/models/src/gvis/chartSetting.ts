import { GraphChartDataNode } from './graphchart.data.node';
import { GraphChartDataLink } from './graphchart.data.link';
import { BaseApi } from './base.api';
import { GraphStyle } from './graphchart.style';

export interface GraphChartSetting {
  advanced?: {
    maxNodeLimit?: number;
    maxLinkLimit?: number;
    hideArrorMinZoom?: number;
    nodeToolTipSetting?: {
      (inNode: GraphChartDataNode, coreNode?: any, chart?: BaseApi): any;
    };
    linkToolTipSetting?: {
      (inLink: GraphChartDataLink, coreLink?: any, chart?: BaseApi): any;
    };
    nodeMenuContentsFunction?: {
      (
        inNode: GraphChartDataNode,
        coreNode?: any,
        config?: any,
        returnDefaultContent?: {
          (): any;
        }
      ): any;
    };
    linkMenuContentsFunction?: {
      (
        inLink: GraphChartDataLink,
        coreLink?: any,
        config?: any,
        returnDefaultContent?: {
          (): any;
        }
      ): any;
    };
  };
  renderType?: string; // canvas, svg, webgl;
  render?: any; // rendering setting.
  style?: {
    node?: {
      condition: string | { (node: GraphChartDataNode, chart: BaseApi): boolean };
      style: GraphStyle.GraphChartNodeStyleInterface;
    }[];
    link?: {
      condition: string | { (link: GraphChartDataLink, chart: BaseApi): boolean };
      style: GraphStyle.GraphChartLinkStyleInterface;
    }[];
  };
  icon?: {
    [exType: string]: string;
  };
  graphSchema?: {
    VertexTypes: {
      Name: string;
    }[];
    EdgeTypes: {
      Name: string;
    }[];
  };
  nodeMenu?: {
    buttons?: {
      label: string;
      callback: { (node: GraphChartDataNode, chart: BaseApi): void };
    }[];
    enabled: boolean;
  };
  linkMenu?: {
    buttons?: {
      label: string;
      callback: { (link: GraphChartDataLink, chart: BaseApi): void };
    }[];
    enabled: boolean;
  };
  language?: {
    selectedLanguage?: string;
    localization?: {
      [language: string]: {
        vertex?: {
          [type: string]:
            | string
            | {
                type?: string;
                attrs: {
                  [attribute: string]:
                    | string
                    | {
                        attr?: string;
                        values: {
                          [value: string]: string;
                        };
                      };
                };
              };
        };
        edge?: {
          [type: string]:
            | string
            | {
                type?: string;
                attrs: {
                  [attribute: string]:
                    | string
                    | {
                        attr?: string;
                        values: {
                          [value: string]: string;
                        };
                      };
                };
              };
        };
      };
    };
  };
}
