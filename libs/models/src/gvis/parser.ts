
import { ExternalGraph } from './graphParser';
import { ExternalNode } from './graphchart.data.node';
import { ExternalLink } from './graphchart.data.link';


/**
 * parser format standard class.
 * @type {[type]}
 */
export class GraphParserFormat {
  public gquery: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  public pta: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  public ksubgraph: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  public gpr: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  public restpp: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  public gst: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  };

  constructor() {

    this
      .initGQueryFormat()
      .initPTAFormat()
      .initKSubGraphFormat()
      .initGPRFormat()
      .initRestppFormat()
      .initGSTFormat();

  }

  private initGQueryFormat(): this {
    this.gquery = {
      isNode: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['v_id'] === 'string';
          result = result && typeof obj['v_type'] === 'string';
          // result = result && typeof obj['v_set'] === 'string';
          result = result && (typeof obj['v'] === 'object' || typeof obj['attributes'] === 'object');
        }

        return result;
      },
      isLink: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['e_type'] === 'string';
          result = result && typeof obj['from_id'] === 'string';
          result = result && typeof obj['from_type'] === 'string';
          result = result && typeof obj['to_id'] === 'string';
          result = result && typeof obj['to_type'] === 'string';
          result = result && typeof obj['directed'] === 'boolean';
          result = result && typeof obj['attributes'] === 'object';
        }

        return result;
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = {
          id: obj.v_id,
          type: obj.v_type,
          attrs: {},
          styles: {},
          others: {
            'vertex set': obj.v_set
          },
          labels: {}
        };

        if (obj.v) {
          Object.keys(obj.v).forEach((attr: string) => {
            newNode.attrs[attr] = obj.v[attr];
          });
        }

        if (obj.attributes) {
          Object.keys(obj.attributes).forEach((attr: string) => {
            newNode.attrs[attr] = obj.attributes[attr];
          });
        }

        obj.styles = obj.styles || {};
        obj.others = obj.others || {};
        obj.labels = obj.labels || {};

        Object.keys(obj.styles).forEach((key: string) => {
          newNode.styles[key] = obj.styles[key];
        });

        Object.keys(obj.others).forEach((key: string) => {
          newNode.others[key] = obj.others[key];
        });

        Object.keys(obj.labels).forEach((key: string) => {
          newNode.labels[key] = obj.labels[key];
        });

        return newNode;
      },
      parseLink: (obj) => {
        let newLink: ExternalLink = {
          source: {
            id: obj.from_id,
            type: obj.from_type
          },
          target: {
            id: obj.to_id,
            type: obj.to_type
          },
          directed: obj.directed,
          type: obj.e_type,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        Object.keys(obj.attributes).forEach((attr: string) => {
          newLink.attrs[attr] = obj.attributes[attr];
        });

        obj.styles = obj.styles || {};
        obj.others = obj.others || {};
        obj.labels = obj.labels || {};

        Object.keys(obj.styles).forEach((key: string) => {
          newLink.styles[key] = obj.styles[key];
        });

        Object.keys(obj.others).forEach((key: string) => {
          newLink.others[key] = obj.others[key];
        });

        Object.keys(obj.labels).forEach((key: string) => {
          newLink.labels[key] = obj.labels[key];
        });

        return newLink;
      }
    };

    return this;
  }

  private initPTAFormat(): this {
    this.pta = {
      isNode: (obj) => {
        return this.gquery.isNode(obj);
      },
      isLink: (obj) => {
        return this.gquery.isLink(obj);
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = this.gquery.parseNode(obj);

        // Remove the @links from attributes
        if ('@links' in newNode.attrs) {
          delete newNode.attrs['@links'];
        }

        return newNode;
      },
      parseLink: (obj) => {
        return this.gquery.parseLink(obj);
      }
    };

    return this;
  }

  private initKSubGraphFormat(): this {

    this.ksubgraph = {
      isNode: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['id'] === 'string';
          result = result && typeof obj['v_type'] === 'string';
          result = result && typeof obj['attrs'] === 'string';
        }

        return result;
      },
      isLink: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['etype'] === 'string';
          result = result && typeof obj['src_vertex'] === 'object';
          result = result && typeof obj['tgt_vertex'] === 'object';
          result = result && typeof obj['attrs'] === 'string';

          if (result) {
            result = result && typeof obj['src_vertex']['vid'] === 'string';
            result = result && typeof obj['src_vertex']['vtype'] === 'string';
            result = result && typeof obj['tgt_vertex']['vid'] === 'string';
            result = result && typeof obj['tgt_vertex']['vtype'] === 'string';
          }
        }

        return result;
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = {
          id: obj.id,
          type: obj.v_type,
          attrs: this.gpeTopologyAttributesOutputToJSON(obj.attrs),
          styles: {},
          others: {},
          labels: {}
        };

        return newNode;
      },
      parseLink: (obj) => {
        let newLink: ExternalLink = {
          source: {
            id: obj.src_vertex.vid,
            type: obj.src_vertex.vtype
          },
          target: {
            id: obj.tgt_vertex.vid,
            type: obj.tgt_vertex.vtype
          },
          directed: true,
          type: obj.etype,
          attrs: this.gpeTopologyAttributesOutputToJSON(obj.attrs),
          styles: {},
          others: {},
          labels: {}
        };

        return newLink;
      }
    };

    return this;
  }

  private initGPRFormat(): this {
    this.gpr = {
      isNode: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['id'] === 'string';
          result = result && typeof obj['vertex_type'] === 'string';
          // result = result && typeof obj['attribute'] === 'object';
        }

        return result;
      },
      isLink: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['edge_type_name'] === 'string';
          result = result && typeof obj['src_vertex'] === 'object';
          result = result && typeof obj['tgt_vertex'] === 'object';
          // result = result && typeof obj['attribute'] === 'object';

          if (result) {
            result = result && typeof obj['src_vertex']['vertex_id'] === 'string';
            result = result && typeof obj['src_vertex']['vertex_type_name'] === 'string';
            result = result && typeof obj['tgt_vertex']['vertex_id'] === 'string';
            result = result && typeof obj['tgt_vertex']['vertex_type_name'] === 'string';
          }
        }

        return result;
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = {
          id: obj.id,
          type: obj.vertex_type,
          attrs: {},
          styles: {},
          others: {
            'vertex set': obj.v_set
          },
          labels: {}
        };

        for (let attr in obj.attribute) {
          if (Object.prototype.hasOwnProperty.call(obj.attribute, attr)) {
            newNode.attrs[attr] = obj.attribute[attr];
          }
        }

        return newNode;
      },
      parseLink: (obj) => {
        let newLink: ExternalLink = {
          source: {
            id: obj.src_vertex.vertex_id,
            type: obj.src_vertex.vertex_type_name
          },
          target: {
            id: obj.tgt_vertex.vertex_id,
            type: obj.tgt_vertex.vertex_type_name
          },
          directed: obj.directed || false,
          type: obj.edge_type_name,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        for (let attr in obj.attribute) {
          if (Object.prototype.hasOwnProperty.call(obj.attribute, attr)) {
            newLink.attrs[attr] = obj.attribute[attr];
          }
        }

        return newLink;
      }
    };

    return this;
  }

  private initRestppFormat(): this {
    this.restpp = {
      isNode: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['id'] === 'string';
          result = result && typeof obj['vtype'] === 'string';
          result = result && typeof obj['attributes'] === 'object';
        }

        return result;
      },
      isLink: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['etype'] === 'string';
          result = result && typeof obj['from_id'] === 'string';
          result = result && typeof obj['from_vtype'] === 'string';
          result = result && typeof obj['to_id'] === 'string';
          result = result && typeof obj['to_vtype'] === 'string';
          result = result && typeof obj['attributes'] === 'object';
        }

        return result;
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = {
          id: obj.id,
          type: obj.vtype,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        Object.keys(obj.attributes).forEach((attr: string) => {
          newNode.attrs[attr] = obj.attributes[attr];
        });

        obj.styles = obj.styles || {};
        obj.others = obj.others || {};
        obj.labels = obj.labels || {};

        Object.keys(obj.styles).forEach((key: string) => {
          newNode.styles[key] = obj.styles[key];
        });

        Object.keys(obj.others).forEach((key: string) => {
          newNode.others[key] = obj.others[key];
        });

        Object.keys(obj.labels).forEach((key: string) => {
          newNode.labels[key] = obj.labels[key];
        });

        return newNode;
      },
      parseLink: (obj) => {
        let newLink: ExternalLink = {
          source: {
            id: obj.from_id,
            type: obj.from_vtype
          },
          target: {
            id: obj.to_id,
            type: obj.to_vtype
          },
          directed: true,
          type: obj.etype,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        Object.keys(obj.attributes).forEach((attr: string) => {
          newLink.attrs[attr] = obj.attributes[attr];
        });

        obj.styles = obj.styles || {};
        obj.others = obj.others || {};
        obj.labels = obj.labels || {};

        Object.keys(obj.styles).forEach((key: string) => {
          newLink.styles[key] = obj.styles[key];
        });

        Object.keys(obj.others).forEach((key: string) => {
          newLink.others[key] = obj.others[key];
        });

        Object.keys(obj.labels).forEach((key: string) => {
          newLink.labels[key] = obj.labels[key];
        });

        return newLink;
      }
    };

    return this;
  }

  private initGSTFormat(): this {
    this.gst = {
      isNode: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['vertex_id'] === 'string';
          result = result && typeof obj['vertex_type'] === 'string';
          result = result && typeof obj['attributes'] === 'object';
          result = result && typeof obj['other'] === 'object';
        }

        return result;
      },
      isLink: (obj) => {
        let result = true;

        if (typeof obj !== 'object' || obj === null) {
          return false;
        } else {
          result = result && typeof obj['edge_type'] === 'string';
          result = result && typeof obj['directed'] === 'boolean';
          result = result && typeof obj['from_id'] === 'string';
          result = result && typeof obj['from_type'] === 'string';
          result = result && typeof obj['to_id'] === 'string';
          result = result && typeof obj['to_type'] === 'string';
          result = result && typeof obj['attributes'] === 'object';
          result = result && typeof obj['other'] === 'object';
        }

        return result;
      },
      parseNode: (obj) => {
        let newNode: ExternalNode = {
          id: obj.vertex_id,
          type: obj.vertex_type,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        Object.keys(obj.attributes).forEach((attr: string) => {
          newNode.attrs[attr] = obj.attributes[attr];
        });

        obj.style = obj.style || {};
        obj.other = obj.other || {};

        Object.keys(obj.style).forEach((key: string) => {
          newNode.styles[key] = obj.style[key];
        });

        Object.keys(obj.other).forEach((key: string) => {
          newNode.others[key] = obj.other[key];
        });

        return newNode;
      },
      parseLink: (obj) => {
        let newLink: ExternalLink = {
          source: {
            id: obj.from_id,
            type: obj.from_type
          },
          target: {
            id: obj.to_id,
            type: obj.to_type
          },
          directed: obj.directed,
          type: obj.edge_type,
          attrs: {},
          styles: {},
          others: {},
          labels: {}
        };

        Object.keys(obj.attributes).forEach((attr: string) => {
          newLink.attrs[attr] = obj.attributes[attr];
        });

        obj.style = obj.style || {};
        obj.other = obj.other || {};

        Object.keys(obj.style).forEach((key: string) => {
          newLink.styles[key] = obj.style[key];
        });

        Object.keys(obj.other).forEach((key: string) => {
          newLink.others[key] = obj.other[key];
        });

        return newLink;
      }
    };

    return this;

  }

  /**
   * convert gpe topology attribute standard output to json object.
   * @example
   * <pre>
   * 'type:2|od_et4:2|od_et5:1|name(0):site12|formula_order(0):2|useAmount(1):6000.000000|' =>
   *  { name: 'site12', 'formula_order': '2', 'useAmount': '6000.000000'}
   * Split by '|', then for each item parse /(.*)\(\d*\):(.*)$/. In this case, 'type', 'od_et4', 'od_et5' are not parsed.
   * </pre>
   * @param {string} attrString [description]
   */
  private gpeTopologyAttributesOutputToJSON(attrString: string): { [attr: string]: any } {
    if (attrString === '' || attrString === undefined || typeof attrString !== 'string') {
      return {};
    } else {
      let result: {
        [key: string]: any
      } = {};
      let reggie = /(.*)\(\d*\):(.*)$/;

      attrString.split('|').forEach(attr => {
        let tempObj = reggie.exec(attr);
        if (tempObj !== null) {
          result[tempObj[1]] = tempObj[2];
        }
      });

      return result;
    }
  }
}


/**
 * Graph parser class.
 */
export class GraphParser {

  public parsers: {
    [parserName: string]: {
      (externalData: any): ExternalGraph
    };
  } = {};

  private parserFormat: GraphParserFormat = new GraphParserFormat();

  constructor() {

    this.addParser('static', (externalData) => {
      return <any>externalData;
    });

    this.addParser('gquery', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.gquery);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });

    this.addParser('pta', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.pta);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });

    this.addParser('ksubgraph', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.ksubgraph);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });

    this.addParser('gpr', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.gpr);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });

    this.addParser('restpp', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.restpp);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });

    this.addParser('gst', (externalData) => {
      let targetGraph: ExternalGraph = {
        nodes: [],
        links: []
      };

      try {
        this.recursivelyGetExternalElement(externalData, targetGraph, this.parserFormat.gst);
      } catch (err) {
        console.warn(err.toString());
        targetGraph = {
          nodes: [],
          links: []
        };
      }

      return targetGraph;
    });
  }

  /**
   * recursively parsing obj to get external graph elements, such as nodes and links. Then add external graph elements in to targetGraph base on the format setting.
   * @param {any}             obj         raw input. It is an random json object, which may contains graph elements.
   * @param {ExternalGraph}   targetGraph The output graph. It is the graph in visualization format.
   * @param {ExternalLink };                       }} format  This is the format object which contians 4 functions:
   * isNode: { (obj: any): boolean }; Check if an obj is a node.
   * isLink: { (obj: any): boolean }; Check if an obj is a link.
   * parseNode: { (obj: any): ExternalNode }; Parser function for a node.
   * parseLink: { (obj: any): ExternalLink }; Parser function for a link.
   */
  private recursivelyGetExternalElement(obj: any, targetGraph: ExternalGraph, format: {
    isNode: { (obj: any): boolean };
    isLink: { (obj: any): boolean };
    parseNode: { (obj: any): ExternalNode };
    parseLink: { (obj: any): ExternalLink };
  }) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    let tempObj = obj.constructor ? obj.constructor() : {};
    for (let key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        tempObj[key] = this.recursivelyGetExternalElement(obj[key], targetGraph, format);
      } else {
        throw new Error('External Data format can not be parased.');
      }
    }

    if (format.isNode(tempObj)) {
      targetGraph.nodes.push(format.parseNode(tempObj));
    }

    if (format.isLink(tempObj)) {
      targetGraph.links.push(format.parseLink(tempObj));
    }

    return tempObj;
  }

  /**
   * Add a parser function by name. Then we can use it by getParser('parserName');
   * @param  {string}           parserName [description]
   * @param  {ExternalGraph }}       callback      [description]
   * @return {this}                        [description]
   */
  public addParser(parserName: string, callback: { (externalData: any): ExternalGraph }): this {
    try {
      if (this.parsers[parserName] === undefined) {
        this.parsers[parserName] = callback;
      } else {
        throw new Error(`${parserName} parser is already existed,`);
      }
    } catch (err) {
      console.warn(err.toString());
    }

    return this;
  }

  /**
   * Update a parser function by name. Then we use the updated parser function by getParser('parserName');
   * @param  {string}           parserName [description]
   * @param  {ExternalGraph }}       callback      [description]
   * @return {this}                        [description]
   */
  public updateParser(parserName: string, callback: { (externalData: any): ExternalGraph }): this {
    try {
      if (this.parsers[parserName] !== undefined) {
        this.parsers[parserName] = callback;
      } else {
        throw new Error(`${parserName} parser is already existed,`);
      }
    } catch (err) {
      console.warn(err.toString());
    }

    return this;
  }

  /**
   * Get the target parser function by name.
   * @param  {any}           parserName [description]
   * @return {ExternalGraph}            [description]
   */
  public getParser(parserName: string): { (externalData: any): ExternalGraph } {
    try {
      if (this.parsers[parserName] === undefined) {
        throw new Error('External data parser is not existed.');
      } else {
        return this.parsers[parserName];
      }
    } catch (err) {
      console.warn(err.toString());
    }

    return this.parsers['gquery'];
  }
}


/**
 * Array parser class. Need to be implemented for LineChart, once we have time.
 */
export class ArrayParser {

}
