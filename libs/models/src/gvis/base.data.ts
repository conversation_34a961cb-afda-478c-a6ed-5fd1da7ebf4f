/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Data Base Class.
 *
 * Created on: Oct 5th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/

/**
 * Base Data Object Class for all Data object in the GVIS.
 * @preferred
 */
export abstract class BaseData {
  /**
   * Store any additional data values within this field.
   */
  public extra: any;

  /**
   * This field can be used to return any error message for any data object.
   */
  public error: string;

  constructor() {
    this.error = '';
  }
}
