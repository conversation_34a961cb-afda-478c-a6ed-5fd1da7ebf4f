/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: GVIS library Utility Module.
 *
 * Created on: Oct 7th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/

import cloneDeepLodash from 'lodash/cloneDeep';
import mergeLodash from 'lodash/merge';
import defaultsDeepLodash from 'lodash/defaultsDeep';

/**
 * GVIS library Utility Module.
 */
export module Utils {
  /**
   * This method is like `_.clone` except that it recursively clones `value`.
   *
   * @static
   * @memberOf _
   * @category Lang
   * @param {*} value The value to recursively clone.
   * @returns {*} Returns the deep cloned value.
   * @example
   *
   * var objects = [{ 'a': 1 }, { 'b': 2 }];
   *
   * var deep = _.cloneDeep(objects);
   * console.log(deep[0] === objects[0]);
   * // => false
   */
  export function cloneDeep<T>(obj: T): T {
    let deep = cloneDeepLodash(obj);
    return deep;
  }

  /**
   * Recursively merges own and inherited enumerable properties of source
   * objects into the destination object, skipping source properties that resolve
   * to `undefined`. Array and plain object properties are merged recursively.
   * Other objects and value types are overridden by assignment. Source objects
   * are applied from left to right. Subsequent sources overwrite property
   * assignments of previous sources.
   *
   * **Note:** This method mutates `object`.
   *
   * @static
   * @memberOf _
   * @category Object
   * @param {Object} object The destination object.
   * @param {...Object} [sources] The source objects.
   * @returns {Object} Returns `object`.
   * @example
   *
   * var users = {
   *   'data': [{ 'user': 'barney' }, { 'user': 'fred' }]
   * };
   *
   * var ages = {
   *   'data': [{ 'age': 36 }, { 'age': 40 }]
   * };
   *
   * _.merge(users, ages);
   * // => { 'data': [{ 'user': 'barney', 'age': 36 }, { 'user': 'fred', 'age': 40 }] }
   */
  export function extend<T>(destiation: T, ...sources: any[]) {
    // @ts-ignore
    return mergeLodash.apply(this, arguments);
  }

  /**
   * This method is like _.defaults except that it recursively assigns default properties.
   * @param object The destination object.
   * @param sources The source objects.
   * @return Returns object.
   */
  export function defaultsDeep<T>(target: T, ...sources: any[]) {
    // @ts-ignore
    return defaultsDeepLodash.apply(this, arguments);
  }
}
