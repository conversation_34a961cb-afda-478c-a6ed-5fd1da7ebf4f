/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Charts BaseApi Class.
 *
 * Created on: Oct 5th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/
import { BaseStyle } from './base.style';

/**
 * global internal variable for indexing all gvis object.
 * @type {Number}
 */
let instance = 0;

/**
 * BaseApi Class for all future charts.
 * @preferred
 */
export abstract class BaseApi {
  protected version: string = '0.2.3';
  public instanceIndex: number;
  public typeName: string = 'GVIS Chart';
  public container: any;

  public data: any;
  public render: any;
  public event: any;
  public layout: any;

  public language: any;
  public options: any;

  constructor() {
    this.instanceIndex = instance++;
    this.render = {};
    this.render.style = new BaseStyle();
  }

  /**
   * Set customized color by name for all ECharts based chart.
   *
   * @example Change color for series data.
   * <pre>
   *  chart.setColorByName('Users', '#f00');
   *  chart.setColorByName('Series 1', '#f00');
   *  chart.setColorByName('Series 2', '#00ff00');
   *  chart.setColorByName('China', '#f0f');
   *  chart.setColorByName('Shanghai', '#aabbcc');
   *
   * </pre>
   * @param  {string} name  [description]
   * @param  {string} color [description]
   * @return {this}         [description]
   */
  public setColorByName(name: string, color: string): this {
    this.render.style.setColorByName(name, color);
    return this;
  }

  /**
   * Return the customized color by name;
   * 
   * @example Get current color for a series.
   * <pre>

   *  console.assert(chart.getColorByName('Users'), '#f00');
   *  console.assert(chart.getColorByName('Series 1'), '#f00');
   *  console.assert(chart.getColorByName('Series 2'), '#00ff00');
   *  console.assert(chart.getColorByName('China'), '#f0f');
   *  console.assert(chart.getColorByName('Shanghai'), '#aabbcc');
   * </pre>
   * @param  {string} name [description]
   * @return {string}      [description]
   */
  public getColorByName(name: string): string | undefined {
    return this.render.style.getColorByName(name);
  }
}
