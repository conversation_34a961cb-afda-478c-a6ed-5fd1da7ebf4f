/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Define base class of all nodes object.
 *
 * Created on: Oct 6th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/
import { ItemsData } from './items.data';

/**
 * Base node object.
 * @preferred
 */
export class ItemsDataNode extends ItemsData {
  /**
   * node id which is the unique identifier of the node;
   * @type {string}
   */
  public id: string;

  /**
   * This filed can be used to provide multiple classes for styling.
   * Such as className = "classA classB classN";
   * @type {string}
   */
  public className: string;

  /**
   * attribute object for a node data object.
   * @type {Object}
   */
  public attrs: Object;

  /**
   * visualization style of the node object.
   * @type {Object}
   */
  public styles: Object;

  constructor(internalID: string) {
    super();

    this.id = internalID;
    this.className = '';
  }
}
