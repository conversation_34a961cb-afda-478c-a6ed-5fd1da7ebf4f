/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Define node data object class for graph chart.
 *
 * Created on: Oct 6th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/
import { ItemsDataNode } from './items.data.node';
import { GraphStyle } from './graphchart.style';
import { Utils } from './utils';

export interface ExternalNode {
  id: string;
  type: string;
  attrs?: {
    [key: string]: string | number | boolean | Object;
  };
  embeddingAttrs?: {
    [key: string]: string | number | boolean | Object;
  }[];
  styles?: {
    [key: string]: string | number | Object;
  };
  others?: any;
  labels?: any;
  x?: number;
  y?: number;
}

/**
 * Graph chrt node object.
 * @preferred
 */
export class GraphChartDataNode extends ItemsDataNode {
  /**
   * node external id. It is mapped to internal id index. id index + type index = interal id
   * @type {string}
   */
  public exID: string;

  /**
   * node external type. It is mapped to internal type index.
   * @type {string}
   */
  public exType: string;

  /**
   * Style object for graph node object.
   */
  public attrs: {
    [attrName: string]: number | string | boolean | Object;
  };

  /**
   * Style object for graph node object.
   */
  public styles: GraphStyle.GraphChartNodeStyles;

  /**
   * label object to determine which label needs to be shown in visualization.
   */
  public labels: {
    [attrName: string]: boolean;
  };

  /**
   * External x value.
   * @type {number}
   */
  public exX: number;

  /**
   * Exteranl y value.
   * @type {number}
   */
  public exY: number;

  /**
   * Layout x position
   * @type {number}
   */
  public x: number = 0;

  /**
   * Layout y position
   * @type {number}
   */
  public y: number = 0;
  /**
   * This field is used for storing middle reuslt from several algorithms which need to be shown in visualization or affect layout.
   * @type {any}
   */
  public others: any;

  public isNode = true;

  constructor(internalID: string, exNode: ExternalNode) {
    super(internalID);

    this.exID = exNode.id;
    this.exType = exNode.type;

    this.exX = exNode.x;
    this.exY = exNode.y;

    this.attrs = Utils.cloneDeep(exNode.attrs) || Object.create(null);

    this.styles = new GraphStyle.GraphChartNodeStyles(exNode.styles);

    this.others = Utils.cloneDeep(exNode.others) || Object.create(null);
    this.labels = Utils.cloneDeep(exNode.labels) || Object.create(null);
  }

  /**
   * Merge external node. Prefer external node values.
   * @param  {ExternalNode} exNode [description]
   * @return {this}                [description]
   */
  public extendNode(exNode: ExternalNode): this {
    let newNode = Utils.cloneDeep(exNode);

    for (let key in newNode.attrs) {
      if (Object.prototype.hasOwnProperty.call(newNode.attrs, key)) {
        if (newNode.attrs[key] === '') {
          delete newNode.attrs[key];
        }
      }
    }

    Utils.extend(this.attrs, newNode.attrs);

    let tmpStyle = new GraphStyle.GraphChartNodeStyles(newNode.styles);
    Utils.extend(this.styles, tmpStyle);

    Utils.extend(this.others, newNode.others);
    Utils.extend(this.labels, newNode.labels);

    return this;
  }

  /**
   * Merge external node. Prefer old node values.
   * @param  {ExternalNode} exNode [description]
   * @return {this}                [description]
   */
  public defaultsNode(exNode: ExternalNode): this {
    let newNode = Utils.cloneDeep(exNode);

    Utils.defaultsDeep(this.attrs, newNode.attrs);

    let tmpStyle = new GraphStyle.GraphChartNodeStyles(newNode.styles);
    Utils.defaultsDeep(this.styles, tmpStyle);

    Utils.defaultsDeep(this.others, newNode.others);
    Utils.defaultsDeep(this.labels, newNode.labels);

    return this;
  }

  /**
   * Assign external node values to old nodes.
   * @param  {ExternalNode} exNode [description]
   * @return {this}                [description]
   */
  public overwriteNode(exNode: ExternalNode): this {
    let newNode = Utils.cloneDeep(exNode);

    this.attrs = newNode.attrs;

    let tmpStyle = new GraphStyle.GraphChartNodeStyles(newNode.styles);
    this.styles = tmpStyle;

    this.others = newNode.others;
    this.labels = newNode.labels;

    return this;
  }
}

export function getNodeID(node: ExternalNode): string {
  return `${node.type}#${node.id}`;
}

export function createGraphChartDataNode(node: ExternalNode): GraphChartDataNode {
  return new GraphChartDataNode(getNodeID(node), node);
}
