/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Define base class of all links object.
 *
 * Created on: Oct 6th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/
import { ItemsData } from './items.data';

/**
 * Base link object.
 * @preferred
 */
export class ItemsDataLink extends ItemsData {
  /**
   * The unique indentifier of the link
   * @type {string}
   */
  public id: string;

  /**
   * id of the source node which is the node where the link originates.
   * @type {string}
   */
  public from: string;

  /**
   * id of the target node.
   * @type {string}
   */
  public to: string;

  /**
   * Attribute object of the link.
   * @type {Object}
   */
  public attrs: {
    [key: string]: number | string | boolean | Object;
  };

  /**
   * visualization style of the link object.
   * @type {Object}
   */
  public styles: Object;

  /**
   * This filed can be used to provide multiple classes for styling.
   * Such as className = "classA classB classN";
   * @type {string}
   */
  public className: string;

  constructor(internalID: string, sourceID: string, targetID: string) {
    super();

    this.id = internalID;
    this.from = sourceID;
    this.to = targetID;
    this.className = '';
  }
}
