/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: GVIS library Color Class.
 *
 * Created on: Oct 7th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/

/**
 * Color class for GVIS.
 */
export class Color {
  private static c10: string = '1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf';
  private static c10a: string = 'aec7e8ffbb7898df8aff9896c5b0d5c49c94f7b6d2c7c7c7dbdb8d9edae5';
  private static c10b: string = '393b795254a36b6ecf9c9ede6379398ca252b5cf6bcedb9c8c6d31bd9e39';
  private static c10c: string = 'e7ba52e7cb94843c39ad494ad6616be7969c7b4173a55194ce6dbdde9ed6';
  private static c10d: string = '3182bd6baed69ecae1c6dbefe6550dfd8d3cfdae6bfdd0a231a35474c476';
  private static c10e: string = 'a1d99bc7e9c0756bb19e9ac8bcbddcdadaeb636363969696bdbdbdd9d9d9';

  private static c20: string = Color.c10 + Color.c10a;
  private static c30: string = Color.c10 + Color.c10a + Color.c10b;
  private static c40: string = Color.c10 + Color.c10a + Color.c10b + Color.c10c;
  private static c50: string = Color.c10 + Color.c10a + Color.c10b + Color.c10c + Color.c10d;
  private static c60: string = Color.c10 + Color.c10a + Color.c10b + Color.c10c + Color.c10d + Color.c10e;

  /**
   * current color schema color string.
   * @type {string}
   */
  private colorSchema: string;

  /**
   * color array which is parsed from colorSchema.
   * @type {string[]}
   */
  private colorArray: string[];

  /**
   * key is the type, it will be mapped to a index value or a color string.
   * Index will be used to get the color string in colorArray, by colorArray[index % colorArray.length];
   * Color string will be used as color.
   */
  private colorMap: {
    [key: string]: {
      index?: number;
      color?: string;
    };
  };

  private parseColorSchema(schema: string): string[] {
    try {
      return schema.match(/.{6}/g).map(function (x) {
        return '#' + x;
      });
    } catch (err) {
      console.error(err, 'Use default color schema c10.');
      this.colorSchema = Color.c10;

      return this.colorSchema.match(/.{6}/g).map(function (x) {
        return '#' + x;
      });
    }
  }

  constructor(theme?: string) {
    if (theme === undefined) {
      theme = 'c60';
    }

    this.colorSchema = (Color as any)[theme];
    this.colorMap = {};
    this.colorArray = this.parseColorSchema(this.colorSchema);
  }

  /**
   * Initialize color for a list of types.
   * @param  {string[]} types the list of types, example: ['Company', 'Car', 'Member']
   * @return {this}           [description]
   */
  // public initialization(types: string[]): this {
  //   types.forEach(function (type) {
  //     this.getColor(type);
  //   });

  //   return this;
  // }

  /**
   * get color for a type. Dunamically update the colorMap while getting color.
   * @type  {string} id for the color.
   * @return {string} color string.
   */
  public getColor(type: string): string {
    let result = '#000000';

    try {
      if (type === undefined) {
        throw new Error('getColor for a unknow type.');
      }

      if (this.colorMap[type] === undefined) {
        this.colorMap[type] = {
          index: Object.keys(this.colorMap).length,
        };
      }

      if (this.colorMap[type].color !== undefined) {
        result = this.colorMap[type].color;
      } else {
        result = this.colorArray[this.colorMap[type].index % this.colorArray.length];
      }
    } catch (err) {
      console.error(err);
      result = '#000000';
    }

    return result;
  }

  /**
   * Customize a color for a type, it will override the index color.
   * @type  {string}
   * @color  {string}
   * @return {this}
   */
  public setColor(type: string, color: string): this {
    this.colorMap[type] = this.colorMap[type] || {};
    this.colorMap[type].color = color;

    return this;
  }

  /**
   * Change the color shcema theme. It will change color schema and color array after set a acceptable theme.
   * @theme  {string}  Names of schema: 'c10', 'c10a', 'c10b', 'c10c', 'c10d', 'c10e', 'c20', 'c30', 'c40', 'c50', 'c60'
   * @return {this}
   */
  public setColorTheme(theme: string): this {
    this.setColorSchema((Color as any)[theme]);

    return this;
  }

  /**
   * Change the color schema for the color Array.
   * @schema  {string}
   * @return {this}
   */
  public setColorSchema(schema: string): this {
    this.colorSchema = schema;
    this.colorArray = this.parseColorSchema(schema);

    return this;
  }

  /**
   * Reset the color map object. Does not change the color schema or color array.
   * @return {this}
   */
  public reset(): this {
    this.colorMap = {};

    return this;
  }

  /**
   * use to generate random RGB color string. The random color could be duplicated.
   * @return {string} random RGB color string.
   */
  public randomColor(): string {
    let co: { (lor: string): string } = (lor: string) => {
      return (lor += [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'a', 'b', 'c', 'd', 'e', 'f'][Math.floor(Math.random() * 16)]) &&
        lor.length === 6
        ? lor
        : co(lor);
    };

    return '#' + co('');
  }

  /**
   * use to return the privagte memeber color array. It will retrun an array contains currently used colors.
   * @return {string[]} [description]
   */
  public getColorArray(): string[] {
    return this.colorArray;
  }
}
