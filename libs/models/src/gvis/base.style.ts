/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Base style class.
 *
 * Created on: Feb 17th, 2017
 *      Author: <PERSON><PERSON>
 ******************************************************************************/

import { Color } from './color';

/**
 * The base chart setting except for graph chart.
 */
export interface ChartSetting {
  render?: {
    container: Object;
    title?: {
      text?: string;
      subtext?: string;
    };
    legend?: {
      show: boolean;
    };
    grid?: {
      left?: number | string;
      top?: number | string;
      right?: number | string;
      bottom?: number | string;
    };
    textStyle?: {
      color?: string;
      fontStyle?: string;
      fontWeight?: string;
      fontFamily?: string;
      fontSize?: number;
    };
    xAxis?: {
      name?: string;
      nameLocation?: string;
      nameGap?: number;
      nameRotate?: number;
      nameTextStyle?: {
        color?: string;
        fontStyle?: string;
        fontWeight?: string;
        fontFamily?: string;
        fontSize?: number;
      };
    };
    yAxis?: {
      name?: string;
      nameLocation?: string;
      nameGap?: number;
      nameRotate?: number;
      nameTextStyle?: {
        color?: string;
        fontStyle?: string;
        fontWeight?: string;
        fontFamily?: string;
        fontSize?: number;
      };
    };
    [options: string]: any;
  };
  formatter?: {
    tooltip?: { (...args: any[]): string };
  };
  language?: {
    selectedLanguage?: string;
    localization?: {
      [language: string]: {
        vertex?: {
          [type: string]:
            | string
            | {
                type?: string;
                attrs: {
                  [attribute: string]:
                    | string
                    | {
                        attr?: string;
                        values: {
                          [value: string]: string;
                        };
                      };
                };
              };
        };
        edge?: {
          [type: string]:
            | string
            | {
                type?: string;
                attrs: {
                  [attribute: string]:
                    | string
                    | {
                        attr?: string;
                        values: {
                          [value: string]: string;
                        };
                      };
                };
              };
        };
      };
    };
  };
}

/**
 * Base style class for all charts except the graph chart.
 */
export class BaseStyle {
  public defaultChartStyle: {
    [styles: string]: any;
  };

  public color: Color;
  public customizedColor: {
    [keys: string]: string;
  };

  constructor() {
    this.color = new Color('c10');
    this.customizedColor = {};

    this.defaultChartStyle = {
      backgroundColor: 'rgba(245, 245, 245, 0.5)',
      color: this.color.getColorArray(),
      textStyle: {
        color: '#111',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontFamily: 'sans-serief',
        fontSize: 12,
      },
      grid: {
        show: false,
        zlevel: 0,
        z: 2,
        left: 50,
        top: 20,
        right: 30,
        bottom: 50,
        width: 'auto',
        height: 'auto',
        containLabel: false,
        backgroundColor: 'transparent',
        borderColor: '#ccc',
        borderWidth: 1,
        shadowOffsetX: 0,
        shadowOffsetY: 0,
      },
      title: {
        text: '',
        link: '',
        target: 'blank',
        subtext: '',
        sublink: '',
        subtarget: 'blank',
        padding: 5,
        itemGap: 5,
        zlevel: 0,
        z: 2,
        left: 'center',
        top: 15,
        right: 'auto',
        bottom: 'auto',
        backgroundColor: 'transparent',
        shadowOffsetX: 0,
        shadowOffsetY: 0,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        borderWidth: 0,
        padding: 5,
        axisPointer: {},
      },
      legend: {
        show: false,
        zlevel: 0,
        z: 2,
        left: 'center',
        top: 'auto',
        right: 'auto',
        bottom: 5,
        width: 'auto',
        height: 'auto',
        // orient: 'vertical',
        orient: 'horizontal',
        align: 'auto',
        padding: 5,
        itemGap: 10,
        itemWidth: 25,
        itemHeight: 14,
        formatter: null,
        selectedMode: true,
        inactiveColor: '#ccc',
      },
      yAxis: {
        splitNumber: 5,
      },
      xAxis: {
        splitNumber: 5,
      },
    };
  }

  /**
   * Set customized color by name for all ECharts based chart.
   * @param  {string} name  [description]
   * @param  {string} color [description]
   * @return {this}         [description]
   */
  public setColorByName(name: string, color: string): this {
    this.customizedColor[name] = color;
    return this;
  }

  /**
   * Return the color by name;
   * @param  {string} name [description]
   * @return {string}      [description]
   */
  public getColorByName(name: string): string {
    return this.customizedColor[name] || this.color.getColor(name);
  }
}
