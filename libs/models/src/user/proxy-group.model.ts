import { User, UserInfo } from './user.model';

/**
 * Represent a proxy group's JSON.
 *
 * @export
 */
export interface ProxyGroupJson extends UserInfo {
  rule: string;
}

/**
 * GSQL proxy group.
 *
 * @export
 */
export class ProxyGroup extends User {
  private rule: string;

  constructor(info: ProxyGroupJson) {
    super(info);
    this.rule = info.rule || '';
  }

  updateInfo(info: ProxyGroupJson, graphName: string) {
    super.updateInfo(info, graphName);
    this.rule = info.rule || '';
  }

  /** Return the proxy rule of the group. */
  getRule(): string {
    return this.rule;
  }
}
