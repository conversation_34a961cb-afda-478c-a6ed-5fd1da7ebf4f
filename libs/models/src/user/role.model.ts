import { GSQLPrivilege } from '../utils';

/**
 * Role pivileges based on graph.
 */
export interface Privilege {
  graph: string;
  privilege: GSQLTypeLevelDataPrivilege | GSQLPrivilege[];
}

export interface GSQLTypeLevelDataPrivilege {
  privileges: GSQLPrivilege[];
  childPermissions?: GSQLAttrLevelDataPrivilege;
}

export interface GSQLAttrLevelDataPrivilege {
  [name: string]: GSQLTypeLevelDataPrivilege;
}

/**
 * GSQL Role.
 */
export interface Role {
  role: string;
  graph: string;
  isGlobal: boolean;
  isBuiltIn: boolean;
  privileges: Privilege[];
}

export enum RoleType {
  Global = 'Global',
  Local = 'Local'
}
