import { GSQLPrivilege } from '../utils';
import { User, UserPrivilege, UserRole } from './user.model';

describe('UserModel', () => {

  it('should return correct values', () => {
    const config = {
      name: 'user',
      creators: {
        graph1: 'tigergraph',
        graph2: 'tigergraph'
      },
      roles: <UserRole>{
        graph1: ['observer'],
        graph2: ['designer', 'udr1']
      },
      privileges: <UserPrivilege>{
        graph1: { privileges: [GSQLPrivilege.ReadSchema] },
        graph2: { privileges: [GSQLPrivilege.ReadSchema, GSQLPrivilege.WriteSchema] }
      },
      isSuperUser: false
    };

    const user = new User(config);
    user.setCurrentGraph('graph1');
    expect(user.getRoles()).toEqual({
      graph1: ['observer'],
      graph2: ['designer', 'udr1']
    });
    expect(user.getPrivileges()).toEqual({
      graph1: { privileges: [GSQLPrivilege.ReadSchema] },
      graph2: { privileges: [GSQLPrivilege.ReadSchema, GSQLPrivilege.WriteSchema] }
    });
    expect(user.getUsername()).toEqual('user');
    expect(user.getCurrentGraph()).toEqual('graph1');
    expect(user.isSuperUser()).toBeFalsy();
    expect(user.getUserRole()).toEqual('observer');
    expect(user.getUserRoles()).toEqual(['observer']);
    expect(user.getUserRole('graph2')).toEqual('designer');
    expect(user.getUserRoles('graph2')).toEqual(['designer', 'udr1']);
    expect(user.hasPrivilege(GSQLPrivilege.ReadSchema)).toBeTruthy();
    expect(user.hasPrivilege(GSQLPrivilege.WriteSchema)).toBeFalsy();
    expect(user.getGraphList().length).toEqual(2);
    expect(user.getGraphList()[0]).toEqual('graph1');
    expect(user.getGraphList()[1]).toEqual('graph2');
  });
});
