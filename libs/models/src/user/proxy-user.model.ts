import { User, UserInfo } from './user.model';
import { ProxyGroup, ProxyGroupJson } from './proxy-group.model';

/**
 * Represent a proxy user's JSON.
 *
 * @export
 */
export interface ProxyUser<PERSON>son extends UserInfo {
  groups: ProxyGroupJson[];
}

/**
 * GSQL proxy user.
 *
 * @export
 */
export class ProxyUser extends User {
  private groups: ProxyGroup[];

  constructor(info: ProxyUserJson) {
    super(info);
    this.groups = [];
    info.groups.forEach(groupJson => {
      this.groups.push(new ProxyGroup(groupJson));
    });
  }

  updateInfo(info: ProxyUserJson, graphName: string) {
    super.updateInfo(info, graphName);
    this.groups = [];
    info.groups.forEach(groupJson => {
      this.groups.push(new ProxyGroup(groupJson));
    });
  }

  /** Return the groups the proxy user belongs to. */
  getGroups(): ProxyGroup[] {
    return this.groups;
  }
}
