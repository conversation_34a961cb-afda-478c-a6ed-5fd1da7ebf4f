import {
  CpuMemoryMetric,
  DiskMetric,
  QPSMetric,
  ServiceState,
  ServiceStateMetric,
} from './informant.interface';
import { Ts3RecordEntry } from '../ts3';
import { toInteger, groupBy, cloneDeep } from 'lodash';
import { GroupByKey } from '../time-series/group-by.enum';

export const InformantServices = ['GSE', 'GPE', 'RESTPP', 'GSQL', 'GUI'];

export enum InformantMetricType {
  CPU = 'cpu',
  MEMORY = 'memory',
  DISK_SPACE = 'diskSpace',
  SERVICE_STATE = 'serviceState',
  QPS = 'qps'
}

export const Informant = {
  cpu: {
    URL: '/api/informant/metrics/get/cpu-memory',
    name: 'cpu',
    metricName: 'CPUMemoryMetrics',
  },
  memory: {
    URL: '/api/informant/metrics/get/cpu-memory',
    name: 'mem',
    metricName: 'CPUMemoryMetrics',
  },
  diskSpace: {
    URL: '/api/informant/metrics/get/diskspace',
    name: 'diskspace',
    metricName: 'DiskMetrics',
  },
  serviceState: {
    URL: '/api/informant/service-status-history',
    name: 'servicestate',
    metricName: 'ServiceStatusEvents',
  },
  qps: {
    URL: '/api/informant/metrics/get/qps',
    name: 'qps',
    metricName: 'QPSMetrics',
  },
};

export class InformantMetricLogic {

  /**
   * Convert the CPU metrics from the Informant endpoint to the visual data points.
   *
   * @param metrics CpuMemoryMetric Array.
   * @returns Ts3RecordEntry Array.
   */
  static convertCPU(metrics: CpuMemoryMetric[]): Ts3RecordEntry[] {
    const entries: Ts3RecordEntry[] = [];
    metrics.forEach(metric => {
      // Skip if one of the required fields is undefined.
      if (!metric.ServiceDescriptor ||
        !metric.HostID ||
        !metric.CollectTimeStamps ||
        !metric.CPU ||
        !metric.CPU.CPUUsage
      ) {
        return;
      }

      // TODO: Support more services in the future.
      if (!InformantServices.includes(metric.ServiceDescriptor.ServiceName)) {
        return;
      }

      entries.push({
        detail: metric.CPU.CPUUsage,
        when: toInteger(metric.CollectTimeStamps / 10e8),
        where: metric.HostID,
        who: metric.ServiceDescriptor.ServiceName,
        what: Informant[InformantMetricType.CPU].name,
      });
    });
    return entries;
  }

  /**
   * Convert the Memory metrics from the Informant endpoint to the visual data points.
   *
   * @param metrics CpuMemoryMetric Array.
   * @returns Ts3RecordEntry Array.
   */
  static convertMemory(metrics: CpuMemoryMetric[]): Ts3RecordEntry[] {
    const entries: Ts3RecordEntry[] = [];
    metrics.forEach(metric => {
      // Skip if one of the required fields is undefined.
      if (!metric.ServiceDescriptor ||
        !metric.HostID ||
        !metric.CollectTimeStamps ||
        !metric.Memory ||
        !metric.Memory.MemoryUsageMB
      ) {
        return;
      }

      // TODO: Support more services in the future.
      if (!InformantServices.includes(metric.ServiceDescriptor.ServiceName)) {
        return;
      }

      entries.push({
        detail: metric.Memory.MemoryUsageMB,
        when: toInteger(metric.CollectTimeStamps / 10e8),
        where: metric.HostID,
        who: metric.ServiceDescriptor.ServiceName,
        what: Informant[InformantMetricType.MEMORY].name,
      });
    });
    return entries;
  }

  /**
   * Convert the DiskSpace metrics from the Informant endpoint to the visual data points.
   *
   * @param metrics DiskMetric Array.
   * @returns Ts3RecordEntry Array.
   */
  static convertDisk(metrics: DiskMetric[]): Ts3RecordEntry[] {
    const entries: Ts3RecordEntry[] = [];
    metrics.forEach(metric => {
      // Skip if one of the required fields is undefined.
      if (!metric.HostID ||
        !metric.CollectTimeStamps ||
        !metric.Disk ||
        !metric.Disk.Path ||
        !metric.Disk.SizeMB ||
        !metric.Disk.PathName) {
        return;
      }

      entries.push({
        detail: {
          disk: '',
          free: '',
          path: metric.Disk.Path,
          size: metric.Disk.SizeMB,
        },
        when: toInteger(metric.CollectTimeStamps / 10e8),
        where: metric.HostID,
        who: metric.Disk.PathName,
        what: Informant[InformantMetricType.DISK_SPACE].name,
      });
    });
    return entries;
  }

  /**
   * Convert the ServiceStatus metrics from the Informant endpoint to the visual data points.
   *
   * @param metrics ServiceStateMetric Array.
   * @returns Ts3RecordEntry Array.
   */
  static convertServiceStatus(metrics: ServiceStateMetric[], startTime: number, endTime: number) {
    const entries: Ts3RecordEntry[] = [];
    metrics.forEach(metric => {
      // Skip if one of the required fields is undefined.
      if (!metric.ServiceDescriptor ||
        !metric.HostId ||
        !metric.EventMeta ||
        !metric.ServiceStatus ||
        !metric.EventMeta.TimestampNS
      ) {
        return;
      }

      if (!metric.ServiceDescriptor.ServiceName) {
        return;
      }

      entries.push({
        detail:
          ServiceState[metric.ServiceStatus] ? ServiceState[metric.ServiceStatus] : 0,
        when: toInteger(metric.EventMeta.TimestampNS / 10e8),
        where: metric.HostId,
        who: metric.ServiceDescriptor.ServiceName,
        what: Informant[InformantMetricType.SERVICE_STATE].name,
      });
    });

    return InformantMetricLogic.postProcessServiceStatus(
      entries,
      startTime,
      endTime,
    );
  }

  static convertQPS(metrics: QPSMetric[]): Ts3RecordEntry[] {
    return metrics.reduce((res, cur) => {
      if (!cur.HostID || !cur.CollectTimeStamps || !cur.QPS) {
        return res;
      }
      const ts3RecordEntry: Ts3RecordEntry = {
        detail: {
          QPS: cur.QPS.QPS || 0,
          average_latency: cur.QPS.AverageLatencyMS || 0,
          max_latency: cur.QPS.MaxLatencyMS || 0,
          timeout: cur.QPS.TimeoutRequestNum || 0,
          min_latency: cur.QPS.MinLatencyMS || 0,
        },
        when: toInteger(Number(cur.QPS.EndTimestampNS) / 1e9),
        where: cur.HostID,
        who: cur.QPS.Endpoint,
        what: Informant[InformantMetricType.QPS].name,
      };
      return [...res, ts3RecordEntry];
    }, [] as Ts3RecordEntry[]);
  }
  /**
   * Post-process the service status visual data points.
   *
   * @param entries Ts3RecordEntry[].
   * @param startTime number.
   * @param endTime number.
   * @returns Ts3RecordEntry Array.
   */
  static postProcessServiceStatus(
    entries: Ts3RecordEntry[],
    startTime: number,
    endTime: number
  ): Ts3RecordEntry[] {
    const newEntries: Ts3RecordEntry[] = [];

    // handle if input only have metrics with timestamp greater than endTime.
    entries = entries.filter(entry => entry.when <= endTime);

    const machineGroups = groupBy(entries, GroupByKey.Where);
    Object.keys(machineGroups).forEach(machine => {
      const serviceGroups = groupBy(machineGroups[machine], GroupByKey.Who);
      Object.keys(serviceGroups).forEach(service => {
        if (serviceGroups[service].length === 0) {
          return;
        }

        // Find the visual data points with the largest timestamp which is <= end time.
        // Create a new visual data point with the same metric but the end time as its timestamp.
        let maxEndTime = 0;
        let maxEndTimeEntry: Ts3RecordEntry;
        serviceGroups[service].forEach(entry => {
          if (maxEndTime < entry.when && entry.when <= endTime) {
            maxEndTime = entry.when;
            maxEndTimeEntry = entry;
          }
        });
        let newEntry = cloneDeep(maxEndTimeEntry);
        newEntry.when = endTime;
        newEntries.push(newEntry);

        // Find the visual data points with the smallest timestamp which is <= start time.
        // Create a new visual data point with the same metric but the start time as its timestamp.
        let maxStartTime = 0;
        let maxStartTimeEntry: Ts3RecordEntry;
        serviceGroups[service].forEach(entry => {
          if (maxStartTime < entry.when && entry.when <= startTime) {
            maxStartTime = entry.when;
            maxStartTimeEntry = entry;
          }
        });
        if (maxStartTimeEntry) {
          newEntry = cloneDeep(maxStartTimeEntry);
          newEntry.when = startTime;
          newEntries.push(newEntry);
        }
      });
    });

    // Filter out the visual data points outside the time range.
    entries = entries.filter(
      entry => startTime < entry.when && entry.when < endTime
    );

    entries.push(...newEntries);
    return entries;
  }
}
