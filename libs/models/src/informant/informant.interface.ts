/**
 * Base interface for informant metric.
 *
 * @export
 */
export interface InformantMetric {
  EventMeta: {
    Targets: any;
    EventId: string;
    TimestampNS: number;
    Source: any;
  };
}

/**
 * CPU and Memory metrics.
 *
 * @export
 * @extends {InformantMetric}
 */
export interface CpuMemoryMetric extends InformantMetric {
  HostID: string;
  ServiceDescriptor: {
    ServiceName: string;
    Partition: number;
    Replica: number;
  };
  CollectTimeStamps: number;
  CPU: {
    CPUUsage: number;
  };
  Memory: {
    MemoryUsageMB: number;
  };
}

/**
 * Disk metric.
 * 4 possible path names: log, kafka, gstore, home.
 *
 * @export
 * @extends {InformantMetric}
 */
export interface DiskMetric extends InformantMetric {
  HostID: string;
  CollectTimeStamps: number;
  Disk: {
    PathName: string;
    MountPoint: string;
    Path: string;
    SizeMB: number;
    FreeSizeMB: number;
  };
}

export interface QPSMetric extends InformantMetric {
  HostID: string;
  CollectTimeStamps: string;
  QPS: {
    Endpoint: string;
    EndTimestampNS: string;
    QPS?: number;
    AverageLatencyMS?: number;
    MaxLatencyMS?: number;
    MinLatencyMS?: number;
    TimeoutRequestNum?: number;
    CompletedRequestNum?: number;
  }
}

/**
 * Network Metric.
 *
 * @export
 * @extends {InformantMetric}
 */
export interface NetworkMetric extends InformantMetric {
  HostID: string;
  CollectTimeStamps: string;
  Network: any;
}

/**
 * Service status.
 *
 * @export
 * @extends {InformantMetric}
 */
export interface ServiceStateMetric extends InformantMetric {
  HostId: string;
  ServiceDescriptor: {
    ServiceName: string;
    Replica: number;
    Partition: number;
  };
  ServiceStatus: string;
  ProcessState: string;
}

export interface Response {
  error: boolean;
  msg: string;
}

/**
 *  Service state metric.
 *
 * @export
 * @enum {number}
 */
export enum ServiceState {
  Offline = 0,
  Warmup = 1,
  Online = 2,

  // TODO: Support more status in the future.
  // StatusUnchanged,
  // StatusUnknown,
  // Readonly,
  // Paused,
  // Stopping,
  // Down,
}
