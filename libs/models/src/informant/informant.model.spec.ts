import {
  Informant,
  InformantMetricLogic,
  InformantMetricType,
} from './informant.model';
import {
  CpuMemoryMetric,
  DiskMetric,
  ServiceState,
  ServiceStateMetric,
} from './informant.interface';
import { Ts3RecordEntry } from '../ts3';
import { cloneDeep } from 'lodash';

const mockCpuMemoryInformantMetric: CpuMemoryMetric = {
  EventMeta: {
    Targets: [
      {
        ServiceName: 'IFM',
      },
    ],
    EventId: '669945b4e99c4511beca17b46e43455d',
    TimestampNS: 1654799109018390286,
    Source: {
      ServiceName: 'EXE',
      Partition: 1,
      Replica: 1,
    },
  },
  ServiceDescriptor: {
    ServiceName: 'GSE',
    Partition: 1,
    Replica: 1,
  },
  HostID: 'm1',
  CollectTimeStamps: 1654799109002112140,
  CPU: {
    CPUUsage: 0.1777919,
  },
  Memory: {
    MemoryUsageMB: 39,
  },
};

const mockCpuTs3Entry: Ts3RecordEntry = {
  detail: 0.1777919,
  when: 1654799109,
  where: 'm1',
  who: 'GSE',
  what: Informant[InformantMetricType.CPU].name,
};

const mockMemoryTs3Entry: Ts3RecordEntry = {
  detail: 39,
  what: 'mem',
  when: 1654799109,
  where: 'm1',
  who: 'GSE',
};

const mockDiskInformantMetric: DiskMetric = {
  EventMeta: {
    Targets: [
      {
        ServiceName: 'IFM',
      },
    ],
    EventId: '669945b4e99c4511beca17b46e43455d',
    TimestampNS: 1654799109018390286,
    Source: {
      ServiceName: 'EXE',
      Partition: 1,
    },
  },
  HostID: 'm1',
  CollectTimeStamps: 1654799109002112140,
  Disk: {
    PathName: 'Home',
    MountPoint: '/home/<USER>',
    Path: '/home/<USER>',
    SizeMB: 31406084259,
    FreeSizeMB: 1,
  },
};

const mockDiskTs3Entry: Ts3RecordEntry = {
  detail: {
    disk: '',
    free: '',
    path: '/home/<USER>',
    size: 31406084259,
  },
  what: Informant[InformantMetricType.DISK_SPACE].name,
  when: 1654799109,
  where: 'm1',
  who: 'Home',
};

const mockServiceStatuInformantMetric: ServiceStateMetric = {
  EventMeta: {
    Targets: [
      {
        ServiceName: 'IFM',
      },
    ],
    EventId: 'b14e5bd325394ceb96cea8bb451d5fb5',
    TimestampNS: 1600000000059785811,
    Source: {
      ServiceName: 'EXE',
      Partition: 1,
    },
  },
  HostId: 'm1',
  ServiceDescriptor: {
    ServiceName: 'GSE',
    Replica: 1,
    Partition: 1,
  },
  ServiceStatus: 'Online',
  ProcessState: 'Running',
};

const mockServiceStatuTs3Entry: Ts3RecordEntry = {
  detail: ServiceState.Online,
  what: Informant[InformantMetricType.SERVICE_STATE].name,
  when: 1600000000,
  where: 'm1',
  who: 'GSE',
};

describe('InformantMetricLogic', () => {

  describe('should convert CpuMemoryMetric into Ts3RecordEntry for CPU data', () => {
    it('successfully with valid informant metric', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);

      const entries = InformantMetricLogic.convertCPU([cpuInformantMetric]);
      expect(entries).toEqual([mockCpuTs3Entry]);
    });

    it('failed to convert due to no ServiceDescriptor', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.ServiceDescriptor;

      const entries = InformantMetricLogic.convertCPU([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to invalid service name', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      cpuInformantMetric.ServiceDescriptor.ServiceName = 'sfdsdfdsfsf';

      const entries = InformantMetricLogic.convertCPU([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no Host ID', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.HostID;

      const entries = InformantMetricLogic.convertCPU([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no CollectedTimeStamp', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.CollectTimeStamps;

      const entries = InformantMetricLogic.convertCPU([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });
  });

  describe('should convert CpuMemoryMetric into Ts3RecordEntry for Memory data', () => {
    it('successfully with valid informant metric', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([mockMemoryTs3Entry]);
    });

    it('failed to convert due to no ServiceDescriptor', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.ServiceDescriptor;

      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to invalid service name', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      cpuInformantMetric.ServiceDescriptor.ServiceName = 'sfdsdfdsfsf';

      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no Host ID', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.HostID;

      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no CollectedTimeStamp', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.CollectTimeStamps;

      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no Memory', () => {
      const cpuInformantMetric = cloneDeep(mockCpuMemoryInformantMetric);
      delete cpuInformantMetric.Memory;

      const entries = InformantMetricLogic.convertMemory([cpuInformantMetric]);
      expect(entries).toEqual([]);
    });
  });

  describe('should convert DiskMetric into Ts3RecordEntry for DiskSpace data', () => {
    it('successfully with correct input', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([mockDiskTs3Entry]);
    });

    it('failed to convert due to no Host ID', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.HostID;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no CollectedTimeStamp', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.CollectTimeStamps;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no Disk Object', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.Disk;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to Disk does not have PathName', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.Disk.PathName;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to Disk does not have Path', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.Disk.Path;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });

    it('failed to convert due to Disk does not have Path', () => {
      const diskInformantMetric: DiskMetric = cloneDeep(mockDiskInformantMetric);
      delete diskInformantMetric.Disk.SizeMB;

      const entries = InformantMetricLogic.convertDisk([diskInformantMetric]);
      expect(entries).toEqual([]);
    });
  });

  describe('should post-process Service Status data', () => {
    const startTime = 1700000000;
    const endTime = 1800000000;
    const outBoundTime = 1850000000;

    /*
      Input: m1
              |
      GSE ON  |   X
              |
      GSE OFF |
               --------------------
                  t1   sT        eT

      Output: m1
               |
      GSE ON   |       X          X
               |
      GSE OFF  |
               --------------------
                  t1   sT        eT
    */
    it('successfully with unchanged state', () => {
      const entries = InformantMetricLogic.postProcessServiceStatus(
        [cloneDeep(mockServiceStatuTs3Entry)],
        startTime,
        endTime
      );

      expect(entries.length).toEqual(2);
      entries.forEach(entry => {
        expect(entry.detail).toEqual(mockServiceStatuTs3Entry.detail);
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });
      expect(entries[0].when).toEqual(endTime);
      expect(entries[1].when).toEqual(startTime);
    });

    /*
      Input:  m1
              |
      GSE ON  | X
              |
      GSE OFF |             X
              ---------------------
                t1    sT    t2   eT

      Output: m1
              |
      GSE ON  |       X
              |
      GSE OFF |             X    X
              ---------------------
                t1    sT    t2   eT
    */
    it('successfully with changing state within Time range', () => {
      const changeTime = 1750000000;
      const mockStatuMetricArray: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      mockStatuMetricArray[1].when = changeTime;
      mockStatuMetricArray[1].detail = ServiceState.Offline;

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );

      expect(entries.length).toEqual(3);
      entries.forEach(entry => {
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });
      expect(entries[0].when).toEqual(changeTime);
      expect(entries[0].detail).toEqual(ServiceState.Offline);
      expect(entries[1].when).toEqual(endTime);
      expect(entries[1].detail).toEqual(ServiceState.Offline);
      expect(entries[2].when).toEqual(startTime);
      expect(entries[2].detail).toEqual(ServiceState.Online);
    });

    /*
      Input: m1
              |
      GSE ON  |  X
              |
      GSE OFF |       X
              --------------------
                 t1  sT       eT

      Output: m1
              |
      GSE ON  |
              |
      GSE OFF |       X       X
              --------------------
                 t1  sT       eT
    */
    it('successfully with changing state at StartTime', () => {
      const mockStatuMetricArray: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      mockStatuMetricArray[1].when = startTime;
      mockStatuMetricArray[1].detail = ServiceState.Offline;

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );

      expect(entries.length).toEqual(2);
      entries.forEach(entry => {
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });

      expect(entries[0].when).toEqual(endTime);
      expect(entries[0].detail).toEqual(ServiceState.Offline);
      expect(entries[1].when).toEqual(startTime);
      expect(entries[1].detail).toEqual(ServiceState.Offline);
    });

    /*
      Input: m1
              |
      GSE ON  |  X
              |
      GSE OFF |                 X
              --------------------
                 t1   sT        eT

      Output: m1
              |
      GSE ON  |       X
              |
      GSE OFF |                 X
              --------------------
                 t1   sT        eT
    */
    it('successfully with changing state at endTime', () => {
      const mockStatuMetricArray: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      mockStatuMetricArray[1].when = endTime;
      mockStatuMetricArray[1].detail = ServiceState.Offline;

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );

      expect(entries.length).toEqual(2);
      entries.forEach(entry => {
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });

      expect(entries[0].when).toEqual(endTime);
      expect(entries[0].detail).toEqual(ServiceState.Offline);
      expect(entries[1].when).toEqual(startTime);
      expect(entries[1].detail).toEqual(mockServiceStatuTs3Entry.detail);
    });

    /*
      Input: m1
              |
      GSE ON  |  X
              |
      GSE OFF |                         X
              ----------------------------------
                 t1    sT        eT     t2

      Output: m1
              |
      GSE ON  |          X        X
              |
      GSE OFF |
              ----------------------------------
                  t1    sT        eT     t2
    */
    it('successfully with timestamp greater than endTime included', () => {
      const mockStatuMetricArray: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      mockStatuMetricArray[1].when = outBoundTime;
      mockStatuMetricArray[1].detail = ServiceState.Offline;

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries.length).toEqual(2);
      entries.forEach(entry => {
        expect(entry.detail).toEqual(mockServiceStatuTs3Entry.detail);
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });
      expect(entries[0].when).toEqual(endTime);
      expect(entries[1].when).toEqual(startTime);
    });

    /*
      Input: m1
              |
      GSE ON  |                       X
              |
      GSE OFF |                              X
                ---------------------------------
                      sT        eT    t1    t2

      Output: m1
              |
      GSE ON  |
              |
      GSE OFF |
                ---------------------------------
                      sT        eT    t1    t2
    */
    it('failed to process due to all metrics with TimeStamp greater than EndTime.', () => {
      const mockStatuMetricArray: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      mockStatuMetricArray[0].when = outBoundTime;
      mockStatuMetricArray[1].when = outBoundTime;

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });

    it('failed to process due to empty metrics.', () => {
      const mockStatuMetricArray: Ts3RecordEntry[] = [];

      const entries = InformantMetricLogic.postProcessServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });
  });

  describe('should convert ServiceStatuMetric into Ts3RecordEntry for ServiceStatu data', () => {
    const startTime = 1700000000;
    const endTime = 1800000000;

    it('successfully with valid informant metric', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];

      const expectedOuput: Ts3RecordEntry[] = [
        cloneDeep(mockServiceStatuTs3Entry),
        cloneDeep(mockServiceStatuTs3Entry),
      ];
      expectedOuput[0].when = endTime;
      expectedOuput[1].when = startTime;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      entries.forEach(entry => {
        expect(entry.detail).toEqual(mockServiceStatuTs3Entry.detail);
        expect(entry.what).toEqual(mockServiceStatuTs3Entry.what);
        expect(entry.where).toEqual(mockServiceStatuTs3Entry.where);
        expect(entry.who).toEqual(mockServiceStatuTs3Entry.who);
      });
      expect(entries[0].when).toEqual(endTime);
      expect(entries[1].when).toEqual(startTime);
    });

    it('failed to convert due to no ServiceDescriptor', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].ServiceDescriptor;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no ServiceStatus', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].ServiceStatus;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });
    it('failed to convert due to no Host ID', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].HostId;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });
    it('failed to convert due to no EvenMeta', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].EventMeta;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no EvenMeta TimeStamp', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].EventMeta.TimestampNS;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });

    it('failed to convert due to no ServiceName', () => {
      const mockStatuMetricArray: ServiceStateMetric[] = [
        cloneDeep(mockServiceStatuInformantMetric),
      ];
      delete mockStatuMetricArray[0].ServiceDescriptor.ServiceName;

      const entries = InformantMetricLogic.convertServiceStatus(
        mockStatuMetricArray,
        startTime,
        endTime
      );
      expect(entries).toEqual([]);
    });
  });

});
