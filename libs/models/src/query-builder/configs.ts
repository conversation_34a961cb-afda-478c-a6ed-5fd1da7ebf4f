import { EditorConfiguration } from 'codemirror';
import { GraphChartSetting } from '../gvis';

import { CheckboxButton, IconButton, SeparatorButton, ButtonBase } from '../button';

/**
 * Toolbar configurations.
 *
 * @export
 * @interface ToolbarConfig
 */
export interface ToolbarConfig {
  top?: ButtonBase[];
  hideTop?: boolean;

  left?: ButtonBase[];
  hideLeft?: boolean;

  // TODO (WCAG): add hideBottom and hideRight when needed.
  bottom?: ButtonBase[];
  right?: ButtonBase[];

  asTab?: boolean;
}

/**
 * Console toolbar buttons.
 *
 * @export
 * @enum {number}
 */
export const enum ConsoleToolbarButton {
  Expand,
  Schema,
  Graph,
  Json,
  Table,
  Problems
}

/**
 * Pattern toolbar buttons.
 *
 * @export
 * @enum {number}
 */
export const enum PatternToolbarButton {
  Save,
  SaveAs,
  GSQL,
  Console,
  ConfigPanel,
  ViewOptions,
  Run,

  Separator1,

  <PERSON>do,
  <PERSON>o,
  <PERSON>,
  Copy,
  Delete,

  Separator2,

  PatternVertex,
  <PERSON>ternEdge,
  Pick,
  Merge,
  PatternView,

  Separator3,

  Widget,
  // NotExist,
  Filter,
  Attachment,
  OrderBy,
  Limit,
  Output
}

/**
 * Pattern widget buttons.
 *
 * @export
 * @enum {number}
 */
export const enum PatternWidgetButton {
  Intersect,
  Union,
  Subtract,
  Within,
  ShortestPath,
  None,
}

// How many input patterns each widget takes.
export const PatternWidgetInputNumber = [
  2, 2, 2, 1, 2,
];

// How many output patterns each widget produces.
export const PatternWidgetOutputNumber = [
  1, 1, 1, 1, 0,
];

/**
 * Console toolbar configurations.
 *
 * @export
 * @returns {ToolbarConfig}
 */
export function getConsoleToolbarConfig(): ToolbarConfig {
  return {
    asTab: true,
    left: [
      new IconButton({
        key: 'expand',
        icon: 'fullscreen',
        tooltip: 'Expand',
        tooltipPosition: 'right'
      }),
      new IconButton({
        key: 'schema',
        icon: 'schema',
        tooltip: 'Graph schema',
        tooltipPosition: 'right'
      }),
      new IconButton({
        key: 'graph',
        icon: 'graph',
        tooltip: 'Visualize graph result',
        tooltipPosition: 'right'
      }),
      new IconButton({
        key: 'json',
        icon: 'json',
        tooltip: 'View JSON result',
        tooltipPosition: 'right'
      }),
      new IconButton({
        key: 'table',
        icon: 'table',
        tooltip: 'View table result',
        tooltipPosition: 'right'
      }),
      new IconButton({
        key: 'problems',
        icon: 'error',
        tooltip: 'Problems',
        tooltipPosition: 'right'
      })
    ]
  };
}

/**
 * Pattern toolbar configurations.
 *
 * @export
 * @param {Map<string, any>} dataModels
 * @returns {ToolbarConfig}
 */
export function getPatternToolbarConfig(dataModels: Map<string, any>): ToolbarConfig {
  return {
    top: [
      new IconButton({
        key: 'save',
        icon: 'save',
        tooltip: 'Save the graph pattern',
        disabled: true
      }),
      new IconButton({
        key: 'saveAs',
        icon: 'save-as',
        tooltip: 'Save the graph pattern with a different name',
        disabled: true
      }),
      new IconButton({
        key: 'gsql',
        icon: 'gsql',
        tooltip: 'Save as a GSQL query',
        disabled: true
      }),
      new IconButton({
        key: 'console',
        icon: 'video_label',
        tooltip: 'Console'
      }),
      new IconButton({
        key: 'configPanel',
        icon: 'list',
        tooltip: 'Configuration panel',
        disabled: true
      }),
      new IconButton({
        key: 'viewOptions',
        icon: 'view-options',
        tooltip: 'Render pattern options',
        disabled: true,
        menu: [
          new CheckboxButton({
            key: 'patternDetail',
            label: 'Pattern detail',
            model: dataModels.get('patternDetail')
          }),
          new CheckboxButton({
            key: 'patternAddOn',
            label: 'Pattern add-on',
            model: dataModels.get('patternAddOn')
          }),
          new CheckboxButton({
            key: 'outputGlow',
            label: 'Output glow',
            model: dataModels.get('outputGlow')
          })
        ]
      }),
      new IconButton({
        key: 'run',
        icon: 'play_arrow',
        tooltip: 'Run',
        disabled: true
      }),
      new SeparatorButton(),
      new IconButton({
        key: 'undo',
        icon: 'undo',
        tooltip: 'Undo',
        disabled: true
      }),
      new IconButton({
        key: 'redo',
        icon: 'redo',
        tooltip: 'Redo',
        disabled: true
      }),
      new IconButton({
        key: 'edit',
        icon: 'edit',
        tooltip: 'Edit',
        disabled: true
      }),
      new IconButton({
        key: 'copy',
        icon: 'content_copy',
        tooltip: 'Duplicate selected patterns',
        disabled: true
      }),
      new IconButton({
        key: 'delete',
        icon: 'delete',
        tooltip: 'Delete',
        disabled: true
      }),
      new SeparatorButton(),
      new IconButton({
        key: 'addPatternVertex',
        icon: 'add_circle',
        tooltip: 'Add a vertex pattern',
        disabled: true
      }),
      new IconButton({
        key: 'addPatternEdge',
        icon: 'trending_flat',
        tooltip: 'Add an edge pattern',
        disabled: true
      }),
      new IconButton({
        key: 'pick',
        icon: 'pick',
        tooltip: 'Pick',
        disabled: true
      }),
      new IconButton({
        key: 'merge',
        icon: 'merge',
        tooltip: 'Merge',
        disabled: true
      }),
      new IconButton({
        key: 'addPatternView',
        icon: 'pattern-view',
        tooltip: 'Add a pattern view',
        disabled: true
      }),
      new SeparatorButton(),
      new IconButton({
        key: 'widget',
        icon: 'extension',
        tooltip: 'Widget',
        disabled: true,
        menu: [
          new IconButton({
            key: 'intersect',
            icon: 'intersect',
            label: 'Intersect'
          }),
          new IconButton({
            key: 'union',
            icon: 'union',
            label: 'Union'
          }),
          new IconButton({
            key: 'subtract',
            icon: 'subtract',
            label: 'Subtract'
          }),
          new IconButton({
            key: 'within',
            icon: 'within',
            label: 'Within'
          }),
          // new IconButton({
          //   key: 'shortest-path',
          //   icon: 'shortest-path',
          //   label: 'Shortest Path'
          // }),
        ]
      }),
      // new IconButton({
      //   key: 'deny',
      //   icon: 'deny',
      //   tooltip: 'Not exist',
      //   disabled: true
      // }),
      new IconButton({
        key: 'filter',
        icon: 'filter',
        tooltip: 'Filter',
        disabled: true
      }),
      new IconButton({
        key: 'attachment',
        icon: 'aggregation',
        tooltip: 'attachment',
        disabled: true
      }),
      new IconButton({
        key: 'order',
        icon: 'order',
        tooltip: 'Order by',
        disabled: true
      }),
      new IconButton({
        key: 'limit',
        icon: 'limit',
        tooltip: 'Limit',
        disabled: true
      }),
      new IconButton({
        key: 'output',
        icon: 'output',
        tooltip: 'In result',
        disabled: true
      }),
      new SeparatorButton(),
      new IconButton({
        key: 'patternVisInfo',
        icon: 'description',
        tooltip: 'Show visual pattern information',
        disabled: true
      })
    ]
  };
}

/**
 * Pattern chart configurations.
 *
 * @export
 * @returns {GraphChartSetting}
 */
export function getPatternChartConfig(): GraphChartSetting {
  return {
    advanced: {
      hideArrorMinZoom: 0.1,
    },
    render: {
      info: {
        enabled: false
      },
      style: {
        selfLinkHeightFactor: 4,
        fadeTime: 0
      }
    }
  };
}

/**
 * Schema chart configurations.
 *
 * @export
 * @returns {GraphChartSetting}
 */
export function getSchemaChartConfig(): GraphChartSetting {
  return {
    render: {
      style: {
        selfLinkHeightFactor: 7
      },
      interaction: {
        nodesMovable: false
      }
    }
  };
}

/**
 * Json viewer configurations.
 *
 * @export
 * @returns {EditorConfiguration}
 */
export function getJsonViewerConfig(): EditorConfiguration {
  return {
    readOnly: true,
    cursorHeight: 0,
    mode: 'application/json',
    tabindex: -1,
    autofocus: false
  };
}
