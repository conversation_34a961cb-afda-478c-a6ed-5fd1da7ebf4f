import { ArithmeticOperator, FunctionType, RelationalOperator } from '../expression';
import { Edge, Graph, GSQLGraphJson, Vertex } from '../topology';
import { PatternJson } from '../visual-query-pattern';
import { DataType } from '../data';

import { AliasSchemaType, buildAliasSchema } from '.';

const mockGSQLGraphJsonGlobal: GSQLGraphJson = {
  GraphName: 'MyGraph',
  VertexTypes: [
    {
      Name: 'person',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [],
      Config: {
        STATS: 'OUTDEGREE_BY_EDGETYPE',
        PRIMARY_ID_AS_ATTRIBUTE: true
      },
      IsLocal: true
    },
    {
      Name: 'movie',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'title',
          AttributeType: {
            Name: 'STRING'
          }
        },
        {
          AttributeName: 'genres',
          AttributeType: {
            Name: 'STRING'
          }
        }
      ],
      Config: {
        STATS: 'OUTDEGREE_BY_EDGETYPE',
        PRIMARY_ID_AS_ATTRIBUTE: false
      }
    }
  ],
  EdgeTypes: [
    {
      Name: 'rate',
      FromVertexTypeName: 'person',
      ToVertexTypeName: 'movie',
      IsDirected: true,
      Config: {
        REVERSE_EDGE: 'reverse_rate'
      },
      Attributes: [
        {
          AttributeName: 'rating',
          AttributeType: {
            Name: 'DOUBLE'
          }
        },
        {
          AttributeName: 'rated_at',
          AttributeType: {
            Name: 'DATETIME'
          }
        }
      ]
    }
  ]
};
const mockPatternJsonGlobal: PatternJson = {
  name: 'RecommendMovie',
  patternVertexSets: [
    {
      output: true,
      existentialCheck: false,
      vertices: ['person'],
      isParam: true,
      ids: [],
      id: '1',
      filters: [
        {
          operands: [
            {
              valueType: DataType.Int,
              value: 'a',
              type: 'IntParam'
            },
            {
              value: 'b',
              valueType: DataType.Real,
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        }
      ],
      aggregations: [
        {
          name: 'singleValueInt',
          expression: {
            valueType: DataType.Int,
            value: 'i',
            type: 'IntParam'
          },
        },
        {
          name: 'singleValueReal',
          expression: {
            valueType: DataType.Real,
            value: 1.5,
            type: 'RealConstant'
          },
        },
        {
          name: 'singleValueString',
          expression: {
            valueType: DataType.String,
            value: 'a',
            type: 'StringConstant'
          },
        },
        {
          name: 'singleValueDatetime',
          expression: {
            valueType: DataType.Datetime,
            value: 'datetimeTest',
            type: 'DatetimeParam'
          },
        },
        {
          name: 'singleValueBool',
          expression: {
            valueType: DataType.Bool,
            value: true,
            type: 'BoolConstant'
          },
        },
        {
          name: 'aliasRef',
          expression: {
            value: 'm',
            type: 'AliasReference'
          },
        },
        {
          name: 'functionCollect',
          expression: {
            operands: [
              {
                valueType: DataType.Int,
                value: 1,
                type: 'IntConstant'
              }
            ],
            operator: FunctionType.Collect,
            type: 'FunctionExpression'
          }
        },
        {
          name: 'functionCount',
          expression: {
            operands: [],
            operator: FunctionType.Count,
            type: 'FunctionExpression'
          }
        },
        {
          name: 'arithmeticInt',
          expression: {
            operands: [
              {
                valueType: DataType.Int,
                value: 1,
                type: 'IntConstant'
              },
              {
                valueType: DataType.Int,
                value: 1,
                type: 'IntConstant'
              }
            ],
            operator: ArithmeticOperator.Plus,
            type: 'ArithmeticExpression'
          }
        },
        {
          name: 'arithmeticReal',
          expression: {
            operands: [
              {
                valueType: DataType.Int,
                value: 1,
                type: 'IntConstant'
              },
              {
                valueType: DataType.Real,
                value: 1.5,
                type: 'RealConstant'
              }
            ],
            operator: ArithmeticOperator.Plus,
            type: 'ArithmeticExpression'
          }
        },
        {
          name: 'arithmeticString',
          expression: {
            operands: [
              {
                valueType: DataType.String,
                value: 'a',
                type: 'StringConstant'
              },
              {
                valueType: DataType.Int,
                value: 'a',
                type: 'StringConstant'
              }
            ],
            operator: ArithmeticOperator.Plus,
            type: 'ArithmeticExpression'
          }
        }
      ],
      orderBy: [
        {
          asc: true,
          expression: {
            valueType: DataType.String,
            value: 'e',
            type: 'StringParam'
          }
        }
      ],
      hasLimit: false,
      limit: 10,
      paramName: 'p',
      alias: 'p'
    },
    {
      output: false,
      existentialCheck: false,
      vertices: ['movie'],
      isParam: false,
      ids: [],
      filters: [],
      aggregations: [],
      orderBy: [],
      hasLimit: false,
      limit: 10,
      id: '2',
      alias: 'm'
    }
  ],
  patternEdgeSets: [
    {
      output: false,
      existentialCheck: false,
      sourcePatternId: '1',
      targetPatternId: '2',
      edges: ['rate'],
      repeatAtLeast: 1,
      repeatAtMost: 1,
      filters: [
        {
          operands: [
            {
              value: 'r1',
              valueType: DataType.Int,
              type: 'IntParam'
            },
            {
              value: 'r2',
              valueType: DataType.Int,
              type: 'IntParam'
            }
          ],
          operator: RelationalOperator.NotEqual,
          type: 'ComparisonCondition'
        }
      ],
      notExist: false,
      alias: 'r'
    }
  ],
  patternWidgets: [],
  patternViews: [],
  filters: [],
  aggregations: [],
  postAggregationFilters: [],
  skip: 0,
  limit: -1,
  orderBy: [],
  description: ''
};

const vertexNamesToAttributes = {
  'p': {
    'id': 'STRING'
  },
  'm': {
    'title': 'STRING',
    'genres': 'STRING'
  }
};

const vertexNamesToAttachments = {
  'p': {
    'singleValueInt': 'INT',
    'singleValueReal': 'DOUBLE',
    'singleValueString': 'STRING',
    'functionCount': 'INT',
    'arithmeticInt': 'INT',
    'arithmeticReal': 'DOUBLE',
    'arithmeticString': 'STRING'
  }
};

const edgeNamesToAttributes = {
  'r': {
    'rating': 'DOUBLE',
    'rated_at': 'DATETIME'
  }
};

const edgeNameToSourceAndTargetNames = {
  'r': [
    {
      to: 'p',
      from: 'm'
    }
  ]
};

/**
 * Check if the mock alias schema vertices contain the expected aliases and attributes.
 *
 * @param {Vertex[]} vertices
 * @param {{
 *       expectedName: string,
 *       expectedAttributes: any
 *     }[]} expectedVertices
 */
function checkVerticesAreExpected(
  vertices: Vertex[],
  expectedVertices: {
    expectedName: string,
    expectedAttributes: any
  }[]
) {
  expect(vertices.length).toEqual(expectedVertices.length);

  for (const { expectedName, expectedAttributes } of expectedVertices) {
    const vertex = vertices.find((vertexType) => vertexType.name === expectedName);
    expect(vertex).toBeTruthy();

    const expectedAttributesKeys = Object.keys(expectedAttributes);
    expect(vertex.attributes.length).toEqual(expectedAttributesKeys.length);

    for (const expectedAttrName of expectedAttributesKeys) {
      expect(vertex.attributes.find((attr) =>
        attr.name === expectedAttrName &&
        attr.type.name === expectedAttributes[expectedAttrName])
      ).toBeTruthy();
    }
  }
}

/**
 * Check if the mock alias schema edges contain the expected aliases, source and target vertex aliases, and attributes.
 *
 * @param {Edge[]} edges
 * @param {{
 *     expectedName: string,
 *     expectedFromToVertexTypePairs: {
 *       to: string,
 *       from: string
 *     }[],
 *     expectedAttributes: any
 *   }[]} expectedEdges
 */
function checkEdgesAreExpected(
  edges: Edge[],
  expectedEdges: {
    expectedName: string,
    expectedFromToVertexTypePairs: {
      to: string,
      from: string
    }[],
    expectedAttributes: any
  }[]
) {
  expect(edges.length).toEqual(expectedEdges.length);

  for (const { expectedName, expectedFromToVertexTypePairs, expectedAttributes } of expectedEdges) {
    const edge = edges.find((edgeType) => edgeType.name === expectedName);
    expect(edge).toBeTruthy();
    expect(edge.fromToVertexTypePairs.length).toEqual(expectedFromToVertexTypePairs.length);

    for (let i = 0; i < expectedFromToVertexTypePairs.length; i++) {
      expect(edge.fromToVertexTypePairs[i].to).toEqual(expectedFromToVertexTypePairs[i].to);
      expect(edge.fromToVertexTypePairs[i].from).toEqual(expectedFromToVertexTypePairs[i].from);
    }

    const expectedAttributesKeys = Object.keys(expectedAttributes);
    expect(edge.attributes.length).toEqual(expectedAttributesKeys.length);

    for (const expectedAttrName of expectedAttributesKeys) {
      expect(edge.attributes.find((attr) =>
        attr.name === expectedAttrName &&
        attr.type.name === expectedAttributes[expectedAttrName])
      ).toBeTruthy();
    }
  }
}

describe('build-alias-schema', () => {
  it('it should build an alias schema for the initial pattern', () => {
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
    const mockAliasSchema = buildAliasSchema(
      mockGraph, mockPatternJsonGlobal, AliasSchemaType.INITIAL_ALIAS_SCHEMA
    );

    const expectedVertices = [
      {
        expectedName: 'p',
        expectedAttributes: vertexNamesToAttributes['p']
      },
      {
        expectedName: 'm',
        expectedAttributes: vertexNamesToAttributes['m']
      },
    ];
    checkVerticesAreExpected(mockAliasSchema.vertexTypes, expectedVertices);

    const expectedEdges = [
      {
        expectedName: 'r',
        expectedFromToVertexTypePairs: edgeNameToSourceAndTargetNames['r'],
        expectedAttributes: edgeNamesToAttributes['r']
      }
    ];
    checkEdgesAreExpected(mockAliasSchema.edgeTypes, expectedEdges);
  });

  it('it should build an alias schema for the order-by', () => {
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
    const mockAliasSchema = buildAliasSchema(
      mockGraph, mockPatternJsonGlobal, AliasSchemaType.ORDER_BY_ALIAS_SCHEMA
    );

    const expectedVertices = [
      {
        expectedName: 'p',
        expectedAttributes: {
          ...vertexNamesToAttributes['p'],
          ...vertexNamesToAttachments['p']
        }
      },
      {
        expectedName: 'm',
        expectedAttributes: vertexNamesToAttributes['m']
      },
    ];
    checkVerticesAreExpected(mockAliasSchema.vertexTypes, expectedVertices);

    const expectedEdges = [
      {
        expectedName: 'r',
        expectedFromToVertexTypePairs: edgeNameToSourceAndTargetNames['r'],
        expectedAttributes: edgeNamesToAttributes['r']
      }
    ];
    checkEdgesAreExpected(mockAliasSchema.edgeTypes, expectedEdges);
  });

  it('it should build an alias schema for the pattern view', () => {
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);

    const patternJson: PatternJson = {
      name: 'PatternView',
      description: '',
      patternVertexSets: [
        {
          vertices: ['person'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'p1',
          alias: 'p1',
          output: false,
          existentialCheck: false,
        },
        {
          vertices: ['movie'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'm1',
          alias: 'm1',
          output: false,
          existentialCheck: false,
        },
        {
          vertices: ['person'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'p2',
          alias: '',
          output: false,
          existentialCheck: false,
        },
      ],
      patternEdgeSets: [
        {
          edges: ['rate'],
          sourcePatternId: 'p1',
          targetPatternId: 'm1',
          repeatAtLeast: 1,
          repeatAtMost: 1,
          notExist: false,
          filters: [],
          id: 'e1',
          alias: 'e1',
          output: false,
          existentialCheck: false,
        },
        {
          edges: ['rate'],
          sourcePatternId: 'p2',
          targetPatternId: 'm1',
          repeatAtLeast: 1,
          repeatAtMost: 1,
          notExist: false,
          filters: [],
          id: 'e2',
          alias: '',
          output: false,
          existentialCheck: false,
        }
      ],
      patternWidgets: [],
      patternViews: [
        {
          vertexSetIds: ['p1', 'm1', 'p2'],
          edgeSetIds: ['e1', 'e2'],
          filters: [],
          orderBy: [],
          hasLimit: false,
          limit: 10,
          id: 'v1',
          alias: 'myView',
          output: true,
          existentialCheck: false,
        }
      ],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };

    const mockAliasSchema = buildAliasSchema(
      mockGraph, patternJson, AliasSchemaType.PATTERN_VIEW_ALIAS_SCHEMA
    );

    const expectedVertices = [
      {
        expectedName: 'myView',
        expectedAttributes: {
          'p1_id': 'STRING',
          'm1_title': 'STRING',
          'm1_genres': 'STRING',
          'e1_rating': 'DOUBLE',
          'e1_rated_at': 'DATETIME'
        }
      },
    ];
    checkVerticesAreExpected(mockAliasSchema.vertexTypes, expectedVertices);

    const expectedEdges = [];
    checkEdgesAreExpected(mockAliasSchema.edgeTypes, expectedEdges);
  });
});
