import { Atom<PERSON><PERSON>, <PERSON>ion<PERSON><PERSON>, FunctionMetaInfo, FunctionType } from '../expression';
import {
  Graph, GSQLAttribute<PERSON>son, GSQLEdgeJson, GSQLGraphJson,
  GSQLVertexJson, supportedTypesInExpression
} from '../topology';
import {
  <PERSON><PERSON><PERSON><PERSON>, PatternE<PERSON>, PatternJson,
  PatternEdgeSetJ<PERSON>, PatternVertexSetJson, PatternViewJson
} from '../visual-query-pattern';
import { DataType } from '../data';

export const aliasSchemaAffix = 'alias_schema';

export enum AliasSchemaType {
  INITIAL_ALIAS_SCHEMA,
  ORDER_BY_ALIAS_SCHEMA,
  PATTERN_VIEW_ALIAS_SCHEMA
}

/**
 * Build alias schema based on the pattern.
 *
 * @export
 * @param {Graph} schema
 * @param {PatternJson} patternJson
 * @param {AliasSchemaType} aliasSchemaType
 * @returns {Graph}
 */
export function buildAliasSchema(
  schema: Graph,
  patternJson: Pat<PERSON><PERSON><PERSON>,
  aliasSchemaType: AliasSchemaType
): Graph {
  if (!schema || !patternJson) {
    return undefined;
  }

  const vertexJsons: GSQLVertexJson[] = [];
  const edgeJsons: GSQLEdgeJson[] = [];

  switch (aliasSchemaType) {
    case AliasSchemaType.INITIAL_ALIAS_SCHEMA:
    case AliasSchemaType.ORDER_BY_ALIAS_SCHEMA:
      // <key = vertex set id, value = vertex set alias>
      const id2Alias = new Map<string, string>();

      // Collect vertext sets.
      patternJson.patternVertexSets.forEach(vertexSet => {
        const alias = getPatternComponentAlias(vertexSet);
        id2Alias.set(vertexSet.id, alias);

        const attrJsons = getAttributeJsons(schema, vertexSet);
        if (aliasSchemaType === AliasSchemaType.ORDER_BY_ALIAS_SCHEMA) {
          addAttachmentsToAttributes(vertexSet, attrJsons);
        }

        vertexJsons.push(
          constructGSQLVertexJson(
            alias,
            attrJsons,
            vertexSet
          )
        );
      });

      // Collect edge sets.
      patternJson.patternEdgeSets.forEach(edgeSet => {
        edgeJsons.push(
          constructGSQLEdgeJson(
            getPatternComponentAlias(edgeSet),
            id2Alias.get(edgeSet.sourcePatternId),
            id2Alias.get(edgeSet.targetPatternId),
            getAttributeJsons(schema, edgeSet),
            edgeSet
          )
        );
      });

      break;
    case AliasSchemaType.PATTERN_VIEW_ALIAS_SCHEMA:
      patternJson.patternViews.forEach(patternView => {
        const attrJsons: GSQLAttributeJson[] = [];

        // Collect vertex sets that have an alias within the pattern view.
        patternView.vertexSetIds.forEach(vertexSetId => {
          const vertexSet = <PatternVertexSetJson>getPatternEntityJsonById(vertexSetId, patternJson);
          if (!vertexSet.alias) {
            return;
          }

          const attrJsonsInVertexSet = getAttributeJsons(schema, vertexSet);
          addAttachmentsToAttributes(vertexSet, attrJsonsInVertexSet);
          // The attribute name should start with the vertex set alias
          // to avoid duplicate attribute names within a pattern view.
          attrJsonsInVertexSet.forEach(attrJson => {
            attrJson.AttributeName = `${vertexSet.alias}_${attrJson.AttributeName}`;
          });

          attrJsons.push(...attrJsonsInVertexSet);
        });

        // Collect edge sets that have an alias within the pattern view.
        patternView.edgeSetIds.forEach(edgeSetId => {
          const edgeSet = <PatternEdgeSetJson>getPatternEntityJsonById(edgeSetId, patternJson);
          if (!edgeSet.alias) {
            return;
          }

          const attrJsonsInEdgeSet = getAttributeJsons(schema, edgeSet);
          // The attribute name should start with the edge set alias
          // to avoid duplicate attribute names within a pattern view.
          attrJsonsInEdgeSet.forEach(attrJson => {
            attrJson.AttributeName = `${edgeSet.alias}_${attrJson.AttributeName}`;
          });

          attrJsons.push(...attrJsonsInEdgeSet);
        });

        // Each pattern view can be treated as a vertex data.
        vertexJsons.push(
          constructGSQLVertexJson(
            patternView.alias,
            attrJsons,
            patternView
          )
        );
      });

      break;
  }

  return constructGraph(
    `${patternJson.name}_${aliasSchemaAffix}`,
    vertexJsons,
    edgeJsons
  );
}

/**
 * Construct a GSQL vertex json.
 *
 * PrimaryId is not used.
 *
 * @param {string} name
 * @param {GSQLAttributeJson[]} attributes
 * @param {(PatternVertexSetJson | PatternViewJson)} entity
 * @returns {GSQLVertexJson}
 */
function constructGSQLVertexJson(
  name: string,
  attributes: GSQLAttributeJson[],
  entity: PatternVertexSetJson | PatternViewJson,
): GSQLVertexJson {
  return {
    Name: name,
    PrimaryId: {
      AttributeName: 'pid',
      AttributeType: { Name: 'INT' }
    },
    Attributes: attributes,
    Config: {
      existentialCheck: entity.existentialCheck,
      patternId: entity.id,
    }
  };
}

/**
 * Construct a GSQL edge json.
 *
 * IsDirected is not used.
 *
 * @param {string} name
 * @param {string} source
 * @param {string} target
 * @param {GSQLAttributeJson[]} attributes
 * @param {PatternEdgeSetJson} edge
 * @returns {GSQLEdgeJson}
 */
function constructGSQLEdgeJson(
  name: string,
  source: string,
  target: string,
  attributes: GSQLAttributeJson[],
  edge: PatternEdgeSetJson,
): GSQLEdgeJson {
  return {
    Name: name,
    ToVertexTypeName: source,
    FromVertexTypeName: target,
    IsDirected: false,
    Attributes: attributes,
    Config: {
      existentialCheck: edge.existentialCheck,
      patternId: edge.id,
    }
  };
}

/**
 * Construct a Graph.
 *
 * @param {string} graphName
 * @param {GSQLVertexJson[]} vertexTypes
 * @param {GSQLEdgeJson[]} edgeTypes
 * @returns {Graph}
 */
function constructGraph(
  graphName: string,
  vertexTypes: GSQLVertexJson[],
  edgeTypes: GSQLEdgeJson[]
): Graph {
  const graphJson: GSQLGraphJson = {
    GraphName: graphName,
    VertexTypes: vertexTypes,
    EdgeTypes: edgeTypes
  };
  return new Graph().loadFromGSQLJson(graphJson);
}

/**
 * Get a unique alias for given pattern entity or entity json.
 *
 * @export
 * @param {(PatternEntity | PatternEntityJson)} entity
 * @returns {string}
 */
export function getPatternComponentAlias(
  entity: PatternEntity | PatternEntityJson
): string {
  return entity.alias || `${aliasSchemaAffix}_${entity.id}`;
}

/**
 * Get attribute jsons from the vertex pattern or edge pattern.
 * Only retrieve attributes for single type vertex pattern or edge pattern.
 *
 * @param {Graph} schema
 * @param {(PatternVertexSetJson | PatternEdgeSetJson)} entity
 * @returns {GSQLAttributeJson[]}
 */
function getAttributeJsons(
  schema: Graph,
  entityJson: PatternVertexSetJson | PatternEdgeSetJson
): GSQLAttributeJson[] {
  let attrJsons: GSQLAttributeJson[] = [];

  if ('vertices' in entityJson) {
    if (entityJson.vertices.length === 1) {
      const vertex = schema.getVertex(entityJson.vertices[0]);
      if (vertex) {
        const vertexJson = vertex.dumpToGSQLJson();
        attrJsons = vertexJson.Attributes;
        if (vertex.config && vertex.config.PRIMARY_ID_AS_ATTRIBUTE) {
          attrJsons.push(vertexJson.PrimaryId);
        }
      }
    }
  } else if ('edges' in entityJson) {
    if (entityJson.edges.length === 1) {
      const edge = schema.getEdge(entityJson.edges[0]);
      if (edge) {
        attrJsons = edge.dumpToGSQLJson().Attributes;
      }
    }
  }

  return attrJsons.filter(json => supportedTypesInExpression.includes(json.AttributeType.Name));
}

/**
 * Add the attachments of a vertex set to its attributes.
 *
 * @param {PatternVertexSetJson} vertexSet
 * @param {GSQLAttributeJson[]} attributes
 * @return {string}
 */
function addAttachmentsToAttributes(
  vertexSet: PatternVertexSetJson,
  attributes: GSQLAttributeJson[]
) {
  vertexSet.aggregations.forEach(agg => {
    const aggExprValueType = getAggregationExpressionValueTypeForOrderBy(agg.expression);
    if (aggExprValueType !== 'UNSUPPORTED') {
      attributes.push({
        AttributeName: agg.name,
        AttributeType: {
          Name: aggExprValueType
        }
      });
    }
  });
}

/**
 * Recursively get the value type of the given aggregation,
 * and filter it out if it's unsupported in order-by.
 *
 * @param {(AtomJson | ExpressionJson)} aggregationExpression
 * @return {string}
 */
function getAggregationExpressionValueTypeForOrderBy(aggExpressionJson: AtomJson | ExpressionJson): string {
  // TODO:  Support Datetime in the future with GSQL support.
  if (['IntConstant', 'IntParam'].includes(aggExpressionJson.type)) {
    return 'INT';
  } else if (['RealConstant', 'RealParam'].includes(aggExpressionJson.type)) {
    return 'DOUBLE';
  } else if (['StringConstant', 'StringParam'].includes(aggExpressionJson.type)) {
    return 'STRING';
  } else if (['AttrVariableWithAlias', 'AttrVariable'].includes(aggExpressionJson.type)) {
    return getDataTypeForOrderBy((<AtomJson>aggExpressionJson).valueType);
  } else if (['BoolConstant', 'BoolParam', 'DatetimeConstant', 'DatetimeParam', 'AliasReference']
    .includes(aggExpressionJson.type)) {
    return 'UNSUPPORTED';
  }

  if (aggExpressionJson.type === 'FunctionExpression') {
    const functionExpressionReturnType = FunctionMetaInfo.get(<FunctionType>(<ExpressionJson>aggExpressionJson).operator).returnType;
    return getDataTypeForOrderBy(functionExpressionReturnType);
  }

  const expressionValueType = (<ExpressionJson>aggExpressionJson).operands
    .map(operand => getAggregationExpressionValueTypeForOrderBy(operand))
    .reduce((a, b) => {
      // Semantic check has already ensured that operands in one expression can only be all numbers or all strings.
      if (a === 'DOUBLE' || b === 'DOUBLE') {
        return 'DOUBLE';
      } else if (a === 'STRING' || b === 'STRING') {
        return 'STRING';
      } else if (a === 'INT' && b === 'INT') {
        return 'INT';
      }
    }, 'INT');

  return expressionValueType;
}


/**
 * Given a data type return the corresponding string value if it is supported in orderby.
 * If it is not supported return 'UNSUPPORTED'.
 *
 * @param {DataType} dataType
 * @return {string}
 */
function getDataTypeForOrderBy(dataType: DataType): string {
  switch (dataType) {
    case DataType.Int:
      return 'INT';
    case DataType.Real:
      return 'DOUBLE';
    case DataType.String:
      return 'STRING';
    default:
      return 'UNSUPPORTED';
  }
}

/**
 * Find pattern entity by id.
 *
 * @param {string} id
 * @param {PatternJson} patternJson
 * @returns {PatternEntityJson}
 */
function getPatternEntityJsonById(
  id: string,
  patternJson: PatternJson
): PatternEntityJson {
  const entities: PatternEntityJson[] = [];
  entities.push(...patternJson.patternVertexSets);
  entities.push(...patternJson.patternEdgeSets);
  return entities.find(entity => entity.id === id);
}
