import { AxiosResponse } from 'axios';

function getErrorMessage(error: AxiosResponse | string | undefined): string {
  let message = '';
  if (typeof error === 'string') {
    message = error;
  } else if (error?.data?.message) {
    message = error.data?.message as string;
  }
  return message;
}

export function isSessionRevoked(data: AxiosResponse | string | undefined): boolean {
  return getErrorMessage(data).toLowerCase().includes('session is revoked');
}

export function isPasswordExpired(data: AxiosResponse | string | undefined): boolean {
  return getErrorMessage(data).toLowerCase().includes('password has expired');
}

export function handlePasswordExpired() {
  const redirectionURL = `/#/changepassword?returnURL=${encodeURIComponent(
    window.location.pathname + window.location.search + window.location.hash
  )}`;  

  showErrorDialog("You password has expired", "Please change your password to continue.", redirectionURL);
}

export function showSessionEndDialog() {
  const redirectionURL = `/#/login?returnURL=${encodeURIComponent(
    window.location.pathname + window.location.search + window.location.hash
  )}&loggedOut=true`;

  showErrorDialog('Your session has ended', 'Your session has been terminated due to a login from another device. For security reasons, we can only allow one active session per user at a time.', redirectionURL);
}

export function showErrorDialog(title: string, content: string, redirectionURL: string) {
  if (document.querySelector('.dialog-overlay')) {
    return;
  }
  const overlay = document.createElement('div');
  overlay.classList.add('dialog-overlay');
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.32)';
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.display = 'flex';
  overlay.style.alignItems = 'center';
  overlay.style.justifyContent = 'center';
  overlay.style.zIndex = '10000';

  const dialog = document.createElement('div');
  dialog.classList.add('dialog');
  dialog.style.backgroundColor = '#fff';
  dialog.style.padding = '20px';
  dialog.style.maxWidth = '400px';
  dialog.style.textAlign = 'center';
  dialog.style.boxShadow = '0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)';
  dialog.style.borderRadius = '4px';
  dialog.style.color = 'rgba(0, 0, 0, 0.87)';

  const titleElem = document.createElement('h2');
  titleElem.textContent = title;
  titleElem.style.marginTop = '0';
  titleElem.style.font = '500 20px/32px Roboto, "Helvetica Neue", sans-serif';

  const contentElem = document.createElement('p');
  contentElem.textContent = content;
  contentElem.style.marginBottom = '20px';
  contentElem.style.font = '400 14px/20px Roboto, "Helvetica Neue", sans-serif';

  const confirmButton = document.createElement('button');
  confirmButton.textContent = 'CONFIRM';
  confirmButton.style.padding = '10px 20px';
  confirmButton.style.background = 'transparent';
  confirmButton.style.color = '#d50000';
  confirmButton.style.border = 'none';
  confirmButton.style.cursor = 'pointer';
  confirmButton.style.font = '500 14px Roboto, "Helvetica Neue", sans-serif';
  confirmButton.style.borderRadius = '4px';

  dialog.appendChild(titleElem);
  dialog.appendChild(contentElem);
  dialog.appendChild(confirmButton);
  overlay.appendChild(dialog);
  document.body.appendChild(overlay);
  document.body.style.overflow = 'hidden';

  confirmButton.addEventListener('click', onClick);

  function onClick() {
    window.location.href = redirectionURL;
  }
}