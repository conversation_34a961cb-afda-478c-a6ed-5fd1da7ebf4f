/**
 * A response that represents an error or failure, either from a
 * non-successful Http Socket status, or an error while executing the request.
 *
 * Any error returned on the `Observable` response stream will be
 * wrapped in an `HttpSourceErrorResponse` to provide additional context about
 * the state of the Http long request layer when the error occurred.
 *
 * @export
 * @class HttpSourceErrorResponse
 * @implements {Error}
 */
export class HttpSourceErrorResponse implements Error {
  readonly error: any;
  readonly status: number;
  readonly message: string;
  readonly statusText: string;
  readonly name = 'HttpSourceErrorResponse';

  constructor(init: { error?: any; status?: number }) {
    this.error = init.error;
    this.statusText = 'Error';
    this.status = init.status || 0;
    this.message = `HttpSource failure response with ${this.status}`;
  }
}
