export const paramsSerializer = (params?: Record<string, string | string[]>) => {
  return Object.keys(params).reduce((res, key) => {
    const paramsValue = params[key];
    let paramValueString: string;
    if (Array.isArray(paramsValue)) {
      paramValueString = paramsValue.map((val) => `${key}=${encodeURIComponent(val)}`).join('&');
    } else if (typeof paramsValue === 'string') {
      paramValueString = `${key}=${encodeURIComponent(paramsValue)}`;
    }
    return [res, paramValueString].filter(Boolean).join('&');
  }, '');
};
