import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

type DataSource = {
  name: string;
  isLocal: boolean;
  type: string;
};

export type GetAllDataSourcesReturnType = Result<{
  datasources: DataSource[];
}>;

export type GetAllDataSourcesParam = {
  graph: string;
};

export const getAllDataSources = async (
  params: GetAllDataSourcesParam,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<GetAllDataSourcesParam>(
      `/api/gsql-server/gsql/v1/data-sources?graph=${params.graph}`,
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<GetAllDataSourcesParam>(
      `/api/gsql-server/gsql/data-sources?graph=${params.graph}`,
      config
    );

    return res;
  }
};
