import { AxiosRequestConfig } from 'axios';
import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteOneDataSourceParam = {
  graph: string;
  name: string;
};

export const deleteOneDataSource = async (
  params: DeleteOneDataSourceParam,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/data-sources?graph=${params.graph}&name=${params.name}`,
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/data-sources/${params.name}?graph=${params.graph}`,
      config
    );

    return res;
  }
};
