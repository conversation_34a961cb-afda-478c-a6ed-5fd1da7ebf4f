import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteSecretParams = {
  graph?: string;
  alias: string;
};

export const deleteSecret = async (params?: DeleteSecretParams, config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().delete<Result>(
      `/api/gsql-server/gsql/v1/secrets`,
      {
        data: {
          secrets: [params.alias]
        },
        ...config
      },
    );
    return res;
  } else {
    const res = await getAxiosInstance().delete<Result>(
      '/api/gsql-server/gsql/secrets',
      {
        params,
        ...config
      }
    );

    return res;
  }
};
