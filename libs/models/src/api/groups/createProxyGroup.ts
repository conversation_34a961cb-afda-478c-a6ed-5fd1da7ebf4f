import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type CreateProxyGroupParams = {
  name: string;
  rule: string;
};

export const createProxyGroup = async (
  params: CreateProxyGroupParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/scim/v2/Groups',
      {
        groupName: params.name,
        proxyRule: params.rule,
      },
      {
        params: {
          gsqlFormat: true,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/proxy-groups',
      params,
      config
    );
    return res;
  }
};
