import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type ChangeProxyGroupParams = {
  name: string;
  rule: string;
};

export const changeProxyRule = async (params: ChangeProxyGroupParams, config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().patch<Result>(
      `/api/gsql-server/gsql/scim/v2/Groups/${params.name}`,
      params,
      {
        params: {
          gsqlFormat: true,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().put<Result>(
      '/api/gsql-server/gsql/proxy-groups',
      params,
      config,
    );
    return res;
  }
};
