import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteProxyGroup = {
  name: string;
};

export const deleteProxyGroup = async (params: DeleteProxyGroup, config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/scim/v2/Groups`,
      {
        groupNames: [params.name],
      },
      {
        params: {
          gsqlFormat: true,
          action: 'delete',
        },
        ...config,
      },
    );
    return res;
  } else {
    const res = await getAxiosInstance().delete<Result>(
      '/api/gsql-server/gsql/proxy-groups',
      {
        params,
        ...config,
      }
    );
    return res;
  }
};
