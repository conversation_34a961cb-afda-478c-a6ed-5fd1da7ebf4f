import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetProxyGroupsParams = {
  graph?: string;
};

export const getProxyGroups = async (
  params: GetProxyGroupsParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    return getAxiosInstance().get('/api/gsql-server/gsql/scim/v2/Groups', {
      params: {
        ...params,
        gsqlFormat: true,
      },
      ...config,
    });
  } else {
    return getAxiosInstance().get('/api/gsql-server/gsql/proxy-groups', {
      params,
      ...config,
    });
  }
};
