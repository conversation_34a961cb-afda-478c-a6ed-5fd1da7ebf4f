import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export type FilePolicyParam = {
  path: string;
};

export const checkFilePolicy = async (
  params: FilePolicyParam,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/internal/check/file-policy',
      {
        params,
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/filepolicy',
      {
        params,
        ...config,
      }
    );

    return res;
  }
};
