import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export const backup = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/back-up',
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/export',
      config
    );

    return res;
  }
};
