import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const isSchemaReady = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/internal/check/schema-ready',
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/isschemaready',
      config
    );

    return res;
  }
};
