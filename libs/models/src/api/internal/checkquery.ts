import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export type CheckQueryParam = {
  graph: string;
  code: string;
};

export const checkQuery = async (
  params: CheckQueryParam,
  config?: CommonRequestConfig
) => {
  const { code, graph } = params;
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/internal/check/query',
      {
        code,
      },
      {
        params: {
          graph,
        },
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/codecheck',
      {
        code,
        graph,
      },
      config
    );

    return res;
  }
};
