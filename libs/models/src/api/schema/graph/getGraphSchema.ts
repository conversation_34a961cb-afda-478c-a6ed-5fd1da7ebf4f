import { CommonRequestConfig } from '../../common';
import { getAxiosInstance } from '../../request';
import { getVersionIsGTE410 } from '../../version';

export type GetGraphSchemaParams = {
  graph?: string;
};

export const getGraphSchema = async (
  params?: GetGraphSchemaParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const graph = params?.graph || 'global';
    const res = await getAxiosInstance().get(
      `/api/gsql-server/gsql/v1/schema/graphs/${graph}`,
      config
    );
    return res;
  } else {
    const res = await getAxiosInstance().get('/api/gsql-server/gsql/schema', {
      params,
      ...config,
    });
    return res;
  }
};
