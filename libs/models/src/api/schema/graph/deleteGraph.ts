import { CommonRequestConfig } from '../../common';
import { getAxiosInstance } from '../../request';
import { getVersionIsGTE410 } from '../../version';

export type DeleteGraphParams = {
  graph: string;
};

export const deleteGraph = async (
  params: DeleteGraphParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().delete(
      `/api/gsql-server/gsql/v1/schema/graphs/${params.graph}`,
      {
        params: {
          cascade: 'true'
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().delete(
      `/api/gsql-server/gsql/schema/graphs/${params.graph}`,
      {
        params,
        ...config,
      }
    );
    return res;
  }
};
