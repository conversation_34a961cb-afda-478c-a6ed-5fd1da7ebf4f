import { CommonRequestConfig } from '../../common';
import { getAxiosInstance } from '../../request';
import { getVersionIsGTE410 } from '../../version';

export type CreateGraphParams = {
  GraphName: string;
  VertexTypes: string[];
  EdgeTypes: string[];
};

export const createGraph = async (params: CreateGraphParams, config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().post('/api/gsql-server/gsql/v1/schema/graphs', {
      VertexTypes: params.VertexTypes,
      EdgeTypes: params.EdgeTypes,
    }, {
      params: {
        graphName: params.GraphName,
      },
      ...config
    });
    return res;
  } else {
    const res = getAxiosInstance().post('/api/gsql-server/gsql/schema', params, config);
    return res;
  }
};
