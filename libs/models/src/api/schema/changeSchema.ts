import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type ChangeSchemaParams = {
  graph: string;
  schemaChange: any;
  reinstall?: boolean;
};

export const changeSchema = async (
  params: ChangeSchemaParams,
  config?: CommonRequestConfig
) => {
  const schemaChange = params.schemaChange;
  delete params.schemaChange;
  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/schema/change',
      schemaChange,
      {
        params,
        ...config,
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().put(
      '/api/gsql-server/gsql/schema/change',
      schemaChange,
      {
        params,
        ...config,
      }
    );
    return res;
  }
};
