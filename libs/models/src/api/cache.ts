export class Cache {
  private storage: Storage;
  constructor() {
    this.storage = window.localStorage;
  }

  set(key: string, value: unknown, ttl = 300) {
    const now = new Date();
    const item = {
      value: value,
      expiry: now.getTime() + ttl * 1000,
    };
    this.storage.setItem(key, JSON.stringify(item));
  }

  get(key: string) {
    const itemStr = this.storage.getItem(key);

    if (!itemStr) {
      return null;
    }

    const item = JSON.parse(itemStr);
    const now = new Date();

    if (now.getTime() > item.expiry) {
      this.storage.removeItem(key);
      return null;
    }

    return item.value;
  }
}
