import { AxiosRequestConfig, AxiosInstance, isAxiosError } from 'axios';
import axios from './axios.min.js';
import { handlePasswordExpired, isPasswordExpired, isSessionRevoked, showSessionEndDialog } from '../error/http-error';

const config: AxiosRequestConfig = {
  // TODO will change here config
};

let axiosInstance: AxiosInstance = axios.create(config);

// 1. tools on premise and cloud version both need credential (cloud also use cookie for authentication)
// 2. for tools cloud version, need to setup baseURL (get baseURL from sessionStorage)
//    for on premise version, there is no need to set baseURL
axiosInstance.defaults.withCredentials = true;
if (sessionStorage.getItem('BASEURL')) {
  axiosInstance.defaults.baseURL = sessionStorage.getItem('BASEURL');
}

export const getAxiosInstance = () => {
  return axiosInstance;
};

export const getBaseURL = () => {
  return getAxiosInstance().defaults.baseURL || '';
}

// 1. insights will call this to set up baseURL and tigergraphToken (if any)
// 2. cloud portal will call this to setup baseURL and idToken
export function setupAxiosInstance(
  axios: AxiosInstance,
  baseURL: string,
  token: {
    idToken?: string;
    // TigerGraphToken is used to for embed insights widget to third party page
    tigergraphToken?: string;
  } = {}
) {
  const {
    idToken,
    tigergraphToken,
  } = token
  axiosInstance = axios;
  axiosInstance.defaults.baseURL = baseURL;
  axiosInstance.defaults.withCredentials = true;
  if (tigergraphToken) {
    axiosInstance.defaults.headers.common['Authorization'] = `Token ${tigergraphToken}`;
  } else if (idToken) {
    axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${idToken}`;
    // for idToken, we should not to send cookie (backend will check cookie header first, so we must not send cookie in this case)
    axiosInstance.defaults.withCredentials = false;
  }
}

axiosInstance.interceptors.response.use((response) => {
  if (response.data?.error) {
    throw new Error(response.data.message || 'Some errors have occurred.');
  }

  return response;
}, (err) => {
  if (err?.response?.status === 401) {
    if (isPasswordExpired(err.response)) {
      handlePasswordExpired();
    } else if (isSessionRevoked(err.response)) {
      showSessionEndDialog();
    } else {
      navigateToLogin();
    }
  }

  if (isAxiosError(err)) {
    const message = err.response.data?.message || err.message;
    (err as any).error = message;
    err.message = message;
  }
  return Promise.reject(err);
});

export const navigateToLogin = () => {
  if (!window.location.hash.includes('login')) {
    const pathname = window.location.pathname + (window.location.pathname.endsWith('/') ? '' : '/');
    window.location.href = `${window.location.origin}/#/login?returnURL=${encodeURIComponent(
      pathname + window.location.search + window.location.hash + getReturnURLParamStr()
    )}`;
  }
};

export const getReturnURLParamStr = () => {
  // check if on cloud env
  if (!sessionStorage.getItem('BASEURL')) {
    return '';
  }

  const params = new URLSearchParams();

  const baseUrl = sessionStorage.getItem('BASEURL');
  const domain = baseUrl?.replace(/https:\/\/|\.tgcloud-dev\.com|\.tgcloud\.io/g, '') || '';
  params.set('domain', domain);

  params.set('orgName', sessionStorage.getItem('ORGNAME') || '');
  params.set('clusterid', sessionStorage.getItem('CURRENTCLUSTERID') || '');

  // only cloud 4 has cloudEnv and cloudVersion
  if (sessionStorage.getItem('CLOUDENV')) {
    params.set('cloudEnv', sessionStorage.getItem('CLOUDENV'));
  }
  if (sessionStorage.getItem('CLOUDVERSION')) {
    params.set('cloudVersion', sessionStorage.getItem('CLOUDVERSION'));
  }

  return `${window.location.hash ? '' : '#/'}?${params.toString()}`;
};