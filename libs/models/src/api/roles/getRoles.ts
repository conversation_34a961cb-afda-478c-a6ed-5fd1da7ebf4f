import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

type GetRolesParams = {
  graph?: string;
};

export const getRoles = async (
  params?: GetRolesParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/roles`,
      {
        params: {
          graph: params?.graph,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/roles',
      {
        params: {
          graph: params?.graph,
        },
        ...config,
      }
    );

    return res;
  }
};
