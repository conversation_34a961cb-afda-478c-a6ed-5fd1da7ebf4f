import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GrantRolesParams = {
  graph?: string;
  roles: string[];
  usernames: string[];
};

export const grantRoles = async (
  params: GrantRolesParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/roles/grant',
      {
        roles: params.roles,
        users: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/user',
      {
        roles: params.roles,
        usernames: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
        ...config,
      }
    );
    return res;
  }
};
