import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type GetRoleParams = {
  role: string;
};

export const getRole = async (
  params?: GetRoleParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/role/${params.role}`,
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/role',
      {
        params,
        ...config,
      }
    );

    return res;
  }
};
