import { CommonRequestConfig, paramsSerializer } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RevokeRolesParams = {
  graph?: string;
  roles: string[];
  usernames: string[];
};

export const revokeRoles = async (
  params: RevokeRolesParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/roles/revoke',
      {
        roles: params.roles,
        users: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().delete('/api/gsql-server/gsql/user', {
      params: {
        graph: params.graph,
        role: params.roles,
        username: params.usernames,
      },
      paramsSerializer,
      ...config,
    });
    return res;
  }
};
