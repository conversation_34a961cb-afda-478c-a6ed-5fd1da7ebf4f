import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type GetRolesByRoleNameListParams = {
  roles: string[];
};

export const getRolesByRoleNameList = async (
  params?: GetRolesByRoleNameListParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/privileges`,
      {
        params: {
          role: params.roles.join(','),
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/role',
      {
        params: {
          role: params.roles,
        },
        paramsSerializer: (params?: { role?: string[] }) => {
          return params?.role?.map((role) => `role=${role}`).join('&') || '';
        },
        ...config,
      }
    );

    return res;
  }
};
