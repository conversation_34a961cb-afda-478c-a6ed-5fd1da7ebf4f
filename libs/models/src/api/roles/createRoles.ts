import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type CreateRoleParams = {
  graph?: string;
  roleName: string;
};

export const createRoles = async (
  params?: CreateRoleParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/roles`,
      {
        roles: [params.roleName],
      },
      {
        params: {
          graph: params.graph,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/roles',
      {
        roles: [params.roleName],
      },
      {
        params: {
          graph: params.graph,
        },
        ...config,
      }
    );

    return res;
  }
};
