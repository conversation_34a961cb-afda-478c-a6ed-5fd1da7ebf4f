import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export const getSAMLRequest = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/saml/authnrequest',
      config,
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/saml/authnrequest',
      config,
    );

    return res;
  }
};