import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const getSAMLMeta = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/saml/meta',
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/saml/meta',
      config
    );

    return res;
  }
};