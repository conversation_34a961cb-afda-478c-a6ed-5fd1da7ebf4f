import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export type RDBMSDumpParam = {
  type: string;
  server: string;
  port: number | string;
  database: string;
  username: string;
  password: string;
  tableNames: string[];
  limit?: number;
};

export const getRDBMSDump = async (
  params: RDBMSDumpParam,
  config?: CommonRequestConfig
) => {
  const { username, password, tableNames, ...queryParams } = params;
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/rdbms/dump',
      {
        username,
        password,
        tableNames,
      },
      {
        params: queryParams,
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/rdbms/dump',
      {
        username,
        password,
        tableNames,
      },
      {
        params: queryParams,
        ...config,
      }
    );


    return res;
  }
};
