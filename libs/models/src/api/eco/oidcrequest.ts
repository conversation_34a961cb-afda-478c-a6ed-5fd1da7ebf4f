import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export const getOIDCRequest = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/oidc/authnrequest',
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/oidc/authnrequest',
      config
    );

    return res;
  }
};
