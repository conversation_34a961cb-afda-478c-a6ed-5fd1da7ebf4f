import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';
import { CommonRequestConfig } from '../common';

export type RDBMSMetaParam = {
  type: string;
  server: string;
  port: number | string;
  database: string;
  username: string;
  password: string;
};

export const getRDBMSMeta = async (
  { username, password, ...params }: RDBMSMetaParam,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/rdbms/meta',
      {
        username,
        password,
      },
      {
        params,
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/rdbms/meta',
      {
        username,
        password,
      },
      {
        params,
        ...config
      }
    );

    return res;
  }
};
