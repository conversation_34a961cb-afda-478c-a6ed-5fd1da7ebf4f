import { CommonRequestConfig, paramsSerializer } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RevokePrivilegesParams = {
  privileges: any;
  vertexName?: string;
  edgeName?: string;
  attrName?: string;
  roleName: string;
  graph?: string;
  queryName?: string;
};

export const revokePrivileges = async (
  {
    graph,
    roleName,
    privileges,
    vertexName,
    edgeName,
    attrName,
    queryName,
  }: RevokePrivilegesParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/privileges/revoke',
      {
        vertexName: vertexName ? [vertexName] : undefined,
        edgeName: edgeName ? [edgeName] : undefined,
        attrNames: attrName ? [attrName] : undefined,
        queryName: queryName ? [queryName] : undefined,
        privileges,
        roles: [roleName],
      },
      {
        params: {
          graph,
        },
        paramsSerializer,
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().delete('/api/gsql-server/gsql/role', {
      params: {
        vertexName,
        edgeName,
        attrNames: attrName,
        privilege: privileges,
        role: roleName,
        graph: graph,
      },
      paramsSerializer,
      ...config,
    });
    return res;
  }
};
