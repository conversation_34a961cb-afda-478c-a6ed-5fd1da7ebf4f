import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type PostUserDefinedFunctionsParams = {
  filename: string;
  filepath: string;
  errormessage?: string;
  content?: string;
};

export const uploadUserDefinedFunctions = async (
  params: PostUserDefinedFunctionsParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/udt/filels?${params.filename}&filepath=${params.filepath}`,
      {
        params: {
          content: params.content,
        },
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/userdefinedfunction?${params.filename}&filepath=${params.filepath}`,
      {
        params: {
          content: params.content,
        },
        ...config,
      }
    );

    return res;
  }
};
