import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type GetUserDefinedFunctionsParams = {
  filename: string;
  filepath: string;
  errormessage?: string;
};

export const getUserDefinedFunctions = async (
  params: GetUserDefinedFunctionsParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/udt/filels?filename=${params.filename}&filepath=${params.filepath}`,
      config
    );

    return res.data.results;
  } else {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/userdefinedfunction?filename=${params.filename}&filepath=${params.filepath}`,
      config
    );

    return res.data.results;
  }
};
