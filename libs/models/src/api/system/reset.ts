import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const reset = async (config?: CommonRequestConfig) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/reset',
      config
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/reset',
      config
    );

    return res;
  }
};
