import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type CommandParam = {
  async: boolean;
  timeout: number;
  text: string;
};

export const statements = async (params: CommandParam, config?: CommonRequestConfig) => {
  const newUrl = `/api/gsql-server/gsql/v1/statements?${
    params.async ? `async=${params.async}` : 'async=false'
  }${params.timeout ? `timeout=${params.timeout}` : 'timeout=0'}`;
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<string>(`${newUrl}`, {
      params: {
        text: params.text,
      },
      ...config
    });

    return res;
  } else {
    const res = await getAxiosInstance().post<string>(
      '/api/gsql-server/gsql/command',
      {
        params: {
          text: params.text,
        },
        ...config
      }
    );

    return res;
  }
};
