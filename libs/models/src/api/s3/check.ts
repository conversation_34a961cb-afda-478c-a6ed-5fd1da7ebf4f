import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type S3PayloadParam = {
  type: string;
  config: any;
};

export const checkS3 = async (
  params: S3PayloadParam,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/ds-credentials',
      {
        params: {
          type: params?.type,
          config: params?.config,
        },
        ...config,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/check/ds-credentials',
      {
        params: {
          type: params?.type,
          config: params?.config,
        },
        ...config,
      }
    );

    return res;
  }
};
