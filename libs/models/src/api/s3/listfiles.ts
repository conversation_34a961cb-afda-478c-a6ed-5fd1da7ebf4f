import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type ListFilesParam = {
  datasource: string;
  path?: string;
};

export const getListFiles = async (params: ListFilesParam, config?: CommonRequestConfig) => {
  const baseOldUrl = `/api/gsql-server/gsql/listfiles?datasource=${
    params.datasource
  }${params.path ? `&path=${params.path}` : ''}`;
  const baseNewUrl = `/api/gsql-server/gsql/v1/list-files/${params.datasource}${
    params.path ? `?path="${params.path}"` : ''
  }`;

  if (getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().get<Result>(baseNewUrl, config);

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(baseOldUrl, config);

    return res;
  }
};
