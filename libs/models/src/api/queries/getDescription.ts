import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetDescriptionParams = {
  graph: string;
};

export const getDescription = async (
  params: GetDescriptionParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().get(
      '/api/gsql-server/gsql/v1/queries/description',
      {
        params: {
          query: 'all',
          ...params,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/description', {
      params: {
        query: 'all',
        ...params,
      },
      ...config,
    });
    return res;
  }
};
