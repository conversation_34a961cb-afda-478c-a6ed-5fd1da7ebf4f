import { parseJSO<PERSON> } from '../../utils';
import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RunInterpretParams = {
  queryBody: string;
  params: any;
  headers?: any;
};

export const runInterpret = async (
  { queryBody, params, headers }: RunInterpretParams,
  config?: CommonRequestConfig
) => {
  const transformResponse = [(data: string) => {
    if (typeof data !== 'string') return data;
    return parseJSON(data);
  }];

  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/queries/interpret',
      queryBody,
      {
        params,
        headers: {
          ...headers,
          'Content-Type': 'text/plain',
        },
        transformResponse,
        ...config,
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().post(
      '/api/gsql-server/interpreted_query',
      queryBody,
      {
        params,
        headers: {
          ...headers,
          'Content-Type': 'text/plain',
        },
        transformResponse,
        ...config,
      }
    );
    return res;
  }
};
