import { getAxiosInstance } from './request';
import { compare } from 'compare-versions';
import { CommonRequestConfig } from './common';

export type IVersionResponse = {
  error: boolean;
  message: string;
  results: {
    tigergraph_version: string;
  };
};

const host2versionMap = new Map<string, Promise<string>>();

export const getTGVersion = async (host = getAxiosInstance().defaults.baseURL || '') => {
  let versionPromise = host2versionMap.get(host || 'DEFAULT');
  // if version cannot get, will try to request api version
  if (versionPromise instanceof Promise) {
    // using cached version api
  } else {
    versionPromise = getAxiosInstance()
      .get<IVersionResponse>(`${host}/api/version`)
      .then((r) => r.data.results.tigergraph_version);
    host2versionMap.set(host, versionPromise);
  }

  return versionPromise;
};

export const getVersionIsGTE410 = async (config: CommonRequestConfig = {}) => {
  let { baseURL, version } = config;
  if (!version) {
    version = await getTGVersion(baseURL);
  }

  return compare(version, '4.0.x', '>');
};
