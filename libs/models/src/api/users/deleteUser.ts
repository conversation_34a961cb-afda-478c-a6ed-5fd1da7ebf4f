import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type DeleteUserParams = {
  user: string;
};

export const deleteUser = async (
  params: DeleteUserParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const res = getAxiosInstance().delete(
      `/api/gsql-server/gsql/scim/v2/Users/<USER>
      {
        params: {
          gsqlFormat: true,
        },
        ...config,
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().delete('/api/gsql-server/gsql/users', {
      params: {
        name: params.user,
      },
      ...config,
    });
    return res;
  }
};
