import { getVersionIsGTE410 } from '../version';
import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { CommonRequestConfig } from '../common';

export type CreateUserParams = {
  name: string;
  password: string;
};

export const createUser = async (params: CreateUserParams, config?: CommonRequestConfig) => {
    if (await getVersionIsGTE410(config)) {
    const res = await getAxiosInstance().post<Result>('/api/gsql-server/gsql/scim/v2/Users', {
      username: params.name,
      password: params.password,
    }, {
      params: {
        gsqlFormat: true,
      },
      ...config
    });

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>('/api/gsql-server/gsql/users', params, config);

    return res;
  }
};
