import { CommonRequestConfig } from '../common';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetUserListParams = {
  graph?: string;
  user?: string;
};

export const getUserList = async (
  params: GetUserListParams,
  config?: CommonRequestConfig
) => {
  if (await getVersionIsGTE410(config)) {
    const path = ['/api/gsql-server/gsql/scim/v2/Users', params?.user].filter(Boolean).join('/')
    const res = getAxiosInstance().get(path, {
      params: {
        ...params,
        gsqlFormat: true,
      },
      ...config,
    });
    return res;
  } else {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/users', {
      params: {
        name: params.user,
        ...params,
      },
      ...config,
    });
    return res;
  }
};
