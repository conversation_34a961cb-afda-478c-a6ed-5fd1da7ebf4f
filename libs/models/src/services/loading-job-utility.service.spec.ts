import { Graph } from "../topology";

import { LoadingJobUtilityService } from "./loading-job-utility.service";

describe("LoadingJobUtilityService", () => {
  let service: LoadingJobUtilityService;

  beforeEach(() => {
    service = new LoadingJobUtilityService();
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });

  it("empty array should be returned if the Json file loaded from DB is undefined", () => {
    expect(
      service.loadLoadingJobsFromGSQLAndDB(new Graph(), [], undefined, [])
        .length
    ).toBe(0);
  });
});
