import { isEqual } from 'lodash';

import { History } from '../history';
import { SchemaChangeType } from '../topology';

import { SchemaChangeLogicService } from './schema-change-logic.service';

class MockLoggerService {
  error() {}
  log() {}
  warn() {}
}

interface MockAttribute {
  AttributeName: string;
  AttributeType: {
    Name: string
  };
  HasIndex?: boolean;
  IndexName?: string;
  IsDiscriminator?: boolean;
}

interface MockVertex {
  Name: string;
  PrimaryId: MockAttribute;
  Attributes: MockAttribute[];
  IsLocal?: boolean;
}

interface MockEdge {
  Name: string;
  FromVertexTypeName: string;
  ToVertexTypeName: string;
  IsDirected: boolean;
  Attributes: MockAttribute[];
  IsLocal?: boolean;
  Config?: any;
  DiscriminatorCount: number;
  CompositeDiscriminator?: string[];
  DiscriminatorList?: string[];
}

class MockGraph {
  VertexTypes: MockVertex[];
  EdgeTypes: MockEdge[];

  dumpToGSQLJson() {
    return {
      VertexTypes: this.VertexTypes,
      EdgeTypes: this.EdgeTypes
    };
  }

  getEdge(name: string) {
    return {
      edgeTypes: this.EdgeTypes,
      dumpToGSQLJson() {
        return this.edgeTypes.filter(edgeType => edgeType.Name === name)[0];
      }
    };
  }

  getVertex(name: string) {
    return {
      vertexTypes: this.VertexTypes,
      dumpToGSQLJson() {
        return this.vertexTypes.filter(vertexType => vertexType.Name === name)[0];
      }
    };
  }
}

describe('SchemaChangeLogicService', () => {
  let service: SchemaChangeLogicService;

  beforeEach(() => {
    service = new SchemaChangeLogicService();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getSchemaChange', () => {
    it('should return empty schema change object if graph schema change history length == 1',
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
    });

    it(`should return empty schema change object if graph schema change doesn't touch schema structure`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.undo();
      history.undo();
      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
    });

    it(`should add new vertex and edge types successfully`,
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, v2, e
      // add v2 with attribute index
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      // mockGraph3: v, v2, e, e2
      const mockGraph3 = new MockGraph();
      mockGraph3.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'attr1_random'
            }
          ]
        }
      ];
      mockGraph3.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph4: v, v2New, e, e2
      const mockGraph4 = new MockGraph();
      mockGraph4.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2new',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr0',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ]
        }
      ];
      mockGraph4.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph5: v, v2New, e, e2, multiEdge1
      const mockGraph5 = new MockGraph();
      mockGraph5.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2new',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr0',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ]
        }
      ];
      mockGraph5.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'INT'
              }
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr4'],
          CompositeDiscriminator: ['attr4']
        }
      ];
      spyOn(<any>service, 'generateAttributeIndexName').and.returnValue('index_random');

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);
      const addVertexWithAttributeIndex = service.getSchemaChange(<any>history);
      expect(addVertexWithAttributeIndex.addVertexTypes.length).toBe(1);
      expect(isEqual(addVertexWithAttributeIndex.addVertexTypes[0], {
        Name: 'v2',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'INT'
          }
        },
        Attributes: [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'INT'
            },
            HasIndex: true,
            IndexName: 'index_random'
          }
        ]
      })).toBeTruthy();
      expect(addVertexWithAttributeIndex.alterVertexTypes.length).toBe(0);
      expect((<any>service).generateAttributeIndexName).toHaveBeenCalled();
      history.update(mockGraph3);
      history.update(mockGraph4);
      let schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.addVertexTypes[0], {
        Name: 'v2new',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'STRING'
          }
        },
        Attributes: [
          {
            AttributeName: 'attr0',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ]
      })).toBeTruthy();
      expect(schemaChangeObject.addEdgeTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.addEdgeTypes[0], {
        Name: 'e2',
        FromVertexTypeName: 'v',
        ToVertexTypeName: 'v2new',
        IsDirected: true,
        Attributes: [
          {
            AttributeName: 'attr4',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ],
        DiscriminatorCount: 0
      })).toBeTruthy();

      history.update(mockGraph5);
      schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.addVertexTypes[0], {
        Name: 'v2new',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'STRING'
          }
        },
        Attributes: [
          {
            AttributeName: 'attr0',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ]
      })).toBeTruthy();
      expect(schemaChangeObject.addEdgeTypes.length).toBe(2);
      expect(isEqual(schemaChangeObject.addEdgeTypes[0], {
        Name: 'e2',
        FromVertexTypeName: 'v',
        ToVertexTypeName: 'v2new',
        IsDirected: true,
        Attributes: [
          {
            AttributeName: 'attr4',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ],
        DiscriminatorCount: 0
      })).toBeTruthy();
      expect(isEqual(schemaChangeObject.addEdgeTypes[1], {
        Name: 'multiEdge1',
        FromVertexTypeName: 'v',
        ToVertexTypeName: 'v2new',
        IsDirected: true,
        Attributes: [
          {
            AttributeName: 'attr4',
            AttributeType: {
              Name: 'INT'
            }
          }
        ],
        DiscriminatorCount: 1,
        DiscriminatorList: ['attr4'],
        CompositeDiscriminator: ['attr4']
      })).toBeTruthy();
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
    });

    it('should drop vertex and edge types successfully',
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, v2, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      // mockGraph3: v, v2, e, e2
      const mockGraph3 = new MockGraph();
      mockGraph3.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph3.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph4: v, v2New, e, e2, multiedge1
      const mockGraph4 = new MockGraph();
      mockGraph4.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2new',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr0',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ]
        }
      ];
      mockGraph4.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'INT'
              }
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr4'],
          CompositeDiscriminator: ['attr4']
        }
      ];
      // mockGraph5: v, v2New, e, e2, e3
      const mockGraph5 = new MockGraph();
      mockGraph5.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2new',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr0',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ]
        }
      ];
      mockGraph5.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'INT'
              }
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr4'],
          CompositeDiscriminator: ['attr4']
        },
        {
          Name: 'e3',
          FromVertexTypeName: 'v2new',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph6: v, v2New, e, e2
      const mockGraph6 = new MockGraph();
      mockGraph6.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2new',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr0',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ]
        }
      ];
      mockGraph6.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v2new',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph4);
      history.update(mockGraph5);
      history.update(mockGraph4);
      history.update(mockGraph6);
      history.update(mockGraph3);
      history.update(mockGraph2);
      history.update(mockGraph1);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(1);
      expect(schemaChangeObject.dropVertexTypes[0]).toBe('v2new');
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(2);
      expect(schemaChangeObject.dropEdgeTypes[0]).toBe('e2');
      expect(schemaChangeObject.dropEdgeTypes[1]).toBe('multiEdge1');
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
    });

    it(`should drop edge and add new edge if edge is renamed`,
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, e2
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e2',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.addEdgeTypes[0], {
        Name: 'e2',
        FromVertexTypeName: 'v',
        ToVertexTypeName: 'v',
        IsDirected: true,
        Attributes: [
          {
            AttributeName: 'attr3',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ],
        DiscriminatorCount: 0
      })).toBeTruthy();
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(1);
      expect(schemaChangeObject.dropEdgeTypes[0]).toBe('e');
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
    });

    it(`should drop edge and add new edge if edge attribute change is in-compatible`,
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.alterEdgeTypes, [
        {
          name: 'e',
          dropAttributes: ['attr3'],
          addAttributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          dropIndexAttributes: [],
          addIndexAttributes: []
        }
      ])).toBeTruthy();
    });

    it(`should drop edge and add new edge if edge discriminator attributes are changed.`,
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'INT'
              },
              IsDiscriminator: true
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr3'],
          CompositeDiscriminator: ['attr3']
        }
      ];
      // mockGraph2: v, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              IsDiscriminator: true
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr1'],
          CompositeDiscriminator: ['attr1']
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(1);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(1);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
      expect(isEqual(schemaChangeObject.addEdgeTypes, [{
        Name: 'multiEdge1',
        FromVertexTypeName: 'v',
        ToVertexTypeName: 'v',
        IsDirected: true,
        Attributes: [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'INT'
            },
            IsDiscriminator: true
          }
        ],
        DiscriminatorCount: 1,
        DiscriminatorList: ['attr1'],
        CompositeDiscriminator: ['attr1']
      }])).toBeTruthy();
      expect(isEqual(schemaChangeObject.dropEdgeTypes, ['multiEdge1'])).toBeTruthy();
    });

    it(`should alter edge if only non discriminator attributes are changed`,
    () => {
      // mockGraph1: v, e
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'INT'
              },
              IsDiscriminator: true
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'STRING'
              },
              IsDiscriminator: false
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr3'],
          CompositeDiscriminator: ['attr3']
        }
      ];
      // mockGraph2: v, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'multiEdge1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'INT'
              },
              IsDiscriminator: true
            },
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              },
              IsDiscriminator: false
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'STRING'
              },
              IsDiscriminator: false
            }
          ],
          DiscriminatorCount: 1,
          DiscriminatorList: ['attr3'],
          CompositeDiscriminator: ['attr3']
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(0);
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.alterEdgeTypes, [
        {
          name: 'multiEdge1',
          dropAttributes: ['attr2'],
          addAttributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              },
              IsDiscriminator: false
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'STRING'
              },
              IsDiscriminator: false
            }
          ],
          dropIndexAttributes: [],
          addIndexAttributes: []
        }
      ])).toBeTruthy();
    });

    describe('should drop vertex and add new vertex if vertex attributes orders are changed', () => {
      // mockGraph1: v, v2, e, e2
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            },
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];

      it('with no attribute index in original schema',
      () => {
        const history: History<MockGraph> = new History();
        history.update(mockGraph1);
        history.update(mockGraph2);

        const schemaChangeObject = service.getSchemaChange(<any>history);
        expect(schemaChangeObject.addVertexTypes.length).toBe(0);
        expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
        expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
        expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
        expect(schemaChangeObject.alterVertexTypes.length).toBe(1);
        expect(schemaChangeObject.alterEdgeTypes.length).toBe(0);
        expect(isEqual(schemaChangeObject.alterVertexTypes, [
          {
            name: 'v',
            dropAttributes: ['attr1'],
            addAttributes: [
              {
                AttributeName: 'attr1',
                AttributeType: {
                  Name: 'STRING'
                }
              }
            ],
            dropIndexAttributes: [],
            addIndexAttributes: []
          }
        ]));
      });

      it('with attribute index in original schema',
      () => {
        mockGraph1.VertexTypes[0].Attributes = [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'STRING'
            },
            HasIndex: true,
            IndexName: 'attr1_index'
          },
          {
            AttributeName: 'attr2',
            AttributeType: {
              Name: 'BOOL'
            },
            HasIndex: true,
            IndexName: 'attr2_index'
          }
        ];
        mockGraph2.VertexTypes[0].Attributes = [
          {
            AttributeName: 'attr2',
            AttributeType: {
              Name: 'BOOL'
            },
            HasIndex: false
          },
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'STRING'
            },
            HasIndex: true,
            IndexName: 'attr1_index'
          }
        ];
        const history: History<MockGraph> = new History();
        history.update(mockGraph1);
        history.update(mockGraph2);

        const schemaChangeObject = service.getSchemaChange(<any>history);
        expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
        expect(schemaChangeObject.addVertexTypes.length).toBe(0);
        expect(schemaChangeObject.alterVertexTypes.length).toBe(1);
        expect(isEqual(schemaChangeObject.alterVertexTypes[0], {
          name: 'v',
          addAttributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              },
              HasIndex: true,
              IndexName: 'attr1_index'
            }
          ],
          dropAttributes: [],
          dropIndexAttributes: [
            {
              attributeName: 'attr2',
              indexName: 'attr2_index'
            }
          ],
          addIndexAttributes: [
            {
              attributeName: 'attr1',
              indexName: 'attr1_index'
            }
          ]
        }));
      });
    });

    it(`should generate correct altering vertex or edge object according to attributes changes`,
    () => {
      // mockGraph1: v, v2, e, e2
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'index'
            },
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, e
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: false
            },
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING'
              },
              HasIndex: true
            },
            {
              AttributeName: 'attr5',
              AttributeType: {
                Name: 'UINT'
              },
              HasIndex: true
            },
            {
              AttributeName: 'attr6',
              AttributeType: {
                Name: 'DOUBLE'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      spyOn(<any>service, 'generateAttributeIndexName').and.returnValue('generate_index');

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeObject = service.getSchemaChange(<any>history);
      expect((<any>service).generateAttributeIndexName).toHaveBeenCalledTimes(2);
      expect(schemaChangeObject.addVertexTypes.length).toBe(0);
      expect(schemaChangeObject.addEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.dropVertexTypes.length).toBe(0);
      expect(schemaChangeObject.dropEdgeTypes.length).toBe(0);
      expect(schemaChangeObject.alterVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeObject.alterVertexTypes[0], {
        name: 'v',
        dropAttributes: ['attr4'],
        addAttributes: [
          {
            AttributeName: 'attr5',
            AttributeType: {
              Name: 'UINT'
            },
            HasIndex: true
          },
          {
            AttributeName: 'attr6',
            AttributeType: {
              Name: 'DOUBLE'
            }
          }
        ],
        addIndexAttributes: [
          {
            attributeName: 'attr3',
            indexName: 'generate_index'
          },
          {
            attributeName: 'attr5',
            indexName: 'generate_index'
          }
        ],
        dropIndexAttributes: [
          {
            attributeName: 'attr2',
            indexName: 'index'
          }
        ]
      })).toBeTruthy();
      expect(schemaChangeObject.alterEdgeTypes.length).toBe(2);
      expect(isEqual(schemaChangeObject.alterEdgeTypes[0], {
        name: 'e',
        dropAttributes: ['attr3'],
        addAttributes: [],
        addIndexAttributes: [],
        dropIndexAttributes: []
      })).toBeTruthy();
      expect(isEqual(schemaChangeObject.alterEdgeTypes[1], {
        name: 'e2',
        dropAttributes: [],
        addAttributes: [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'STRING'
            }
          },
          {
            AttributeName: 'attr2',
            AttributeType: {
              Name: 'STRING COMPRESS'
            }
          }
        ],
        addIndexAttributes: [],
        dropIndexAttributes: []
      })).toBeTruthy();
    });
  });

  describe('getSchemaChangeJobs', () => {
    it('should return empty array if schema change history is empty',
    () => {
      const history: History<any> = new History();
      const schemaChanges = service.getSchemaChangeJobs(history, false);
      expect(schemaChanges.length).toBe(0);
    });

    it(`should return empty schema change object if graph schema change doesn't touch schema structure`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.update(mockGraph);
      history.undo();
      history.undo();
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(0);
    });

    it(`should generate 2 global schema change job if schema change has global drop and add sections`,
    () => {
      // mockGraph1: v, v2, e, e2
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, v2, e, e2
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            },
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: []
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, true);
      expect(schemaChangeJobs.length).toBe(2);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(isEqual(schemaChangeJobs[0].alterVertexTypes[0], {
        name : 'v',
        addAttributes: [],
        dropAttributes: ['attr1'],
        dropIndexAttributes: [],
        addIndexAttributes: []
      }));

      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeJobs[1].alterVertexTypes[0], {
        name : 'v',
        addAttributes: [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'STRING'
            }
          }
        ],
        dropAttributes: [],
        dropIndexAttributes: [],
        addIndexAttributes: []
      }));
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
    });

    it('should generate 1 global schema change jobs if schema change has add global vertex with attribute index' +
    ' and drop attribute index', () => {
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'drop_index'
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [];
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: false
            }
          ]
        },
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'name',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'attr_index'
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [];
      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, true);
      expect(schemaChangeJobs.length).toBe(1);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterVertexTypes[0].dropIndexAttributes.length).toBe(1);
    });

    it(`should generate 2 local schema change jobs if schema change has local drop and add sections`,
    () => {
      // mockGraph1: v, v2, e, e2
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          IsLocal: true
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];
      // mockGraph2: v, v2, e, e2
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            },
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          IsLocal: true
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(2);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(isEqual(schemaChangeJobs[0].alterVertexTypes[0], {
        name : 'v',
        addAttributes: [],
        dropAttributes: ['attr1'],
        dropIndexAttributes: [],
        addIndexAttributes: []
      }));

      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeJobs[1].alterVertexTypes[0], {
        name : 'v',
        addAttributes: [
          {
            AttributeName: 'attr1',
            AttributeType: {
              Name: 'STRING'
            }
          }
        ],
        dropAttributes: [],
        dropIndexAttributes: [],
        addIndexAttributes: []
      }));
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
    });

    it('should generate 1 local schema change jobs if schema change has add local vertex with attribute index' +
    ' and drop attribute index', () => {
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'index_drop'
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph1.EdgeTypes = [];
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: false
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'name',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'attr_index'
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph2.EdgeTypes = [];
      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(1);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterVertexTypes[0].dropIndexAttributes.length).toBe(1);
    });

    it(`should generate 2 schema change jobs, 1 local then 1 global`,
    () => {
      // mockGraph1: v1 (global), v2(local), e1(global), e2(local)
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          IsLocal: true
        }
      ];
      mockGraph1.EdgeTypes = [
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];

      // mockGraph2: v1 (global), v2(local), e1(global), e2(local)
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          IsLocal: true
        }
      ];
      mockGraph2.EdgeTypes = [
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      // mockGraph3: v1 (global), v2(local), e1(global), e2(local)
      const mockGraph3 = new MockGraph();
      mockGraph3.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          IsLocal: true
        }
      ];
      mockGraph3.EdgeTypes = [
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      // mockGraph4: v1 (global), v2(local), e1(global), e2(local)
      const mockGraph4 = new MockGraph();
      mockGraph4.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph4.EdgeTypes = [
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e1',
          FromVertexTypeName: 'v',
          ToVertexTypeName: 'v',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        }
      ];

      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);
      history.update(mockGraph3);
      history.update(mockGraph4);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(2);

      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].graphs.length).toBe(1);
      expect(schemaChangeJobs[0].graphs[0].addEdgeTypes).toEqual(['e1']);

      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(1);
      expect(isEqual(schemaChangeJobs[1].alterEdgeTypes[0], {
        name: 'e2',
        dropAttributes: [],
        addAttributes: [{
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'STRING'
          }
        }],
        addIndexAttributes: [],
        dropIndexAttributes: []
      })).toBeTruthy();
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(1);
      expect(isEqual(schemaChangeJobs[1].alterVertexTypes[0], {
        name: 'v2',
        dropAttributes: [],
        addAttributes: [{
          AttributeName: 'attr1',
          AttributeType: {
            Name: 'INT'
          }
        }],
        addIndexAttributes: [],
        dropIndexAttributes: []
      })).toBeTruthy();
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
    });

    // TODO: fix after approve code
    xit('should generate 4 local schema change jobs if add 1 global and 1 local vertices both with attribute index',
    () => {
      const mockGraph1 = new MockGraph();
      mockGraph1.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              }
            }
          ]
        }
      ];
      mockGraph1.EdgeTypes = [];
      const mockGraph2 = new MockGraph();
      mockGraph2.VertexTypes = [
        {
          Name: 'v',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              }
            }
          ]
        },
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'name',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'INT'
              },
              HasIndex: true,
              IndexName: 'attr_index'
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'DOUBLE'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'STRING'
              },
              HasIndex: true,
              IndexName: 'attr2_index'
            }
          ]
        }
      ];
      mockGraph2.EdgeTypes = [];
      const history: History<MockGraph> = new History();
      history.update(mockGraph1);
      history.update(mockGraph2);

      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(4);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(0);

      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(0);

      expect(schemaChangeJobs[2].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[2].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[2].alterVertexTypes[0].addIndexAttributes.length).toBe(1);

      expect(schemaChangeJobs[3].schemaChangeType).toEqual('local');
      expect(schemaChangeJobs[3].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[3].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[3].alterVertexTypes[0].addIndexAttributes.length).toBe(1);
    });

    // TODO: fix after approve code
    xit(`should get 4 schema change jobs at most in the case each section contains both global part and local part`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'BOOL'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v3',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v4',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e3',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e4',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      spyOn(service, 'getSchemaChange').and.returnValue({
        schemaChangeType: SchemaChangeType.Raw,
        alterEdgeTypes: [
          {
            name: 'e1',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          },
          {
            name: 'e2',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        alterVertexTypes: [
          {
            name: 'v1',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          },
          {
            name: 'v2',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        dropEdgeTypes: ['e3', 'e4'],
        dropVertexTypes: ['v3', 'v4'],
        addVertexTypes: [
          {
            Name: 'v3',
            PrimaryId: {
              AttributeName: 'id',
              AttributeType: {
                Name: 'STRING'
              }
            },
            Attributes: [
              {
                AttributeName: 'attr1',
                AttributeType: {
                  Name: 'STRING'
                }
              },
              {
                AttributeName: 'attr2',
                AttributeType: {
                  Name: 'BOOL'
                }
              }
            ],
            Config: undefined
          },
          {
            Name: 'v4',
            PrimaryId: {
              AttributeName: 'id',
              AttributeType: {
                Name: 'STRING'
              }
            },
            Attributes: [
              {
                AttributeName: 'attr1',
                AttributeType: {
                  Name: 'STRING'
                }
              },
              {
                AttributeName: 'attr2',
                AttributeType: {
                  Name: 'BOOL'
                }
              }
            ],
            IsLocal: true,
            Config: undefined
          }
        ],
        addEdgeTypes: [
          {
            Name: 'e3',
            FromVertexTypeName: 'v1',
            ToVertexTypeName: 'v1',
            IsDirected: true,
            Attributes: [
              {
                AttributeName: 'attr3',
                AttributeType: {
                  Name: 'STRING'
                }
              }
            ],
            Config: undefined
          },
          {
            Name: 'e4',
            FromVertexTypeName: 'v1',
            ToVertexTypeName: 'v1',
            IsDirected: true,
            Attributes: [
              {
                AttributeName: 'attr3',
                AttributeType: {
                  Name: 'STRING'
                }
              }
            ],
            IsLocal: true,
            Config: undefined
          }
        ]
      });
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(4);
      // drop e4, v4, alter e2, v2
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterEdgeTypes[0].name).toBe('e2');
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterVertexTypes[0].name).toBe('v2');
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropEdgeTypes[0]).toBe('e4');
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropVertexTypes[0]).toBe('v4');
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      // drop e3, v3, alter e1, v1
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[1].alterEdgeTypes[0].name).toBe('e1');
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[1].alterVertexTypes[0].name).toBe('v1');
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[1].dropEdgeTypes[0]).toBe('e3');
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[1].dropVertexTypes[0]).toBe('v3');
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
      // add v3, e3
      expect(schemaChangeJobs[2].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[2].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[2].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[2].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[2].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[2].addVertexTypes[0].Name).toBe('v3');
      expect(schemaChangeJobs[2].addEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[2].addEdgeTypes[0].Name).toBe('e3');
      // add v4, e4
      expect(schemaChangeJobs[3].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[3].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[3].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[3].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[3].addVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[3].addVertexTypes[0].Name).toBe('v4');
      expect(schemaChangeJobs[3].addEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[3].addEdgeTypes[0].Name).toBe('e4');
    });

    // TODO: fix after approve code
    xit(`should get 2 schema change jobs if alter local vertices, drop local edges `
    + `and drop both global and local vertices`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v3',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v4',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e3',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e4',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      spyOn(service, 'getSchemaChange').and.returnValue({
        schemaChangeType: SchemaChangeType.Raw,
        alterEdgeTypes: [],
        alterVertexTypes: [
          {
            name: 'v2',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        dropEdgeTypes: ['e4'],
        dropVertexTypes: ['v3', 'v4'],
        addVertexTypes: [],
        addEdgeTypes: []
      });
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(2);
      // alter v2, drop e4, drop v4
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterVertexTypes[0].name).toBe('v2');
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropEdgeTypes[0]).toBe('e4');
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropVertexTypes[0]).toBe('v4');
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      // drop v3
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[1].dropVertexTypes[0]).toBe('v3');
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
    });

    // TODO: fix after approve code
    xit(`should get 2 schema change jobs if alter local vertex type and drop global edge type`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v3',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v4',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e3',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e4',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          Config: {
            REVERSE_EDGE: 'reverse_e4'
          },
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      spyOn(service, 'getSchemaChange').and.returnValue({
        schemaChangeType: SchemaChangeType.Raw,
        alterEdgeTypes: [],
        alterVertexTypes: [
          {
            name: 'v2',
            dropAttributes: [],
            addAttributes: [{
              AttributeName: 'attr4',
              AttributeType: {
                Name: 'STRING'
              }
            }],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        dropEdgeTypes: ['e3'],
        dropVertexTypes: [],
        addVertexTypes: [],
        addEdgeTypes: []
      });
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(2);
      // global job, drop e3
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[0].dropEdgeTypes[0]).toBe('e3');
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      // local job, alter v2
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[1].alterVertexTypes[0].name).toBe('v2');
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
    });

    // TODO: fix after approve code
    xit(`should drop both forward and reverse edge in schema change`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        },
        {
          Name: 'v3',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v4',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        },
        {
          Name: 'e3',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          Config: {
            REVERSE_EDGE: 'reverse_e3'
          },
          DiscriminatorCount: 0
        },
        {
          Name: 'e4',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr3',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          Config: {
            REVERSE_EDGE: 'reverse_e4'
          },
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      spyOn(service, 'getSchemaChange').and.returnValue({
        schemaChangeType: SchemaChangeType.Raw,
        alterEdgeTypes: [],
        alterVertexTypes: [],
        dropEdgeTypes: ['e3', 'e4'],
        dropVertexTypes: [],
        addVertexTypes: [],
        addEdgeTypes: []
      });
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(2);
      // local job, drop e4, reverse_e4
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(2);
      expect(schemaChangeJobs[0].dropEdgeTypes[0]).toBe('e4');
      expect(schemaChangeJobs[0].dropEdgeTypes[1]).toBe('reverse_e4');
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      // global job, drop e3, reverse_e3
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(2);
      expect(schemaChangeJobs[1].dropEdgeTypes[0]).toBe('e3');
      expect(schemaChangeJobs[1].dropEdgeTypes[1]).toBe('reverse_e3');
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
    });

    // TODO: fix after approve code
    xit(`should split one job into two jobs if the job contains drop and add attribute of ` +
    `one entity with same name`,
    () => {
      const mockGraph = new MockGraph();
      mockGraph.VertexTypes = [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ]
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'attr2',
              AttributeType: {
                Name: 'BOOL'
              }
            }
          ],
          IsLocal: true
        }
      ];
      mockGraph.EdgeTypes = [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          DiscriminatorCount: 0
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Attributes: [
            {
              AttributeName: 'attr1',
              AttributeType: {
                Name: 'STRING COMPRESS'
              }
            }
          ],
          IsLocal: true,
          DiscriminatorCount: 0
        }
      ];
      const history: History<MockGraph> = new History();
      history.update(mockGraph);
      spyOn(service, 'getSchemaChange').and.returnValue({
        schemaChangeType: SchemaChangeType.Raw,
        alterEdgeTypes: [
          {
            name: 'e1',
            dropAttributes: [],
            addAttributes: [
              {
                AttributeName: 'attr3',
                AttributeType: {
                  Name: 'INT'
                }
              }
            ],
            addIndexAttributes: [],
            dropIndexAttributes: []
          },
          {
            name: 'e2',
            dropAttributes: ['attr2'],
            addAttributes: [
              {
                AttributeName: 'attr2',
                AttributeType: {
                  Name: 'DATETIME'
                }
              }
            ],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        alterVertexTypes: [
          {
            name: 'v1',
            dropAttributes: ['attr1'],
            addAttributes: [
              {
                AttributeName: 'attr1',
                AttributeType: {
                  Name: 'INT'
                }
              }
            ],
            addIndexAttributes: [],
            dropIndexAttributes: []
          },
          {
            name: 'v2',
            dropAttributes: ['attr1'],
            addAttributes: [
            ],
            addIndexAttributes: [],
            dropIndexAttributes: []
          }
        ],
        dropEdgeTypes: [],
        dropVertexTypes: [],
        addVertexTypes: [],
        addEdgeTypes: []
      });
      const schemaChangeJobs = service.getSchemaChangeJobs(<any>history, false);
      expect(schemaChangeJobs.length).toBe(4);
      // local job, e1 -attr2, v2 -attr1
      expect(schemaChangeJobs[0].alterEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterEdgeTypes[0].name).toBe('e2');
      expect(schemaChangeJobs[0].alterEdgeTypes[0].dropAttributes[0]).toBe('attr2');
      expect(schemaChangeJobs[0].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[0].alterVertexTypes[0].name).toBe('v2');
      expect(schemaChangeJobs[0].alterVertexTypes[0].dropAttributes[0]).toBe('attr1');
      expect(schemaChangeJobs[0].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[0].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[0].addEdgeTypes.length).toBe(0);
      // local job, e2 +attr2
      expect(schemaChangeJobs[1].alterEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[1].alterEdgeTypes[0].name).toBe('e2');
      expect(schemaChangeJobs[1].alterEdgeTypes[0].addAttributes[0].AttributeName).toBe('attr2');
      expect(schemaChangeJobs[1].alterVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[1].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[1].addEdgeTypes.length).toBe(0);
      // global job, v1 -attr1
      expect(schemaChangeJobs[2].alterEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[2].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[2].alterVertexTypes[0].dropAttributes[0]).toBe('attr1');
      expect(schemaChangeJobs[2].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[2].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[2].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[2].addEdgeTypes.length).toBe(0);
      // global job, e1 +attr3, v1 +attr1
      expect(schemaChangeJobs[3].alterEdgeTypes.length).toBe(1);
      expect(schemaChangeJobs[3].alterEdgeTypes[0].name).toBe('e1');
      expect(schemaChangeJobs[3].alterEdgeTypes[0].addAttributes[0].AttributeName).toBe('attr3');
      expect(schemaChangeJobs[3].alterVertexTypes.length).toBe(1);
      expect(schemaChangeJobs[3].alterVertexTypes[0].name).toBe('v1');
      expect(schemaChangeJobs[3].alterVertexTypes[0].addAttributes[0].AttributeName).toBe('attr1');
      expect(schemaChangeJobs[3].dropEdgeTypes.length).toBe(0);
      expect(schemaChangeJobs[3].dropVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[3].addVertexTypes.length).toBe(0);
      expect(schemaChangeJobs[3].addEdgeTypes.length).toBe(0);
    });
  });
});
