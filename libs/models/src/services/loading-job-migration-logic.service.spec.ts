import { cloneDeep, isEqual } from 'lodash';

import { History } from '../history';
import { OneColumnMapping, SourceType } from '../loading-job';
import { LoadingJobUtilityService } from './loading-job-utility.service';

import { LoadingJobMigrationLogicService } from './loading-job-migration-logic.service';

interface MockAttribute {
  AttributeName: string;
  AttributeType: {
    Name: string
  };
}

interface MockVertex {
  Name: string;
  PrimaryId: MockAttribute;
  Attributes: MockAttribute[];
}

interface MockEdge {
  Name: string;
  FromVertexTypeName: string;
  ToVertexTypeName: string;
  IsDirected: boolean;
  Attributes: MockAttribute[];
}

class MockGraph {
  VertexTypes: MockVertex[];
  EdgeTypes: MockEdge[];

  dumpToGSQLJson() {
    return {
      VertexTypes: this.VertexTypes,
      EdgeTypes: this.EdgeTypes
    };
  }
}

interface MockLoadToVertex {
  vertexName: string;
  mappings: OneColumnMapping[];
}

interface MockLoadToEdge {
  edgeName: string;
  fromVertexType: string;
  toVertexType: string;
  mappings: OneColumnMapping[];
}

interface MockLoadingJob {
  jobName: string;
  loadingStatements: (MockLoadToVertex | MockLoadToEdge)[];
}

describe('LoadingJobMigrationLogicService', () => {
  let initialGraph: MockGraph;
  let personVertexCollection: Map<string, MockVertex>;
  let movieVertexCollection: Map<string, MockVertex>;
  let watchEdgeCollection: Map<string, MockEdge>;
  let friendWithEdgeCollection: Map<string, MockEdge>;
  let initialLoadingJobs: MockLoadingJob[];
  let service: LoadingJobMigrationLogicService;

  beforeEach(() => {
    service = new LoadingJobMigrationLogicService(
      new LoadingJobUtilityService()
    );

    personVertexCollection = new Map();
    // original person vertex: person(primary_id id string, name string, gender string compress, age uint)
    personVertexCollection.set('original', {
      Name: 'person',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        },
        {
          AttributeName: 'gender',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        },
        {
          AttributeName: 'age',
          AttributeType: {
            Name: 'UINT'
          }
        }
      ]
    });
    personVertexCollection.set('renamed', {
      Name: 'personNewName',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        },
        {
          AttributeName: 'gender',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        },
        {
          AttributeName: 'age',
          AttributeType: {
            Name: 'UINT'
          }
        }
      ]
    });
    personVertexCollection.set('changepid', {
      Name: 'person',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'DOUBLE'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        },
        {
          AttributeName: 'gender',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        },
        {
          AttributeName: 'age',
          AttributeType: {
            Name: 'UINT'
          }
        }
      ]
    });
    personVertexCollection.set('changeattr', {
      Name: 'person',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'gender',
          AttributeType: {
            Name: 'STRING COMPRESS'
          }
        },
        {
          AttributeName: 'newAttr',
          AttributeType: {
            Name: 'BOOL'
          }
        },
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        }
      ]
    });

    movieVertexCollection = new Map();
    // original movie vertex: movie(primary_id id string, name string)
    movieVertexCollection.set('original', {
      Name: 'movie',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'STRING'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        }
      ]
    });
    movieVertexCollection.set('changepid', {
      Name: 'movie',
      PrimaryId: {
        AttributeName: 'id',
        AttributeType: {
          Name: 'INT'
        }
      },
      Attributes: [
        {
          AttributeName: 'name',
          AttributeType: {
            Name: 'STRING'
          }
        }
      ]
    });

    watchEdgeCollection = new Map();
    // original watch edge: directed watch(from person, to movie, when datetime)
    watchEdgeCollection.set('original', {
      Name: 'watch',
      FromVertexTypeName: 'person',
      ToVertexTypeName: 'movie',
      IsDirected: true,
      Attributes: [
        {
          AttributeName: 'when',
          AttributeType: {
            Name: 'DATETIME'
          }
        }
      ]
    });
    watchEdgeCollection.set('personrenamed', {
      Name: 'watch',
      FromVertexTypeName: 'personNewName',
      ToVertexTypeName: 'movie',
      IsDirected: true,
      Attributes: [
        {
          AttributeName: 'when',
          AttributeType: {
            Name: 'DATETIME'
          }
        }
      ]
    });

    friendWithEdgeCollection = new Map();
    // original friend_with edge: undirected friend_with(from person, to person, since datetime)
    friendWithEdgeCollection.set('original', {
      Name: 'friend_with',
      FromVertexTypeName: 'person',
      ToVertexTypeName: 'person',
      IsDirected: false,
      Attributes: [
        {
          AttributeName: 'since',
          AttributeType: {
            Name: 'DATETIME'
          }
        }
      ]
    });
    friendWithEdgeCollection.set('personrenamed', {
      Name: 'friend_with',
      FromVertexTypeName: 'personNewName',
      ToVertexTypeName: 'personNewName',
      IsDirected: false,
      Attributes: [
        {
          AttributeName: 'since',
          AttributeType: {
            Name: 'DATETIME'
          }
        }
      ]
    });
    friendWithEdgeCollection.set('changeattr', {
      Name: 'friend_with',
      FromVertexTypeName: 'person',
      ToVertexTypeName: 'person',
      IsDirected: false,
      Attributes: [
        {
          AttributeName: 'since',
          AttributeType: {
            Name: 'UINT'
          }
        }
      ]
    });

    // person -(watch)-> movie, person -(friend_with)-> person
    initialGraph = new MockGraph();
    initialGraph.VertexTypes = [
      personVertexCollection.get('original'),
      movieVertexCollection.get('original')
    ];
    initialGraph.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('original')
    ];

    // initial loading jobs:
    initialLoadingJobs = [
      {
        jobName: 'job_1',
        loadingStatements: [
          // to person vertex: ($0 -> id, $1 -> name, $2 -> gender, $3 -> age)
          {
            vertexName: 'person',
            mappings: [
              {
                sourceType: SourceType.DataSourceColumn,
                index: 0
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 1
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 2
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 3
              }
            ]
          },
          // to movie vertex: ($0 -> id, $1 -> name)
          {
            vertexName: 'movie',
            mappings: [
              {
                sourceType: SourceType.DataSourceColumn,
                index: 0
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 1
              }
            ]
          },
          // to watch edge: ($0 -> person, $1 -> movie, $2 -> when)
          {
            edgeName: 'watch',
            fromVertexType: 'person',
            toVertexType: 'movie',
            mappings: [
              {
                sourceType: SourceType.DataSourceColumn,
                index: 0
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 1
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 2
              }
            ]
          }
        ]
      },
      {
        jobName: 'job_2',
        // to friend_with edge: ($0 -> person, $1 -> person, $2 -> since)
        loadingStatements: [
          {
            edgeName: 'friend_with',
            fromVertexType: 'person',
            toVertexType: 'person',
            mappings: [
              {
                sourceType: SourceType.DataSourceColumn,
                index: 0
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 1
              },
              {
                sourceType: SourceType.DataSourceColumn,
                index: 2
              }
            ]
          }
        ]
      }
    ];
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should report error if graph schema change history length < 1',
  () => {
    const history: History<any> = new History();
    expect(service.migrateLoadingJobs(history, [], [], []).length).toBe(0);
  });

  it('should return same loading jobs as original if graph schema change history length == 1',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    const migrationResult = service.migrateLoadingJobs(<any>history, [], [], []);
    expect(isEqual(migrationResult, initialLoadingJobs)).toBeTruthy();
  });

  it('should return same loading jobs as original if graph schema change does not touch existing vertices and edges',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // A graph with one more vertex type.
    const graphAddVertex = new MockGraph();
    graphAddVertex.VertexTypes = [
      personVertexCollection.get('original'),
      movieVertexCollection.get('original'),
      {
        Name: 'newVertex',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'STRING'
          }
        },
        Attributes: [
          {
            AttributeName: 'someAttr',
            AttributeType: {
              Name: 'DOUBLE'
            }
          }
        ]
      }
    ];
    graphAddVertex.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('original')
    ];
    history.update(graphAddVertex);
    const migrationResult = service.migrateLoadingJobs(<any>history, [], [], []);
    expect(isEqual(migrationResult, initialLoadingJobs)).toBeTruthy();
  });

  it('should remove loading statements target at removed vertex and edge types',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Remove watch edge in graph.
    const graphRemoveWatch = new MockGraph();
    graphRemoveWatch.VertexTypes = [
      personVertexCollection.get('original'),
      movieVertexCollection.get('original')
    ];
    graphRemoveWatch.EdgeTypes = [
      friendWithEdgeCollection.get('original')
    ];
    history.update(graphRemoveWatch);
    const migrationResultWithoutWatch = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultWithoutWatch = cloneDeep(initialLoadingJobs);
    targetMigrationResultWithoutWatch[0].loadingStatements.splice(2, 1);
    expect(isEqual(migrationResultWithoutWatch, targetMigrationResultWithoutWatch)).toBeTruthy();
    // Remove person vertex in graph.
    const graphRemovePerson = new MockGraph();
    graphRemovePerson.VertexTypes = [
      movieVertexCollection.get('original')
    ];
    graphRemovePerson.EdgeTypes = [];
    history.update(graphRemovePerson);
    const migrationResultWithoutPerson = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultWithoutPerson = cloneDeep(targetMigrationResultWithoutWatch);
    targetMigrationResultWithoutPerson[0].loadingStatements.splice(0, 1);
    targetMigrationResultWithoutPerson[1].loadingStatements.splice(0, 1);
    expect(isEqual(migrationResultWithoutPerson, targetMigrationResultWithoutPerson)).toBeTruthy();
  });

  it('should rename vertexName in loading statements target at a renamed vertex type',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Rename watch edge in graph.
    const graphRenamePerson = new MockGraph();
    graphRenamePerson.VertexTypes = [
      personVertexCollection.get('renamed'),
      movieVertexCollection.get('original')
    ];
    graphRenamePerson.EdgeTypes = [
      watchEdgeCollection.get('personrenamed'),
      friendWithEdgeCollection.get('personrenamed')
    ];
    history.update(graphRenamePerson);
    const migrationResultRenamedPerson = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultRenamedPerson = cloneDeep(initialLoadingJobs);
    (<MockLoadToVertex>targetMigrationResultRenamedPerson[0].loadingStatements[0]).vertexName = 'personNewName';
    (<MockLoadToEdge>targetMigrationResultRenamedPerson[0].loadingStatements[2]).fromVertexType = 'personNewName';
    (<MockLoadToEdge>targetMigrationResultRenamedPerson[1].loadingStatements[0]).fromVertexType = 'personNewName';
    (<MockLoadToEdge>targetMigrationResultRenamedPerson[1].loadingStatements[0]).toVertexType = 'personNewName';
    expect(isEqual(migrationResultRenamedPerson, targetMigrationResultRenamedPerson)).toBeTruthy();
  });

  it('should reset mapping to vertex primary_id in loading statements if the primary_id data type is modified',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Change movie primary_id data type.
    const graphChangeMoviePid = new MockGraph();
    graphChangeMoviePid.VertexTypes = [
      personVertexCollection.get('original'),
      movieVertexCollection.get('changepid')
    ];
    graphChangeMoviePid.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('original')
    ];
    history.update(graphChangeMoviePid);
    const migrationResultChangeMoviePid = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultChangeMoviePid = cloneDeep(initialLoadingJobs);
    targetMigrationResultChangeMoviePid[0].loadingStatements[1].mappings[0] = {
      sourceType: SourceType.Default
    };
    targetMigrationResultChangeMoviePid[0].loadingStatements[2].mappings[1] = {
      sourceType: SourceType.Default
    };
    expect(isEqual(migrationResultChangeMoviePid, targetMigrationResultChangeMoviePid)).toBeTruthy();
    // Change person primary_id name.
    const graphChangePersonPid = new MockGraph();
    graphChangePersonPid.VertexTypes = [
      personVertexCollection.get('changepid'),
      movieVertexCollection.get('original')
    ];
    graphChangePersonPid.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('original')
    ];
    history.undo();
    history.update(graphChangePersonPid);
    const migrationResultChangePersonPid = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultChangePersonPid = cloneDeep(initialLoadingJobs);
    targetMigrationResultChangePersonPid[0].loadingStatements[0].mappings[0] = {
      sourceType: SourceType.Default
    };
    targetMigrationResultChangePersonPid[0].loadingStatements[2].mappings[0] = {
      sourceType: SourceType.Default
    };
    targetMigrationResultChangePersonPid[1].loadingStatements[0].mappings[0] = {
      sourceType: SourceType.Default
    };
    targetMigrationResultChangePersonPid[1].loadingStatements[0].mappings[1] = {
      sourceType: SourceType.Default
    };
    expect(isEqual(migrationResultChangePersonPid, targetMigrationResultChangePersonPid)).toBeTruthy();
  });

  it('should set mapping to unmatched attributes to be default mapping',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Change person vertex and friend_with edge attributes.
    const graphChangePersonAttributes = new MockGraph();
    graphChangePersonAttributes.VertexTypes = [
      personVertexCollection.get('changeattr'),
      movieVertexCollection.get('original')
    ];
    graphChangePersonAttributes.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('original')
    ];
    history.update(graphChangePersonAttributes);
    const graphChangeFriendWithAttributes = new MockGraph();
    graphChangeFriendWithAttributes.VertexTypes = [
      personVertexCollection.get('changeattr'),
      movieVertexCollection.get('original')
    ];
    graphChangeFriendWithAttributes.EdgeTypes = [
      watchEdgeCollection.get('original'),
      friendWithEdgeCollection.get('changeattr')
    ];
    history.update(graphChangeFriendWithAttributes);
    const migrationResultChangeAttributes = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResultChangeAttributes = cloneDeep(initialLoadingJobs);
    targetMigrationResultChangeAttributes[0].loadingStatements[0].mappings[1] = {
      sourceType: SourceType.DataSourceColumn,
      index: 2
    };
    targetMigrationResultChangeAttributes[0].loadingStatements[0].mappings[2] = {
      sourceType: SourceType.Default
    };
    targetMigrationResultChangeAttributes[0].loadingStatements[0].mappings[3] = {
      sourceType: SourceType.DataSourceColumn,
        index: 1
    };
    targetMigrationResultChangeAttributes[1].loadingStatements[0].mappings[2] = {
      sourceType: SourceType.Default
    };
    expect(isEqual(migrationResultChangeAttributes, targetMigrationResultChangeAttributes)).toBeTruthy();
  });

  it('should return same loading jobs as original if change edge direction',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Change edge direction.
    const graphChangeEdgeDirection = cloneDeep(initialGraph);
    graphChangeEdgeDirection.EdgeTypes[0].IsDirected = false;
    graphChangeEdgeDirection.EdgeTypes[1].IsDirected = true;
    history.update(graphChangeEdgeDirection);
    const migrationResult = service.migrateLoadingJobs(<any>history, [], [], []);
    expect(isEqual(migrationResult, initialLoadingJobs)).toBeTruthy();
  });

  it('should return same loading jobs as original if change edge source or target vertex from specific type to *',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Change edge source and/or target vertex type from specific type to *
    const graphGeneralizeEdgeSourceTargetVertexType = cloneDeep(initialGraph);
    graphGeneralizeEdgeSourceTargetVertexType.EdgeTypes[0].FromVertexTypeName = '*';
    graphGeneralizeEdgeSourceTargetVertexType.EdgeTypes[1].FromVertexTypeName =
    graphGeneralizeEdgeSourceTargetVertexType.EdgeTypes[1].ToVertexTypeName = '*';
    history.update(graphGeneralizeEdgeSourceTargetVertexType);
    const migrationResult = service.migrateLoadingJobs(<any>history, [], [], []);
    expect(isEqual(migrationResult, initialLoadingJobs)).toBeTruthy();
  });

  it('should reset source or target vertex mapping of loading statement that target at edge to default ' +
  'if edge source or target vertex type changed',
  () => {
    spyOn((<any>service).loadingJobUtility, 'loadLoadingJobsFromGSQLAndDB').and.returnValue(initialLoadingJobs);
    const history: History<MockGraph> = new History();
    history.update(initialGraph);
    // Change edge source and/or target vertex type from specific type to *
    const graphChangeWatchEdgeSourceTargetVertexType = cloneDeep(initialGraph);
    graphChangeWatchEdgeSourceTargetVertexType.EdgeTypes[0].FromVertexTypeName = 'movie';
    graphChangeWatchEdgeSourceTargetVertexType.EdgeTypes[0].ToVertexTypeName = 'person';
    history.update(graphChangeWatchEdgeSourceTargetVertexType);
    const graphChangeFriendWithEdgeSourceVertexType = cloneDeep(graphChangeWatchEdgeSourceTargetVertexType);
    graphChangeFriendWithEdgeSourceVertexType.EdgeTypes[1].FromVertexTypeName = 'movie';
    history.update(graphChangeFriendWithEdgeSourceVertexType);
    const migrationResult = service.migrateLoadingJobs(<any>history, [], [], []);
    const targetMigrationResult = cloneDeep(initialLoadingJobs);
    (<MockLoadToEdge>targetMigrationResult[0].loadingStatements[2]).fromVertexType = 'movie';
    (<MockLoadToEdge>targetMigrationResult[0].loadingStatements[2]).toVertexType = 'person';
    targetMigrationResult[0].loadingStatements[2].mappings[0] = {
      sourceType: SourceType.Default
    };
    targetMigrationResult[0].loadingStatements[2].mappings[1] = {
      sourceType: SourceType.Default
    };
    (<MockLoadToEdge>targetMigrationResult[1].loadingStatements[0]).fromVertexType = 'movie';
    targetMigrationResult[1].loadingStatements[0].mappings[0] = {
      sourceType: SourceType.Default
    };
    expect(isEqual(migrationResult, targetMigrationResult)).toBeTruthy();
  });

});
