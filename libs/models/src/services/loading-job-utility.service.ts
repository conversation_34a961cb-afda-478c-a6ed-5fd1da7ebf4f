import {
  DBLoadingJob<PERSON>son,
  GSQLLoadingJobJson,
  LoadingJobData,
  LoadingJobLogic,
  SourceType,
} from "../loading-job";
import { Graph, GSQLUdtJson, AttributeType } from "../topology";

export class LoadingJobUtilityService {
  constructor() {}

  /**
   * Combine information from GSQL and DB to construct loading jobs.
   *
   * @param {Graph} schema
   * @param {DBGraphStyleJson} graphStyleJson
   * @param {GSQLLoadingJobJson[]} gsqlLoadingJobsJson
   * @param {DBLoadingJobJson[]} dbLoadingJobsJson
   * @param {GSQLUdtJson[]} udtList
   * @returns {LoadingJobData[]}
   * @memberof LoadingJobUtilityService
   */
  loadLoadingJobsFromGSQLAndDB(
    schema: Graph,
    gsqlLoadingJobsJson: GSQLLoadingJobJson[],
    dbLoadingJobsJson: DBLoadingJobJson[],
    udtList: GSQLUdtJson[]
  ): LoadingJobData[] {
    // Load loading jobs
    if (dbLoadingJobsJson !== undefined) {
      const loadingJobs: LoadingJobData[] = [];
      // For each loading job saved in server-side database, find if it is saved in GSQL
      // engine side. If so, combine the information. If not, only use the database side information
      // to construct an empty loading job (only has data source information)
      dbLoadingJobsJson.forEach((dbLoadingJob) => {
        let gsqlLoadingJob: GSQLLoadingJobJson | undefined;
        // Find the gsql loading job with same name as db loading job
        // - Note: If not found, gsqlLoadingJob will be 'undefined', which is handled in loading job logic
        gsqlLoadingJobsJson.forEach((gsqlLoadingJobJson) => {
          if (gsqlLoadingJobJson.JobName === dbLoadingJob.loadingJobName) {
            gsqlLoadingJob = gsqlLoadingJobJson;
          }
        });
        // Validation check. If the loading job contains any unsupported feature(s),
        // the loading job will be skipped in designer
        if (gsqlLoadingJob !== undefined) {
          const validationCheckResult = LoadingJobLogic.validationCheck(
            gsqlLoadingJob,
            dbLoadingJob
          );
          if (!validationCheckResult.success) {
            console.error(validationCheckResult.message);
            return;
          }
        } else {
          return [];
        }
        // Load the GSQL and DB loading job information into in-memory loading job
        const loadingJob = LoadingJobLogic.loadFromGSQLAndDB(
          gsqlLoadingJob,
          dbLoadingJob
        );
        // Semantic check. If the loading job contains any semantic error(s),
        // the loading job will be skipped in designer
        const semanticCheckResult = LoadingJobLogic.semanticCheck(
          loadingJob,
          schema
        );
        if (!semanticCheckResult.success) {
          console.error(semanticCheckResult.message);
          return;
        }

        // For the data mappings to Map attributes, set the Map keyType, valType and udtType
        // according to the attribute type.
        loadingJob.loadingStatements.forEach((loadingStatement) => {
          loadingStatement.mappings.forEach((mapping, i) => {
            // @ts-ignore
            if (
              mapping.sourceType === SourceType.MappingWidget &&
              mapping.index !== undefined
            ) {
              const mappingWidget =
                loadingStatement.mappingWidgets[mapping.index];
              // Assign Map widget key, val and udt type name according to schema attribute type.
              if ("keyType" in mappingWidget) {
                let attrType: AttributeType;
                if ("vertexName" in loadingStatement) {
                  attrType = schema.getVertex(loadingStatement.vertexName)
                    .attributes[i - 1].type;
                } /* if ('edgeName' in loadingStatement) */ else {
                  attrType = schema.getEdge(loadingStatement.edgeName)
                    .attributes[i - 2].type;
                }
                mappingWidget.keyType = attrType.keyTypeName;
                mappingWidget.valType = attrType.valueTypeName;
                mappingWidget.udtType = attrType.valueTypeTupleName;
              }
            }
          });

          // Assign udt of mapping widget only if udt list is provided.
          if (udtList) {
            loadingStatement.mappingWidgets.forEach((mappingWidget) => {
              if ("udtName" in mappingWidget) {
                mappingWidget.udt = udtList.find(
                  (u) => u.name === mappingWidget.udtName
                )!;
              }
            });
          }
        });

        // The loading job is editable because it passed all checks
        loadingJobs.push(loadingJob);
      });
      return loadingJobs;
    }
    return [];
  }
}
