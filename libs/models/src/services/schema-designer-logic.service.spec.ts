
import {
  DBGraph<PERSON>tyle<PERSON><PERSON>, Edge,
  GSQLGraphJson, Vertex, VertexStyle
} from '../topology';

import { SchemaDesignerLogicService } from './schema-designer-logic.service';

describe('SchemaDesignerLogicService', () => {
  let service: SchemaDesignerLogicService;
  let mockGraph1: GSQLGraphJson;
  let mockGraph2: GSQLGraphJson;
  let mockGraphStyle: DBGraphStyleJson;
  let mockGraphStyle2: DBGraphStyleJson;

  beforeEach(() => {
    service = new SchemaDesignerLogicService();
    mockGraph1 = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    mockGraph2 = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v2',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        },
        {
          Name: 'v3',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: {
              Name: 'INT'
            }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        },
        {
          Name: 'e2',
          FromVertexTypeName: 'v2',
          ToVertexTypeName: 'v2',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };

    mockGraphStyle = {
      vertexStyles: {
        'v1': {
          fillColor: '#123456',
          x: 0.3,
          y: 0.45,
          icon: 'img-001',
          other: {}
        },
        'v2': {
          fillColor: '#abcdef',
          x: -1,
          y: -0.5,
          other: {}
        }
      },
      edgeStyles: {
        'e1': {
          fillColor: '#ffffff',
          other: {}
        }
      }
    };
    mockGraphStyle2 = {
      vertexStyles: {
        'v1': {
          fillColor: '#123456',
          x: 0.3,
          y: 0.45,
          icon: 'img-001',
          other: {}
        },
        'v2': {
          fillColor: '#abcdef',
          x: -1,
          y: -0.5,
          icon: 'img-002',
          other: {}
        }
      },
      edgeStyles: {
        'e2': {
          fillColor: '#ffffff',
          other: {}
        }
      }
    };
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should contain empty schema if given undefined schema',
  () => {
    const graphVis = service.setSchemaAndStyle(undefined, undefined);
    expect(graphVis.nodes.length).toBe(0);
    expect(graphVis.links.length).toBe(0);
  });

  it('should contain empty style if given undefined style',
  () => {

    const graphVis = service.setSchemaAndStyle(mockGraph1, undefined);

    expect(graphVis.nodes.length).toBe(2);
    expect(graphVis.nodes[0].vertex_id).toBe('v1');
    expect(graphVis.nodes[0].style).toBeUndefined();
    expect(graphVis.nodes[1].vertex_type).toBe('v2');
    expect(graphVis.nodes[1].style).toBeUndefined();
    expect(graphVis.links.length).toBe(1);
    expect(graphVis.links[0].edge_type).toBe('e1');
    expect(graphVis.links[0].style).toBeUndefined();
  });

  it('should contain empty style if given undefined style',
  () => {
    const graphVis = service.setSchemaAndStyle(mockGraph1, undefined);

    expect(graphVis.nodes.length).toBe(2);
    expect(graphVis.nodes[0].vertex_id).toBe('v1');
    expect(graphVis.nodes[0].style).toBeUndefined();
    expect(graphVis.nodes[1].vertex_type).toBe('v2');
    expect(graphVis.nodes[1].style).toBeUndefined();
    expect(graphVis.links.length).toBe(1);
    expect(graphVis.links[0].edge_type).toBe('e1');
    expect(graphVis.links[0].style).toBeUndefined();
  });

  it('should contain style if given style',
  () => {

    const graphVis = service.setSchemaAndStyle(mockGraph1, mockGraphStyle);

    expect(graphVis.nodes.length).toBe(2);
    expect(graphVis.nodes[0].vertex_id).toBe('v1');
    expect(graphVis.nodes[0].style.x).toBe(0.3);
    expect(graphVis.nodes[1].vertex_type).toBe('v2');
    expect(graphVis.nodes[1].style.fillColor).toBe('#abcdef');
    expect(graphVis.links.length).toBe(1);
    expect(graphVis.links[0].edge_type).toBe('e1');
    expect(graphVis.links[0].style.fillColor).toBe('#ffffff');
  });

  it('should report issue if add vertex with duplicated names',
  () => {
    service.setSchemaAndStyle(undefined, undefined);
    const v1 = new Vertex();
    v1.name = 'v1';
    v1.primaryId.name = 'id';
    const v2 = new Vertex();
    v2.name = 'v1';
    v2.primaryId.name = 'id';
    service.addVertex(v1);
    expect(service.addVertex(v2).success).toBeFalsy();
    expect(service.addVertex(v2).message).toBe('Vertex or edge name "v1" cannot be duplicated ' +
    'in one graph.');
  });

  it('should report issue if add edge pointing to non-exist vertex',
  () => {
    service.setSchemaAndStyle(undefined, undefined);
    const v1 = new Vertex();
    v1.name = 'v1';
    v1.primaryId.name = 'id';
    const e1 = new Edge();
    e1.name = 'e1';
    e1.fromToVertexTypePairs = [{ from: 'v1', to: 'v2' }];
    service.addVertex(v1);
    expect(service.addEdge(e1).success).toBeFalsy();
    expect(service.addEdge(e1).message).toBe('Edge target vertex type "v2" is not in graph.');
  });

  it('should support redo and undo correctly',
  () => {
    service.setSchemaAndStyle(undefined, undefined);
    const v1 = new Vertex();
    v1.name = 'v1';
    v1.primaryId.name = 'id';
    const e1 = new Edge();
    e1.name = 'e1';
    e1.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    service.addVertex(v1);

    let graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.EdgeTypes.length).toBe(0);

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(0);
    expect(graph.EdgeTypes.length).toBe(0);

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.EdgeTypes.length).toBe(0);

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.EdgeTypes.length).toBe(0);

    service.addEdge(e1);
    const v2 = new Vertex();
    v2.name = 'v2';
    v2.primaryId.name = 'id';
    service.addVertex(v2);
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('v2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('e1');

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('e1');

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.EdgeTypes.length).toBe(0);

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(0);
    expect(graph.EdgeTypes.length).toBe(0);

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(0);
    expect(graph.EdgeTypes.length).toBe(0);

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.EdgeTypes.length).toBe(0);

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(1);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('e1');

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('v2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('e1');

    service.redo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('v2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('e1');

    const newE1 = e1.clone();
    newE1.name = 'newE1';
    newE1.fromToVertexTypePairs[0].from = 'v2';
    service.updateEdge('e1', newE1);
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('v2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('newE1');
    expect(graph.EdgeTypes[0].FromVertexTypeName).toBe('v2');
    expect(graph.EdgeTypes[0].ToVertexTypeName).toBe('v1');

    const newV2 = v2.clone();
    newV2.name = 'newV2';
    service.updateVertex('v2', newV2);
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('newV2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('newE1');
    expect(graph.EdgeTypes[0].FromVertexTypeName).toBe('newV2');
    expect(graph.EdgeTypes[0].ToVertexTypeName).toBe('v1');

    service.undo();
    graph = service.getGSQLGraphJson();
    expect(graph.VertexTypes.length).toBe(2);
    expect(graph.VertexTypes[0].Name).toBe('v1');
    expect(graph.VertexTypes[1].Name).toBe('v2');
    expect(graph.EdgeTypes.length).toBe(1);
    expect(graph.EdgeTypes[0].Name).toBe('newE1');
    expect(graph.EdgeTypes[0].FromVertexTypeName).toBe('v2');
    expect(graph.EdgeTypes[0].ToVertexTypeName).toBe('v1');
  });

  it('should get correct vertex and edge from graph given type names',
  () => {

    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);

    expect(service.getVertex('v2').name).toBe('v2');
    expect(service.getEdge('e2').name).toBe('e2');
    expect(service.getVertex('not_exist')).toBeUndefined();
    expect(service.getEdge('not_exist')).toBeUndefined();
  });

  it('should get correct vertex and edge type names',
  () => {

    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);

    expect(service.getAllVertexTypes().length).toBe(3);
    expect(service.getAllVertexTypes()[0]).toBe('v1');
    expect(service.getAllVertexTypes()[1]).toBe('v2');
    expect(service.getAllVertexTypes()[2]).toBe('v3');
    expect(service.getAllEdgeTypes().length).toBe(2);
    expect(service.getAllEdgeTypes()[0]).toBe('e1');
    expect(service.getAllEdgeTypes()[1]).toBe('e2');
  });

  it('should support redo and undo correctly after delete more than one vertices and edges',
  () => {

    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);

    expect(service.getAllVertexTypes().length).toBe(3);
    expect(service.getAllEdgeTypes().length).toBe(2);

    service.remove(['v3', 'v1'], [{ from: 'v2', to: 'v2', type: 'e2' }]);
    expect(service.getAllVertexTypes().length).toBe(1);
    expect(service.getAllVertexTypes()[0]).toBe('v2');
    expect(service.getAllEdgeTypes().length).toBe(0);

    service.undo();
    expect(service.getAllVertexTypes().length).toBe(3);
    expect(service.getAllVertexTypes()[0]).toBe('v1');
    expect(service.getAllVertexTypes()[1]).toBe('v2');
    expect(service.getAllVertexTypes()[2]).toBe('v3');
    expect(service.getAllEdgeTypes().length).toBe(2);
    expect(service.getAllEdgeTypes()[0]).toBe('e1');
    expect(service.getAllEdgeTypes()[1]).toBe('e2');

    service.redo();
    expect(service.getAllVertexTypes().length).toBe(1);
    expect(service.getAllVertexTypes()[0]).toBe('v2');
    expect(service.getAllEdgeTypes().length).toBe(0);

    service.undo();
    service.remove(['v3', 'v1'], [{ from: 'v2', to: 'v2', type: 'e1' }]);
    expect(service.getAllVertexTypes().length).toBe(1);
    expect(service.getAllVertexTypes()[0]).toBe('v2');
    expect(service.getAllEdgeTypes().length).toBe(1);
    expect(service.getAllEdgeTypes()[0]).toBe('e2');
  });

  it('should support update vertex style, vertex position and vertex icon correctly',
  () => {

    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);

    const vertexStyle = new VertexStyle();
    vertexStyle.fillColor = '#123321';
    vertexStyle.x = 2;
    vertexStyle.y = 1;
    vertexStyle.other = {};
    const vis1 = service.updateVertexStyle('v1', vertexStyle);
    expect(vis1.nodes[0].style.fillColor).toBe('#123321');
    expect(vis1.nodes[0].style.x).toBe(2);
    expect(vis1.nodes[0].style.y).toBe(1);
    expect(vis1.nodes[0].style.icon).toBeUndefined();

    const vStyle = new VertexStyle();
    vStyle.x = -1;
    vStyle.y = -4;
    vStyle.fillColor = '#111111';
    vStyle.other = {};
    service.updateVertexStyle('v1', vStyle);
    const vis7 = service.updateVerticesPositions([['v1', 10, 15], ['v2', 1, 2]]);
    expect(vis7.nodes[0].style.x).toBe(10);
    expect(vis7.nodes[0].style.y).toBe(15);
    expect(vis7.nodes[1].style.x).toBe(1);
    expect(vis7.nodes[1].style.y).toBe(2);
  });

  it(`should update edge's from and to vertex name when update vertex name correctly`,
  () => {
    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);
    const vertex1 = service.getVertex('v1');
    vertex1.name = 'newName1';
    service.updateVertex('v1', vertex1);
    const vertex2 = service.getVertex('v2');
    vertex2.name = 'newName2';
    service.updateVertex('v2', vertex2);
    const edge = service.getEdge('e1');
    expect(edge.fromToVertexTypePairs[0].from).toBe('newName1');
    expect(edge.fromToVertexTypePairs[0].to).toBe('newName2');
  });

  it(`should update edge correctly`,
  () => {
    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);
    // Add another edge e2 to graph
    const edge = service.getEdge('e1');
    edge.name = 'e2';
    service.addEdge(edge);
    // Update edge e2
    const e2 = service.getEdge('e2');
    e2.name = 'newEdgeName';
    service.updateEdge('e2', e2);
    expect(service.getEdge('e2')).toBeUndefined();
    expect(service.getEdge('newEdgeName')).toBeDefined();
    expect(service.getAllEdgeTypes().length).toBe(2);
  });

  // it('should directly return visualization result if vertex style is undefined',
  // () => {
  //   service.setSchemaAndStyle(mockGraph2, mockGraphStyle);
  //   service.graph.getVertex('v1').style = undefined;
  //   const graphVis = service.updateVertexIcon('v1', 'test.icon');
  //   expect(graphVis.nodes[0].style).toBeUndefined();
  // });

  it('should support update edge stroke color correctly',
  () => {

    service.setSchemaAndStyle(mockGraph2, mockGraphStyle);

    const vis1 = service.updateEdgeStrokeColor('e1', '#acacac');
    expect(vis1.links[0].style.fillColor).toBe('#acacac');

    const edge = service.getEdge('e1');
    edge.style = undefined;
    service.updateEdge('e1', edge);
    expect(service.getGraphVis().links[0].style).toBeUndefined();
    const vis2 = service.updateEdgeStrokeColor('e1', '#acacac');
    expect(vis1.links[0].style.fillColor).toBe('#acacac');
  });

  it('should report issue if vertex or edge or reverse edge name is duplicated with another GSQL object',
  () => {
    service.setSchemaAndStyle(undefined, undefined);

    const v1 = new Vertex();
    v1.name = 'v1';
    v1.primaryId.name = 'id';
    const v2 = new Vertex();
    v2.name = 'v2';
    v2.primaryId.name = 'id';

    const e1 = new Edge();
    e1.name = 'e1';
    e1.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    e1.hasReverseEdge = true;
    e1.reverseEdge = 'rev_e1';
    const e2 = new Edge();
    e2.name = 'e2';
    e2.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    e2.hasReverseEdge = true;
    e2.reverseEdge = 'rev_e2';
    const e3 = new Edge();
    e3.name = 'e3';
    e3.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    e3.hasReverseEdge = true;
    e3.reverseEdge = 'rev_e3';

    service.setGSQLTypeNames({ MyGraph: ['v2', 'e2', 'rev_e1'] });
    expect(service.addVertex(v1).success).toBeTruthy();
    expect(service.addVertex(v2).success).toBeFalsy();
    expect(service.addVertex(v2).message).toBe('Vertex name "v2" is used by another GSQL object.');
    expect(service.addEdge(e1).success).toBeFalsy();
    expect(service.addEdge(e1).message).toBe('Reverse edge name "rev_e1" is used by another GSQL object.');
    expect(service.addEdge(e2).success).toBeFalsy();
    expect(service.addEdge(e2).message).toBe('Edge name "e2" is used by another GSQL object.');
    expect(service.addEdge(e3).success).toBeTruthy();
  });
});
