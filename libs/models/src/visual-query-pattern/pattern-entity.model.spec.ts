import { PatternEntityJson, PatternEntity } from './pattern-entity.model';

describe('PatternEntityModel', () => {
  it('should get pattern entity as a json type', () => {
    const mockPatternEntityJson: PatternEntityJson = {
      output : false,
      id: '1',
      existentialCheck: false
    };
    const mockPatternEntity = new PatternEntity(mockPatternEntityJson);

    expect(mockPatternEntity.toJson()).toEqual({
      output: false,
      id: '1',
      existentialCheck: false,
      alias: undefined,
      position: {
        x: 0,
        y: 0
      }
    });
  });
});
