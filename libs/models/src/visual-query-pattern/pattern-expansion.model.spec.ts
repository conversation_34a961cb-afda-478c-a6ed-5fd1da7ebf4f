import { PatternExpansionJson, PatternExpansion } from './pattern-expansion.model';
import { ExpansionType } from './pattern-widget.model';

describe('PatternExpansionModel', () => {
  it('should get pattern expansion as a json type', () => {
    const mockPatternExpansionJson: PatternExpansionJson = {
      type: ExpansionType.OneStepExpansion,
      limit: -1,
      repeatAtLeast: 1,
      repeatAtMost: 1,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternExpansion = new PatternExpansion(mockPatternExpansionJson);
    expect(mockPatternExpansion.toJson()).toEqual(<PatternExpansionJson>{
      type: 'oneStepExpansion',
      limit: -1,
      repeatAtLeast: 1,
      repeatAtMost: 1,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1',
      alias: undefined,
      position: {
        x: 0,
        y: 0
      }
    });
  });
});
