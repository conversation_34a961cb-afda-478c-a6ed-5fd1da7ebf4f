import { Pattern<PERSON>ath<PERSON><PERSON>, PatternPath } from './pattern-path.model';
import { PathType } from './pattern-widget.model';

describe('PatternPathModel', () => {
  it('should get path pattern as json type', () => {
    const mockPatternPathJson: PatternPathJson = {
      type: PathType.SingleShortestPath,
      depth: 1,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '10'
    };
    const mockPatternPath = new PatternPath(mockPatternPathJson);
    expect(mockPatternPath.toJson()).toEqual(<PatternPathJson>{
      type: 'singleShortestPath',
      depth: 1,
      id: '10',
      alias: undefined,
      output: false,
      existentialCheck: false,
      position: {
        x: 0,
        y: 0
      },
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6']
    });
  });
});
