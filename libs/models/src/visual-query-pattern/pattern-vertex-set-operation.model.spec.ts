import { PatternVertexSetOperationJson, PatternVertexSetOperation } from './pattern-vertex-set-operation.model';
import { VertexSetOperation } from './pattern-widget.model';

describe('PatternVertexSetOperationModel', () => {
  it('should get vertex set operation pattern as json type', () => {
    const mockPatternVertexSetOperationJson: PatternVertexSetOperationJson = {
      type: VertexSetOperation.Subtract,
      output: false,
      existentialCheck: false,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      id: '1'
    };
    const mockPatternVertexSetOperation
      = new PatternVertexSetOperation(mockPatternVertexSetOperationJson);
    expect(mockPatternVertexSetOperation.toJson()).toEqual(<PatternVertexSetOperationJson>{
      type: 'subtract',
      output: false,
      existentialCheck: false,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      id: '1',
      alias: undefined,
      position: {
        x: 0,
        y: 0
      }
    });
  });
});
