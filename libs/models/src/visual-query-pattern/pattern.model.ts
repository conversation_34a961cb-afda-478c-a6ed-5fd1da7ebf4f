import { uniqWith, intersectionWith, isEqual } from 'lodash';

import { DataType } from '../data';
import { GraphStudioError } from '../error';
import {
  Atom, BaseCondition, BaseExpression, BaseParam,
  BoolParam, ConditionJson, FunctionExpression,
  FunctionType, IntParam, RealParam, StringParam, DatetimeParam,
  parseExprJson, AliasReference, ComparisonCondition, ExpressionValidator, ArithmeticExpression, AttrVariableWithAlias
} from '../expression';
import { Graph } from '../topology';
import { HelperFunctions } from '../utils/helper-functions';
import { ExpressionFormConfig } from './config';

import { PatternAggregation, PatternAggregationJson } from './pattern-aggregation.model';
import { PatternEdgeSet, PatternEdgeSetJson } from './pattern-edge-set.model';
import { PatternEntity } from './pattern-entity.model';
import { PatternOrder<PERSON><PERSON>, PatternOrder<PERSON>y<PERSON><PERSON> } from './pattern-order-by.model';
import { PatternParam } from './pattern-param.model';
import { PatternView, PatternViewJson } from './pattern-view.model';
import { PatternSemanticError } from './pattern-semantic-error.interface';
import { PatternVertexSet, PatternVertexSetJson } from './pattern-vertex-set.model';
import { PatternWidget, PatternWidgetJson, VertexSetOperation } from './pattern-widget.model';
import { PatternWidgetFactory } from './pattern-widget-factory.model';
import { SchemaTriplet, Triplet } from './triplet';
import { defaultCondList, defaultExprList, defaultFuncList, defaultOptList } from '../expression/default-options-list';
import { getAggregationFormConfig, getFilterFormConfig, getOrderByFormConfig } from './config';
import { aliasSchemaAffix } from '../query-builder';

export const versionNumber = '3.3';

// TODO: Keep the list updated for each VQB release.
const patternJsonKeys = [
  'name', 'description',
  'patternVertexSets', 'patternEdgeSets', 'patternWidgets', 'patternViews',
  'version',
  'filters', 'aggregations', 'postAggregationFilters', 'orderBy', 'skip', 'limit',
];

export interface PatternSemanticCheckResult {
  success: boolean;
  errors: PatternSemanticError[];
}

export interface PatternJson {
  name: string;
  description: string;
  patternVertexSets: PatternVertexSetJson[];
  patternEdgeSets: PatternEdgeSetJson[];
  patternWidgets: PatternWidgetJson[];
  patternViews: PatternViewJson[];
  version?: string;

  // Not used.
  filters: ConditionJson[];
  aggregations: PatternAggregationJson[];
  postAggregationFilters: ConditionJson[];
  orderBy: PatternOrderByJson[];
  skip: number;
  limit: number;
}

export class Pattern {
  name: string;
  description: string;
  patternVertexSets: PatternVertexSet[];
  patternEdgeSets: PatternEdgeSet[];
  patternWidgets: PatternWidget[];
  patternViews: PatternView[];
  version: string;

  // Not used.
  filters: BaseCondition[];
  aggregations: PatternAggregation[];
  postAggregationFilters: BaseCondition[];
  orderBy: PatternOrderBy[];
  skip = 0;
  limit = -1;

  constructor(data: PatternJson, schema?: Graph) {
    Object.keys(data).forEach(key => {
      if (!patternJsonKeys.includes(key) && Pattern.checkIfValueHasData(data[key])) {
        throw new GraphStudioError(`Pattern ${data.name} contains unsupported features. ` +
          `Did you import a solution from a higher version of GraphStudio?`);
      }
    });

    this.name = data.name;
    this.description = data.description || '';
    this.patternVertexSets = data.patternVertexSets.map(
      patternVertexSet => new PatternVertexSet(patternVertexSet, schema)
    );
    this.patternEdgeSets = data.patternEdgeSets.map(
      patternEdgeSet => new PatternEdgeSet(patternEdgeSet, schema)
    );
    this.patternWidgets = data.patternWidgets.map(
      patternWidget => new PatternWidgetFactory().getPatternWidget(patternWidget)
    );
    this.patternViews = (data.patternViews || []).map(
      patternView => new PatternView(patternView, schema)
    );
    this.version = data.version || '';

    // Not used.
    this.filters = data.filters.map(filterJson => <BaseCondition>parseExprJson(filterJson, schema));
    this.aggregations = data.aggregations.map(aggJson => new PatternAggregation(aggJson, schema));
    this.postAggregationFilters = data.postAggregationFilters.map(
      postFilterJson => <BaseCondition>parseExprJson(postFilterJson, schema)
    );
    this.orderBy = data.orderBy.map(orderByJson => new PatternOrderBy(orderByJson, schema));
    this.skip = data.skip;
    this.limit = data.limit;
  }

  /**
   * Helper function to check if the value of a patternJson attribute has data.
   * A value has data if it is not null, undefined, an empty list, or an empty object.
   *
   * @param {any} value
   * @return {boolean}
   * @memberof Pattern
   */
   static checkIfValueHasData(value: any): boolean {
    if (value === undefined || value === null) {
      return false;
    }

    if (value instanceof Array) {
      return value.length > 0;
    }
    if (typeof value === 'object') {
      return Object.keys(value).length > 0;
    }

    return true;
  }

  /**
   * Find the component within the pattern by id.
   *
   * @param {string} id
   * @returns {(PatternVertexSet | PatternEdgeSet | PatternWidget)}
   * @memberof Pattern
   */
  findComponentById(id: string): PatternVertexSet | PatternEdgeSet | PatternWidget | PatternView {
    return this.patternVertexSets.find(patternVertex => patternVertex.id === id)
      || this.patternEdgeSets.find(patternEdge => patternEdge.id === id)
      || this.patternWidgets.find(patternWidget => patternWidget.id === id)
      || this.patternViews.find(patternView => patternView.id === id);
  }

  /**
   * Dump the pattern into JSON.
   *
   * @returns {PatternJson}
   * @memberof Pattern
   */
  toJson(): PatternJson {
    return {
      name: this.name,
      description: this.description,
      patternVertexSets: this.patternVertexSets.map(patternVertexSet => patternVertexSet.toJson()),
      patternEdgeSets: this.patternEdgeSets.map(patternEdgeSet => patternEdgeSet.toJson()),
      patternWidgets: this.patternWidgets.map(patternWidget => patternWidget.toJson()),
      patternViews: this.patternViews.map(patternView => patternView.toJson()),
      version: this.version,
      filters: this.filters.map(filter => filter.toJson()),
      aggregations: this.aggregations.map(agg => agg.toJson()),
      postAggregationFilters: this.postAggregationFilters.map(postFilter => postFilter.toJson()),
      orderBy: this.orderBy.map(orderBy => orderBy.toJson()),
      skip: this.skip,
      limit: this.limit,
    };
  }

  /**
   * Generate corresponding GSQL script for the graph pattern.
   * FIXME: use templates.
   *
   * @param {Graph} graph
   * @returns {string}
   * @memberof Pattern
   */
  toGSQL(graph: Graph): string {
    const queryBlocks: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }[] = [];
    const id2Block = new Map<string, number>();
    this.splitPatternIntoBlocks(queryBlocks, id2Block);

    // Store the parameters.
    const paramsDecl = this.collectParameters();

    // Store the tuple definitions.
    const tupleDefs = new Set<string>();
    // Store the accumulator declaration statements.
    const accumulatorsDecl = new Set<string>();

    // block2PatternViewIds[i] is a list of ids of the pattern views on the i-th block.
    const block2PatternViewIds: string[][] = [];
    for (let i = 0; i < queryBlocks.length; i++) {
      block2PatternViewIds[i] = [];
    }
    this.patternViews.forEach(patternView => {
      const blockIndex = id2Block.get(patternView.vertexSetIds[0]);
      block2PatternViewIds[blockIndex].push(patternView.id);
    });

    let queryBody = '';

    // Handle each pattern block.
    queryBlocks.forEach((queryBlock, blockIndex) => {
      // Prepare the vertex set constraints.
      const constrainedVertexPatterns = new Set<string>();
      queryBody += this.prepareVertexSetsInQueryBlock(
        queryBlock, accumulatorsDecl, constrainedVertexPatterns
      );

      // Retrieve all the vertex/edge patterns and pattern views on the current block.
      const vertexPatterns = queryBlock.vertexPatternIds.map(
        pvId => <PatternVertexSet>this.getPatternEntityById(pvId)
      );
      const edgePatterns = queryBlock.edgePatternIds.map(
        peId => <PatternEdgeSet>this.getPatternEntityById(peId)
      );
      const patternViews = block2PatternViewIds[blockIndex]
        .map(patternViewId => <PatternView>this.getPatternEntityById(patternViewId));

      // The vertex patterns that need to be output or is input of another widget
      // needs to be collected.
      const vertexPatternsToBeCollected = vertexPatterns.filter(pv =>
        pv.output || this.patternWidgets.find(pw => pw.inputPatternIds.includes(pv.id))
      );

      // Generate the SELECT clause. We select one vertex pattern with limit.
      // If no pattern has limit, we select one vertex pattern that needs to be output.
      const vertexPatternToBeSelected = vertexPatterns.find(pv => pv.hasLimit)
        || vertexPatternsToBeCollected[0]
        || vertexPatterns[0];
      const selectClause = `  VertexSet_${vertexPatternToBeSelected.id} = \n        `
        + `SELECT ${this.getPatternComponentAlias(vertexPatternToBeSelected)}\n`;

      // Generate the FROM clause.
      const fromClause = this.generateFromClause(queryBlock, graph, constrainedVertexPatterns);

      // Generate the WHERE clause.
      const whereClause = this.generateWhereClause(queryBlock);

      // Generate the ACCUM statements that will be used to assemble the clauses later.
      const accumStatements = this.generateAccumStatements(queryBlock, accumulatorsDecl);
      // Only accumulate the pattern view matching results on the final SELECT FROM clause of one block.
      const patternViewAccumStmts: string[] = [];

      // Handle pattern views.
      let patternViewPostProcessingStmts = '';
      patternViews.forEach(patternView => {
        const tupleFields: {
          baseTypes: string[],
          fieldNames: string[]
        } = {
          baseTypes: [],
          fieldNames: []
        };
        const vertexSetForLoopStmts: string[] = [];
        const vertexSetOutputStmts: string[] = [];

        // Handle each vertex set in the pattern view.
        patternView.vertexSetIds.forEach(vertexSetId => {
          const vertexSet = <PatternVertexSet>(this.getPatternEntityById(vertexSetId));
          const vertexSetAlias = this.getPatternComponentAlias(vertexSet);

          // Collect the vertex set info for pattern view tuple definition.
          // TODO: mark support for multi-type.
          tupleFields.baseTypes.push(
            `vertex<${vertexSet.vertices[0]}> ${vertexSetAlias}`
          );
          tupleFields.fieldNames.push(vertexSetAlias);

          // Define a set accum for the vertex set.
          accumulatorsDecl.add(
            `SetAccum<vertex<${vertexSet.vertices[0]}>> @@${patternView.alias}_${vertexSetAlias}`
          );

          // Collect the vertex set matching results from the pattern view matching results.
          vertexSetForLoopStmts.push(
            `        @@${patternView.alias}_${vertexSetAlias} += result.${vertexSetAlias};\n`
          );

          // Print the vertex set matching results.
          if (patternView.output) {
            vertexSetOutputStmts.push(
              `  ${patternView.alias}_${vertexSetAlias} = { @@${patternView.alias}_${vertexSetAlias} };\n`
              + `  ${this.getPrintStatement(vertexSet, `${patternView.alias}_${vertexSetAlias}`, graph, patternView.alias)}\n\n`
            );
          }
        });

        // Handle each edge set in the pattern view.
        patternView.edgeSetIds.forEach((edgeSetId) => {
          const edgeSet = <PatternEdgeSet>this.getPatternEntityById(edgeSetId);
          // Edge with kleen start cannot be collected.
          if (edgeSet.repeatAtLeast !== 1 || edgeSet.repeatAtMost !== 1) {
            return;
          }

          const edgeSetAlias = this.getPatternComponentAlias(edgeSet);

          // Collect the edge set info for pattern view tuple definition.
          tupleFields.baseTypes.push(`edge<${edgeSet.edges[0]}> ${edgeSetAlias}`);
          tupleFields.fieldNames.push(edgeSetAlias);
        });

        // If the pattern view has limit, dump the HeapAccum to the Result_PatternViewAlias.
        if (patternView.hasLimit) {
          // Convert the HeapAccum into SetAccum.
          patternViewPostProcessingStmts +=
            `  WHILE (@@OrderHeap_${patternView.alias}.size() > 0) DO\n` +
            `    @@Result_${patternView.alias} += Tuple_${patternView.alias}(${tupleFields.fieldNames
              .map((field) => `@@OrderHeap_${patternView.alias}.top().${field}`)
              .join(', ')});\n` +
            `    @@OrderHeap_${patternView.alias}.pop();\n` +
            `  END;\n`;
        }

        if (patternView.output) {
          patternViewPostProcessingStmts += `  ${this.getPrintStatement(
            patternView, `@@Result_${patternView.alias}`, graph
          )}\n\n`;
        }

        patternViewPostProcessingStmts +=
          `  FOREACH result IN @@Result_${patternView.alias} DO\n`
          + vertexSetForLoopStmts.join('')
          + `  END;\n\n`
          + vertexSetOutputStmts.join('');

        // With the info collected from all the vertex/edge sets, define a tuple for the pattern view.
        tupleDefs.add(
          `TYPEDEF TUPLE<${tupleFields.baseTypes.join(', ')}> Tuple_${patternView.alias}`
        );
         // If the pattern view has limit, use another HeapAccum to collect top tuples during traversal.
        if (patternView.hasLimit) {
          const heapTupleFields: {
            baseTypes: string[];
            fieldNames: string[];
          } = {
            baseTypes: [...tupleFields.baseTypes],
            fieldNames: [...tupleFields.fieldNames],
          };
          const heapOrders: string[] = [];
          if (patternView.orderBy.length === 0) {
            heapTupleFields.baseTypes.push('int f0');
            heapTupleFields.fieldNames.push('f0');
            heapOrders.push('f0 ASC');
          } else {
            patternView.orderBy.forEach((orderBy, i) => {
              heapTupleFields.baseTypes.push(`${this.getExprDataType(orderBy.expression)} f${i}`);
              heapTupleFields.fieldNames.push(`f${i}`);
              heapOrders.push(`f${i} ${orderBy.asc ? 'ASC' : 'DESC'}`);
            });
          }
          tupleDefs.add(`TYPEDEF TUPLE<${heapTupleFields.baseTypes.join(', ')}> OrderTuple_${patternView.alias}`);
          accumulatorsDecl.add(
            `HeapAccum<OrderTuple_${patternView.alias}>(${patternView.limit}, ` +
              `${heapOrders.join(', ')}) @@OrderHeap_${patternView.alias}`
          );
        }
        // Define a set accumulator to collect the pattern view tuple.
        accumulatorsDecl.add(
          `SetAccum<Tuple_${patternView.alias}> @@Result_${patternView.alias}`
        );
        // Collect pattern view results in the ACCUM statements.
        if (patternView.hasLimit) {
          // TODO: handle pattern view aggregation by extending this.getOrderByPureExpression
          const orderExprList = patternView.orderBy.map((orderBy) =>
            this.removeParamParenthesis(orderBy.expression.toString())
          );
          if (orderExprList.length === 0) {
            orderExprList.push('1');
          }
          patternViewAccumStmts.push(
            `@@OrderHeap_${patternView.alias} += OrderTuple_${patternView.alias}(${tupleFields.fieldNames.join(
              ', '
            )}, ${orderExprList.join(', ')})`
          );
        } else {
          patternViewAccumStmts.push(
            `@@Result_${patternView.alias} += Tuple_${patternView.alias}(${tupleFields.fieldNames.join(', ')})`
          );
        }
      });

      if (
        vertexPatterns.filter(pv => pv.hasLimit).length === 0
        && vertexPatternsToBeCollected.length <= 1
      ) {
        // Case 1. No vertex pattern has limit, at most one vertex pattern needs to be collected.
        // Only need one query block in this case.

        // Collect the edge sets.
        edgePatterns.filter(edgePattern => edgePattern.output).forEach(edgePattern => {
          accumulatorsDecl.add(`SetAccum<edge> @@EdgeSet_${edgePattern.id}`);
          accumStatements.accumStmts.push(
            `@@EdgeSet_${edgePattern.id} += ${this.getPatternComponentAlias(edgePattern)}`
          );
        });

        // Can skip the query block if the query block is ordinary,
        // and the vertex pattern is constrained by a previous block.
        if (
          !this.isOrdinaryPattern(queryBlock) ||
          !constrainedVertexPatterns.has(vertexPatternToBeSelected.id)
        ) {

          accumStatements.accumStmts.push(...patternViewAccumStmts);

          // Get the query block.
          queryBody += this.getQueryBlock(
            selectClause, fromClause, whereClause, accumStatements, vertexPatternToBeSelected
          ) + '\n';

          // Output the edge sets.
          edgePatterns.filter(edgePattern => edgePattern.output).forEach(edgePattern => {
            queryBody += `  ${this.getPrintStatement(
              edgePattern, `@@EdgeSet_${edgePattern.id}`, graph
            )}\n`;
          });
        }

        // Output the selected vertex set if needed.
        if (vertexPatternToBeSelected.output) {
          queryBody += `\n  ${this.getPrintStatement(
            vertexPatternToBeSelected, `VertexSet_${vertexPatternToBeSelected.id}`, graph
          )}\n\n`;
        }
      } else if (
        vertexPatterns.filter(pv =>
          vertexPatternsToBeCollected.includes(pv) || pv.hasLimit
        ).length <= 1
        &&
        edgePatterns.filter(pe => pe.output).length === 0
        &&
        patternViews.length === 0
      ) {
        // Case 2. If order-by and limit only happens on the selected vertex pattern,
        // no edge pattern is output, and no pattern views are involved in the connected component,
        // then use one query block to finish the logic.

        accumStatements.accumStmts.push(...patternViewAccumStmts);

        // Get the query block.
        queryBody += this.getQueryBlock(
          selectClause, fromClause, whereClause, accumStatements, vertexPatternToBeSelected
        ) + '\n';

        // Output the selected vertex set if needed.
        if (vertexPatternToBeSelected.output) {
          queryBody += `\n  ${this.getPrintStatement(
            vertexPatternToBeSelected, `VertexSet_${vertexPatternToBeSelected.id}`, graph
          )}\n\n`;
        }
      } else {
        // Case 3. If accum and post-accum is not empty, need 2 query blocks.
        // Store all selected vertex aliases and edge aliases with limit to a HeapAccum,
        // then collect the vertices or edges in the HeapAccum to a SetAccum.
        // Add a 2nd query block. Re-generate a FROM clause with the new constraints.
        // Use the SetAccum to constrain corresponding vertex sets and select edge sets,
        // use new vertex blocks to convert SetAccum<vertex> to vertex sets and print them.

        vertexPatterns.filter(vertexPattern => vertexPattern.hasLimit).forEach(vertexPattern => {
          const tupleFields: string[] = [`vertex<${vertexPattern.vertices[0]}> v`];
          const heapOrders: string[] = [];
          if (vertexPattern.orderBy.length === 0) {
            tupleFields.push('int f0');
            heapOrders.push('f0 ASC');
          } else {
            vertexPattern.orderBy.forEach((orderBy, i) => {
              tupleFields.push(`${this.getExprDataType(orderBy.expression)} f${i}`);
              heapOrders.push(`f${i} ${orderBy.asc ? 'ASC' : 'DESC'}`);
            });
          }
          tupleDefs.add(
            `TYPEDEF TUPLE<${tupleFields.join(', ')}> OrderTuple_${vertexPattern.id}`
          );
          accumulatorsDecl.add(
            `HeapAccum<OrderTuple_${vertexPattern.id}>(${vertexPattern.limit}, ` +
            `${heapOrders.join(', ')}) @@OrderHeap_${vertexPattern.id}`
          );
          accumulatorsDecl.add(
            `SetAccum<vertex<${vertexPattern.vertices[0]}>> @@OrderVertexSet_${vertexPattern.id}`
          );

          // Add one post-accum clause to select the top vertices.
          const orderExprList = vertexPattern.orderBy.map(
            orderBy => this.getOrderByPureExpression(vertexPattern, orderBy)
          );
          if (orderExprList.length === 0) {
            orderExprList.push('1');
          }
          const alias = this.getPatternComponentAlias(vertexPattern);
          const stmts = accumStatements.postAccumStmts.get(alias) || [];
          stmts.push(`@@OrderHeap_${vertexPattern.id} += OrderTuple_${vertexPattern.id}(${
            alias}, ${orderExprList.join(', ')})`);
          accumStatements.postAccumStmts.set(alias, stmts);
        });

        if (accumStatements.accumStmts.length + accumStatements.postAccumStmts.size > 0) {
          // Generate the 1st query block.
          queryBody += this.getQueryBlock(
            selectClause, fromClause, whereClause,
            accumStatements, vertexPatternToBeSelected, true
          ) + '\n';
        }

        vertexPatterns.filter(vertexPattern => vertexPattern.hasLimit).forEach(vertexPattern => {
          // Convert the HeapAccum into SetAccum.
          queryBody += `  WHILE (@@OrderHeap_${vertexPattern.id}.size() > 0) DO\n` +
          `    @@OrderVertexSet_${vertexPattern.id} += @@OrderHeap_${vertexPattern.id}.pop().v;\n` +
          `  END;\n`;
          // Activate the VertexSet from the SetAccum.
          queryBody +=
            `  VertexSet_${vertexPattern.id} = { @@OrderVertexSet_${vertexPattern.id} };\n\n`;
          // Add to constrained list.
          constrainedVertexPatterns.add(vertexPattern.id);
        });

        // Re-generate the FROM clause.
        const newFromClause = this.generateFromClause(queryBlock, graph, constrainedVertexPatterns);

        // Use ACCUM clause to collect the edge sets.
        // Use POST-ACCUM clause to collect the vertex sets.
        const newAccumStatements: {
          accumStmts: string[],
          postAccumStmts: Map<string, string[]>
        } = {
          accumStmts: [],
          postAccumStmts: new Map<string, string[]>()
        };
        edgePatterns.filter(edgePattern => edgePattern.output).forEach(edgePattern => {
          accumulatorsDecl.add(`SetAccum<edge> @@FinalEdgeSet_${edgePattern.id}`);
          newAccumStatements.accumStmts.push(
            `@@FinalEdgeSet_${edgePattern.id} += ${this.getPatternComponentAlias(edgePattern)}`
          );
        });
        vertexPatternsToBeCollected.forEach(vertexPattern => {
          accumulatorsDecl.add(
            `SetAccum<vertex<${vertexPattern.vertices[0]}>> @@FinalVertexSet_${vertexPattern.id}`
          );
          const alias = this.getPatternComponentAlias(vertexPattern);
          const stmts = newAccumStatements.postAccumStmts.get(alias) || [];
          stmts.push(`@@FinalVertexSet_${vertexPattern.id} += ${alias}`);
          newAccumStatements.postAccumStmts.set(alias, stmts);
        });

        newAccumStatements.accumStmts.push(...patternViewAccumStmts);

        // Assemble the 2nd query block.
        queryBody += this.getQueryBlock(
          selectClause, newFromClause, whereClause,
          newAccumStatements, vertexPatternToBeSelected, true
        ) + '\n';

        // Print the edge patterns.
        edgePatterns.filter(edgePattern => edgePattern.output).forEach(edgePattern => {
          accumulatorsDecl.add(`SetAccum<edge> @@FinalEdgeSet_${edgePattern.id}`);
          newAccumStatements.accumStmts.push(
            `@@FinalEdgeSet_${edgePattern.id} += ${this.getPatternComponentAlias(edgePattern)}`
          );
          queryBody += `  ${this.getPrintStatement(
            edgePattern, `@@FinalEdgeSet_${edgePattern.id}`, graph
          )}\n\n`;
        });

        // Convert the collected SetAccum<vertex> to vertex sets.
        vertexPatternsToBeCollected.forEach(vertexPattern => {
          queryBody += `  VertexSet_${vertexPattern.id} = { @@FinalVertexSet_${vertexPattern.id} };\n`;
          // Output the selected vertex set if needed.
          if (vertexPattern.output) {
            queryBody += `  ${this.getPrintStatement(
              vertexPattern, `VertexSet_${vertexPattern.id}`, graph
            )}\n\n`;
          }
        });
      }

      // Pattern view must have at least one SELECT FROM clause.
      if (this.patternViews.length !== 0 && !queryBody.includes('SELECT')) {
        accumStatements.accumStmts.push(...patternViewAccumStmts);

        // Assemble the query block.
        queryBody += this.getQueryBlock(
          selectClause, fromClause, whereClause,
          accumStatements, vertexPatternToBeSelected
        ) + '\n';
      }

      queryBody += patternViewPostProcessingStmts;
    });

    // Assemble the query.
    const result = `CREATE QUERY ${this.name}(${paramsDecl.map(param => param.toString()).join(', ')}) ` +
      `FOR GRAPH ${graph.name} API("v2") SYNTAX v2 {\n` +
      (this.description ? `/**\n${this.description.replace(new RegExp(`\/`, 'g'), '')}\n*/\n` : '') +
      Array.from(tupleDefs).map(def => `  ${def};\n`).join('') + '\n' +
      Array.from(accumulatorsDecl).map(decl => `  ${decl};\n`).join('') + '\n' +
      queryBody +
    '}\n';

    // Replace auto-generated components ids with a counter to make the query human-readable.
    return this.makeQueryHumanReadable(result);
  }

  /**
   * Split the pattern into blocks and get their dependencies.
   * block: a connected component of the visual pattern.
   * id2Block: the block id each vertex component and edge component belongs to.
   * For a widget that has input in block 1 and output in block 2,
   * - block 1 is block 2's upper dependency.
   * - block 2 is block 1's lower dependency.
   *
   * @param {{
   *       vertexPatternIds: string[],
   *       edgePatternIds: string[]
   *     }[]} blocks
   * @param {Map<string, number>} id2Block
   * @param {Set<number>[]} upperDependencies
   * @param {Set<number>[]} lowerDependencies
   * @memberof Pattern
   */
  splitPatternIntoBlocksAndGetDependencies(
    blocks: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }[],
    id2Block: Map<string, number>,
    upperDependencies: Set<number>[],
    lowerDependencies: Set<number>[]
  ) {
    // Convert pattern to map<vid -> list<eid>>
    const vid2eidList = new Map<string, string[]>();
    this.patternEdgeSets.forEach(pe => {
      const peSrcList = vid2eidList.get(pe.sourcePatternId) || [];
      const peTgtList = vid2eidList.get(pe.targetPatternId) || [];
      peSrcList.push(pe.id);
      if (peTgtList !== peSrcList) {
        peTgtList.push(pe.id);
      }
      vid2eidList.set(pe.sourcePatternId, peSrcList);
      vid2eidList.set(pe.targetPatternId, peTgtList);
    });
    // Holds which vertex and edge patterns have been visited.
    const visited = new Set<string>();
    // Use BFS to seperate the pattern into blocks.
    this.patternVertexSets.forEach(pv => {
      if (!visited.has(pv.id)) {
        const block = {
          vertexPatternIds: [pv.id],
          edgePatternIds: []
        };
        visited.add(pv.id);
        for (let i = 0; i < block.vertexPatternIds.length; i++) {
          const peList = vid2eidList.get(block.vertexPatternIds[i]) || [];
          peList.forEach(peId => {
            const pe = <PatternEdgeSet>this.getPatternEntityById(peId);
            if (!visited.has(pe.sourcePatternId)) {
              visited.add(pe.sourcePatternId);
              block.vertexPatternIds.push(pe.sourcePatternId);
            }
            if (!visited.has(pe.targetPatternId)) {
              visited.add(pe.targetPatternId);
              block.vertexPatternIds.push(pe.targetPatternId);
            }
            if (!visited.has(pe.id)) {
              visited.add(pe.id);
              block.edgePatternIds.push(pe.id);
            }
          });
        }
        blocks.push(block);
        block.vertexPatternIds.forEach(pvId => id2Block.set(pvId, blocks.length - 1));
      }
    });
    // Construct dependency graph between blocks.
    for (let i = 0; i < blocks.length; i++) {
      upperDependencies.push(new Set());
      lowerDependencies.push(new Set());
    }
    this.patternWidgets.forEach(pw => {
      pw.inputPatternIds.forEach(inputPid => {
        pw.outputPatternIds.forEach(outputPid => {
          upperDependencies[id2Block.get(outputPid)].add(id2Block.get(inputPid));
          lowerDependencies[id2Block.get(inputPid)].add(id2Block.get(outputPid));
        });
      });
    });
  }

  /**
   * Collect parameters in the pattern.
   *
   * @returns {string[]}
   * @memberof Pattern
   */
  collectParameters(): PatternParam[] {
    const paramsDecl: PatternParam[] = [];

    // Collect the vertex set parameters.
    this.patternVertexSets.forEach(patternVertex => {
      const vertexType = patternVertex.vertices[0];
      if (patternVertex.isParam) {
        paramsDecl.push(new PatternParam(patternVertex.paramName, true, 'vertex', vertexType));
      }
    });

    // Collect the parameters in the expressions in vertex patterns.
    this.patternVertexSets.forEach(vertexPattern => {
      vertexPattern.filters.forEach(
        filter => paramsDecl.push(...this.collectExprParams(filter))
      );
      vertexPattern.aggregations.forEach(
        agg => paramsDecl.push(...this.collectExprParams(agg.expression))
      );
      vertexPattern.orderBy.forEach(
        orderBy => paramsDecl.push(...this.collectExprParams(orderBy.expression))
      );
    });

    // Collect the parameters in the expressions in edge patterns.
    this.patternEdgeSets.forEach(edgePattern => {
      edgePattern.filters.forEach(
        filter => paramsDecl.push(...this.collectExprParams(filter))
      );
    });

    // Collect the parameters in the expressions in the pattern views.
    this.patternViews.forEach(patternView => {
      patternView.filters.forEach(
        filter => paramsDecl.push(...this.collectExprParams(filter))
      );
      // TODO: Collect the parameters in the attachment in the future.
      patternView.orderBy.forEach(
        orderBy => paramsDecl.push(...this.collectExprParams(orderBy.expression))
      );
    });

    // De-duplicate the parameters.
    return uniqWith(paramsDecl, (p1, p2) => p1.toString() === p2.toString());
  }

  private checkVertexComparedWithEdge(filter: BaseCondition,
    vertexAliases: string[], edgeAliases: string[]
  ): {
    error: boolean,
    message: string
  } {
    if (filter instanceof ComparisonCondition) {
      if (ExpressionValidator.isVertexOrEdge(filter.operands[0].getType()) &&
        ExpressionValidator.isVertexOrEdge(filter.operands[1].getType())) {
        const alias1 = (filter.operands[0] as Atom).value;
        const alias2 = (filter.operands[1] as Atom).value;
        if (
          (vertexAliases.includes(alias1.toString()) &&
            edgeAliases.includes(alias2.toString())) ||
          (edgeAliases.includes(alias1.toString()) &&
            vertexAliases.includes(alias2.toString()))
        ) {
          return {
            error: true,
            message: `${filter.toString()} is not valid. You can not compare a vertex and an edge.`
          };
        }
      }
    }
    for (const operand of filter.operands) {
      if (operand instanceof BaseCondition) {
        const result = this.checkVertexComparedWithEdge(operand, vertexAliases, edgeAliases);
        if (result.error) {
          return result;
        }
      }
    }
    return {
      error: false,
      message: ''
    };
  }

  /**
   * Get a query block giving the clauses.
   *
   * @private
   * @param {string} selectClause
   * @param {string} fromClause
   * @param {string} whereClause
   * @param {{
   *       accumStmts: string[],
   *       postAccumStmts: Map<string, string[]>
   *     }} accumStatements
   * @param {PatternVertexSet} vertexPatternToBeSelected
   * @returns {string}
   * @memberof Pattern
   */
  private getQueryBlock(
    selectClause: string,
    fromClause: string,
    whereClause: string,
    accumStatements: {
      accumStmts: string[],
      postAccumStmts: Map<string, string[]>
    },
    vertexPatternToBeSelected: PatternVertexSet,
    skipOrderByLimit: boolean = false
  ): string {
    const accumClause = accumStatements.accumStmts.length > 0
      ? `        ACCUM ${accumStatements.accumStmts.join(',\n              ')}\n`
      : '';
    let postAccumClause = '';
    accumStatements.postAccumStmts.forEach((exprList, _) => {
      postAccumClause += `        POST-ACCUM ${exprList.join(',\n                   ')}\n`;
    });

    let orderByClause = '';
    let limitClause = '';

    if (!skipOrderByLimit) {
      orderByClause = vertexPatternToBeSelected.orderBy.length > 0
        ? '        ORDER BY ' : '';
      orderByClause += vertexPatternToBeSelected.orderBy.map(orderBy =>
        this.getOrderByExpression(vertexPatternToBeSelected, orderBy)
      ).join(',\n              ');
      orderByClause += orderByClause ? '\n' : '';
      limitClause = vertexPatternToBeSelected.hasLimit
        ? `        LIMIT ${vertexPatternToBeSelected.limit}\n`
        : '';
    }

    return selectClause + fromClause + whereClause
      + accumClause + postAccumClause
      + orderByClause + limitClause + '        ;\n';
  }

  /**
   * Get an order-by expression.
   *
   * @private
   * @param {PatternVertexSet} vertexPatternToBeSelected
   * @param {PatternOrderBy} orderBy
   * @returns {string}
   * @memberof Pattern
   */
  private getOrderByExpression(
    vertexPatternToBeSelected: PatternVertexSet,
    orderBy: PatternOrderBy,
  ): string {
    const expression = this.getOrderByPureExpression(vertexPatternToBeSelected, orderBy);
    return `${expression} ${orderBy.asc ? 'ASC' : 'DESC'}`;
  }

  /**
   * Get an order-by expression with no ASC|DESC.
   *
   * @private
   * @param {PatternVertexSet} vertexPatternToBeSelected
   * @param {PatternOrderBy} orderBy
   * @returns {string}
   * @memberof Pattern
   */
  private getOrderByPureExpression(
    vertexPatternToBeSelected: PatternVertexSet,
    orderBy: PatternOrderBy,
  ): string {
    const alias = this.getPatternComponentAlias(vertexPatternToBeSelected);
    let expression = this.removeParamParenthesis(orderBy.expression.toString());

    // Replace the accumulator expressions with corresponding accumulators.
    vertexPatternToBeSelected.aggregations.forEach(agg => {
      expression = expression.replace(
        new RegExp(`(\\W|^)(${alias}\\.${agg.name})(\\W|$)`, 'g'),
        (_, p1, __, p3) => `${p1}${alias}.@Agg_${vertexPatternToBeSelected.id}_${agg.name}${p3}`
      );
    });
    return expression;
  }

  /**
   * A block only contain one vertex pattern with
   * no filter, aggregation, order-by or limit is ordinary.
   *
   * @private
   * @param {{
   *     vertexPatternIds: string[],
   *     edgePatternIds: string[]
   *   }} block
   * @returns {boolean}
   * @memberof Pattern
   */
  private isOrdinaryPattern(block: {
    vertexPatternIds: string[],
    edgePatternIds: string[]
  }): boolean {
    if (block.vertexPatternIds.length > 1 || block.edgePatternIds.length > 0) {
      return false;
    }
    const vertexPattern = <PatternVertexSet>this.getPatternEntityById(block.vertexPatternIds[0]);
    if (vertexPattern.filters.length > 0) {
      return false;
    }
    if (vertexPattern.aggregations.length > 0) {
      return false;
    }
    if (vertexPattern.orderBy.length > 0) {
      return false;
    }
    if (vertexPattern.hasLimit) {
      return false;
    }
    return true;
  }

  /**
   * Get the print statement.
   *
   * @private
   * @param {PatternVertexSet | PatternEdgeSet | PatternView} entity
   * @param {String} entityName
   * @param {Graph} graph
   * @param {String} aliasPrefix
   * @returns {string}
   * @memberof Pattern
   */
  private getPrintStatement(
    entity: PatternVertexSet | PatternEdgeSet | PatternView,
    entityName: String,
    graph: Graph,
    aliasPrefix?: String
  ): string {
    let printStatement = `PRINT ${entityName}`;

    if (entity instanceof PatternVertexSet) {
      const vertex = graph.getVertex(entity.vertices[0]);
      const aggregations = entity.aggregations;

      if (vertex.attributes.length + aggregations.length === 0) {
        printStatement += `["" as no_attributes]`;
      } else {
        const items = vertex.attributes
          .map(attr => `${entityName}.${attr.name} AS ${attr.name}`)
          .concat(aggregations.map(agg =>
            `${entityName}.@Agg_${entity.id}_${agg.name} AS ${agg.name}`)
        );
        printStatement += `[\n    ${items.join(',\n    ')}\n  ]`;
      }
    }

    if (entity.alias) {
      printStatement += ` AS `;
      if (aliasPrefix) {
        printStatement += aliasPrefix + '_';
      }
      printStatement += this.getPatternComponentAlias(entity);
    }
    return printStatement + `;`;
  }

  /**
   * Split the pattern into blocks. The order of the blocks follow the widget
   * computation dependencies. If there is circular dependency, the block seperation
   * will fail. The seperation result will be put in the input parameters.
   *
   * @private
   * @param {{
   *     vertexPatternIds: string[],
   *     edgePatternIds: string[]
   *   }[]} blocks splited blocks
   * @param {Map<string, number>} id2Block map from each pattern component's id to block index
   * @returns {boolean}
   * @memberof Pattern
   */
  private splitPatternIntoBlocks(
    blocks: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }[],
    id2Block: Map<string, number>
  ): boolean {
    const tempBlocks: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }[] = [];
    const tempId2Block = new Map<string, number>();
    // upperDependencies[i] stores the block indices of tempBlocks[i]'s upper dependency.
    const upperDependencies: Set<number>[] = [];
    // lowerDependencies[i] stores the block indices of tempBlocks[i]'s lower dependency.
    const lowerDependencies: Set<number>[] = [];
    // Use BFS to seperate the pattern into blocks and construct dependency graph between blocks.
    this.splitPatternIntoBlocksAndGetDependencies(
      tempBlocks, tempId2Block, upperDependencies, lowerDependencies
    );

    // Use topology sort to detect circular dependency.
    const blockIndices: number[] = [];
    upperDependencies.forEach((dep, i) => {
      if (dep.size === 0) {
        blockIndices.push(i);
      }
    });
    for (let i = 0; i < blockIndices.length; i++) {
      lowerDependencies[blockIndices[i]].forEach(bid => {
        upperDependencies[bid].delete(blockIndices[i]);
        if (upperDependencies[bid].size === 0) {
          blockIndices.push(bid);
        }
      });
    }

    if (blockIndices.length < tempBlocks.length) {
      return false;
    }

    // Put the blocks into the result.
    blockIndices.forEach(bid => blocks.push(tempBlocks[bid]));
    blocks.forEach((block, i) => {
      block.vertexPatternIds.forEach(vid => id2Block.set(vid, i));
      block.edgePatternIds.forEach(eid => id2Block.set(eid, i));
    });

    return true;
  }

  /**
   * Prepare vertex sets for one query block.
   * 1. If the vertex set has constraint from a parameter, collect the parameter, and generate code:
   *   create ... [QueryName](..., set<vertex<[vertexType]>> [paramName], ...
   *
   *   VertexSet_[patternId] = { [paramName] };
   *
   * 2. If the vertex set has constraint from a fixed id list, generate code:
   *   SetAccum<string> @@VertexSet_[patternId]_idList;
   *   SetAccum<vertex> @@VertexSet_[patternId]_vertexList;
   *
   *   @@VertexSet_[patternId]_idList += [id1];
   *   @@VertexSet_[patternId]_idList += [id2];
   *   ...
   *   @@VertexSet_[patternId]_vertexList = to_vertex_set(@@VertexSet_[patternId]_idList, "[vertexType]");
   *   VertexSet_[patternId] ([vertexType]) = { @@VertexSet_[patternId]_vertexList };
   *
   * 3. If the vertex set is an output of a widget, generate code:
   *    - If it's a Union/Intersect/Subtract widget:
   *      VertexSet_[patternId] = VertexSet_[input_0_patternId] UNION|INTERSECT|MINUS VertexSet_[input_1_patternId];
   *    - If it's a Within widget:
   *      VertexSet_[patternId] = VertexSet_[input_patternId];
   * @private
   * @param {{
   *       vertexPatternIds: string[],
   *       edgePatternIds: string[]
   *     }} queryBlock
   * @param {string[]} accumulatorsDecl
   * @returns {string}
   * @memberof Pattern
   */
  private prepareVertexSetsInQueryBlock(
    queryBlock: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    },
    accumulatorsDecl: Set<string>,
    constrainedVertexPatterns: Set<string>
  ): string {
    const resultStmts: string[] = [];

    queryBlock.vertexPatternIds.forEach(pvId => {
      const patternVertex = <PatternVertexSet>this.getPatternEntityById(pvId);
      const vertexType = patternVertex.vertices[0];

      let patternHasParamOrIdConstraints = false;

      // Case 1. the vertex set has constraint from a parameter, collect the parameter:
      if (patternVertex.isParam) {
        resultStmts.push(`VertexSet_${pvId} = { ${patternVertex.paramName} }`);
        patternHasParamOrIdConstraints = true;
        constrainedVertexPatterns.add(pvId);
      }

      // Case 2. the vertex set has constraint from a fixed id list:
      if (patternVertex.ids.length > 0) {
        const fixedIdListAccum = `@@VertexSet_${pvId}_idList`;
        const fixedVertexAccum = `@@VertexSet_${pvId}_vertexList`;
        accumulatorsDecl.add(`SetAccum<string> ${fixedIdListAccum}`);
        accumulatorsDecl.add(`SetAccum<vertex> ${fixedVertexAccum}`);
        patternVertex.ids.forEach(id =>
          resultStmts.push(`${fixedIdListAccum} += "${HelperFunctions.escapeQuote(id)}"`)
        );
        resultStmts.push(`${fixedVertexAccum} = ` +
          `to_vertex_set(${fixedIdListAccum}, "${vertexType}")`);
        resultStmts.push(`VertexSet_${pvId} (${vertexType}) = { ${fixedVertexAccum} }`);
        patternHasParamOrIdConstraints = true;
        constrainedVertexPatterns.add(pvId);
      }

      // Case 3. the vertex set is an output of a widget:
      const widget = this.patternWidgets.find(pw => pw.outputPatternIds.includes(pvId));
      if (widget) {
        let rhsCode = '';
        switch (widget.type) {
          case VertexSetOperation.Intersect: {
            rhsCode = widget.inputPatternIds.map(pid => `VertexSet_${pid}`).join(' INTERSECT ');
            resultStmts.push(`VertexSet_${pvId} = ` + (patternHasParamOrIdConstraints
              ? `VertexSet_${pvId} INTERSECT (${rhsCode})` : rhsCode));
            constrainedVertexPatterns.add(pvId);
            break;
          }
          case VertexSetOperation.Union: {
            rhsCode = widget.inputPatternIds.map(pid => `VertexSet_${pid}`).join(' UNION ');
            resultStmts.push(`VertexSet_${pvId} = ` + (patternHasParamOrIdConstraints
              ? `VertexSet_${pvId} INTERSECT (${rhsCode})` : rhsCode));
            constrainedVertexPatterns.add(pvId);
            break;
          }
          case VertexSetOperation.Subtract: {
            rhsCode = widget.inputPatternIds.map(pid => `VertexSet_${pid}`).join(' MINUS ');
            resultStmts.push(`VertexSet_${pvId} = ` + (patternHasParamOrIdConstraints
              ? `VertexSet_${pvId} INTERSECT (${rhsCode})` : rhsCode));
            constrainedVertexPatterns.add(pvId);
            break;
          }
          case VertexSetOperation.Within: {
            const withinWidgetInputId = widget.inputPatternIds[0];
            if (this.checkVertexPatternHasNoFromClause(queryBlock, patternVertex)) {
              // if no FROM clause will be generated, a new VertexSet assignment is necessary.
              rhsCode = `VertexSet_${withinWidgetInputId}`;
            }
            if (patternHasParamOrIdConstraints) {
              rhsCode = `VertexSet_${pvId} INTERSECT VertexSet_${withinWidgetInputId}`;
            }
            if (rhsCode) {
              resultStmts.push(`VertexSet_${pvId} = ${rhsCode}`);
              constrainedVertexPatterns.add(pvId);
            }
            break;
          }
          default: {
            // TODO: Support other widget types.
            throw new GraphStudioError(`Not supported widget type ${widget.type}.`);
          }
        }
      }
    });

    return resultStmts.map(stmt => `  ${stmt};\n`).join('');
  }

  /**
   * Check if the GSQL code generation will not generate a FROM clause for a given vertex pattern.
   *
   * @private
   * @param {PatternVertexSet} vertexPattern
   * @returns {boolean}
   * @memberof Pattern
   */
  private checkVertexPatternHasNoFromClause(
    queryBlock: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    },
    vertexPattern: PatternVertexSet
  ): boolean {
    return !vertexPattern.isParam
      && vertexPattern.filters.length === 0
      && vertexPattern.aggregations.length === 0
      && vertexPattern.orderBy.length === 0
      && !vertexPattern.hasLimit
      && queryBlock.vertexPatternIds.length === 1
      && queryBlock.edgePatternIds.length === 0;
  }

  /**
   * Generate the FROM clause of a given query block.
   *
   * @private
   * @param {{
   *       vertexPatternIds: string[],
   *       edgePatternIds: string[]
   *     }} queryBlock
   * @param {Graph} graph
   * @returns {string}
   * @param {Set<string>} constrainedVertexPatterns
   * @memberof Pattern
   */
  private generateFromClause(
    queryBlock: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    },
    graph: Graph,
    constrainedVertexPatterns: Set<string>
  ): string {
    if (queryBlock.vertexPatternIds.length === 1 && queryBlock.edgePatternIds.length === 0) {
      return `        FROM ${this.codeToReferToOneVertexPatternInFromClause(
        queryBlock.vertexPatternIds[0], constrainedVertexPatterns
      )}\n`;
    }
    const edgePatternsCode = queryBlock.edgePatternIds.map(peId =>
      this.generateOneEdgePatternInFromClause(
        <PatternEdgeSet>this.getPatternEntityById(peId),
        graph,
        constrainedVertexPatterns
      ));
    return `        FROM ${edgePatternsCode.join(',\n             ')}\n`;
  }

  /**
   * Generate one edge pattern code in FROM clause.
   *
   * @private
   * @param {PatternEdgeSet} patternEdgeSet
   * @param {Graph} graph
   * @param {Set<string>} constrainedVertexPatterns
   * @returns {string}
   * @memberof Pattern
   */
  private generateOneEdgePatternInFromClause(
    patternEdgeSet: PatternEdgeSet,
    graph: Graph,
    constrainedVertexPatterns: Set<string>
  ): string {
    let result = '';

    // Source vertex pattern.
    result += this.codeToReferToOneVertexPatternInFromClause(
      patternEdgeSet.sourcePatternId, constrainedVertexPatterns
    );

    // Edge pattern.
    const edgeType = patternEdgeSet.edges[0];
    result += ` -(${edgeType + (graph.getEdge(edgeType).directed ? '>' : '')}${
      patternEdgeSet.repeatAtLeast === 1 && patternEdgeSet.repeatAtMost === 1
      ? `:${this.getPatternComponentAlias(patternEdgeSet)}`
      : `*${patternEdgeSet.repeatAtLeast}..${patternEdgeSet.repeatAtMost}`
    })- `;

    // Target vertex pattern.
    result += this.codeToReferToOneVertexPatternInFromClause(
      patternEdgeSet.targetPatternId, constrainedVertexPatterns
    );

    return result;
  }

  /**
   * Code in FROM clause to refer to a vertex pattern.
   *
   * @private
   * @param {string} patternId
   * @param {Set<string>} constrainedVertexPatterns
   * @returns {string}
   * @memberof Pattern
   */
  private codeToReferToOneVertexPatternInFromClause(
    vertexSetId: string,
    constrainedVertexPatterns: Set<string>
  ): string {
    const vertexPattern = <PatternVertexSet>this.getPatternEntityById(vertexSetId);
    let fromConstrain = '';
    if (!constrainedVertexPatterns.has(vertexSetId)) {
      // If the vertex set is not prepared,
      // we should either refer to its within widget parent or its vertex type.
      const parentVertexSetId = this.findWithinWidgetInput(vertexSetId);
      fromConstrain = parentVertexSetId ? `VertexSet_${parentVertexSetId}` : vertexPattern.vertices[0];
    } else {
      fromConstrain = `VertexSet_${vertexSetId}`;
    }
    return `${fromConstrain}:${this.getPatternComponentAlias(vertexPattern)}`;
  }

  /**
   * If the vertex set of the given id is an output of a Within widget,
   * return the input vertex pattern id of the widget.
   *
   * @private
   * @param {string} vertexSetId
   * @returns {string}
   * @memberof Pattern
   */
  private findWithinWidgetInput(vertexSetId: string): string {
    const widget = this.patternWidgets.find(pw => pw.outputPatternIds.includes(vertexSetId));
    if (widget && widget.type === VertexSetOperation.Within) {
      vertexSetId = widget.inputPatternIds[0];
      return vertexSetId;
    }
    return '';
  }

  /**
   * Get vertex or edge pattern alias.
   *
   * @private
   * @param {PatternEntity} component
   * @returns {string}
   * @memberof Pattern
   */
  private getPatternComponentAlias(
    component: PatternEntity
  ): string {
    return component.alias ? component.alias : `${aliasSchemaAffix}_${component.id}`;
  }

  /**
   * Generate the WHERE clause. The clause will contain the disjunction of all filter expressions
   * on all vertex patterns and edge patterns of the query block.
   *
   * @private
   * @param {{
   *       vertexPatternIds: string[],
   *       edgePatternIds: string[]
   *     }} queryBlock
   * @returns {string}
   * @memberof Pattern
   */
  private generateWhereClause(
    queryBlock: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }
  ): string {
    const filterExpressions: string[] = [];

    // Filters on vertex patterns and edge patterns.
    queryBlock.vertexPatternIds
      .map(pvId => this.getPatternEntityById(pvId))
      .concat(queryBlock.edgePatternIds.map(peId => this.getPatternEntityById(peId)))
      .forEach(pv =>
        filterExpressions.push(...(<PatternVertexSet | PatternEdgeSet>pv).filters
          .map(filter => this.removeParamParenthesis(filter.toString()))
        )
      );

    // return filterExpressions;
    return filterExpressions.length === 0
      ? '' : `        WHERE ${filterExpressions.join(' AND \n              ')}\n`;
  }

  /**
   * Generate the ACCUM clauses and POST-ACCUM statements of each vertex pattern in the query block.
   *
   * @private
   * @param {{
   *       vertexPatternIds: string[],
   *       edgePatternIds: string[]
   *     }} queryBlock
   * @param {string[]} accumulatorsDecl
   * @returns {{
   *     accumStmts: string[],
   *     postAccumStmts: Map<string, string[]>
   *   }}
   * @memberof Pattern
   */
  private generateAccumStatements(
    queryBlock: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    },
    accumulatorsDecl: Set<string>,
  ): {
    accumStmts: string[],
    postAccumStmts: Map<string, string[]>
  } {
    const accumStmts: string[] = [];
    const postAccumStmts = new Map<string, string[]>();

    queryBlock.vertexPatternIds.forEach(pvId => {
      const vertexPattern = <PatternVertexSet>this.getPatternEntityById(pvId);
      const alias = this.getPatternComponentAlias(vertexPattern);

      vertexPattern.aggregations.forEach(aggregation => {
        const aggAccumName = `@Agg_${pvId}_${aggregation.name}`;
        // aggIsAnExpression represents if the aggregation is an expression.
        const aggIsAnExpression = aggregation.expression instanceof ArithmeticExpression;
        const dfsHelper = {
          // hasSeenAggregationFunction represents if there is at least one aggregation function.
          hasSeenAggregationFunction: false,
          // funcAccumId generates unique accumulator names in the recursion
          // to translate functions in the aggregation to GSQL code.
          aggregationFunctionAccumId: 0,
          // accumulatorsDecl and accumStmts collect SumAccum declarations and statements
          // for the case when the aggregation involves attributes of vertex or edge.
          accumulatorsDecl: new Set<string>(),
          accumStmts: [],
        };

        // Recursively generate GSQL code.
        const code = this.generateAccumStatementsHelper(
          aggregation.expression,
          alias,
          aggAccumName,
          aggIsAnExpression,
          dfsHelper,
          accumulatorsDecl,
          accumStmts,
          postAccumStmts,
        );

        // Post-processing
        if (dfsHelper.hasSeenAggregationFunction) {
          if (aggIsAnExpression) {
            // If the aggregation is an expression and it contains at least one aggregation function,
            // 1. for each attribute of vertex or edge, use a SumAccum in the ACCUM clause
            //    to collect its values and refer to this SumAccum instead in the POST-ACCUM clause.
            // 2. use an additional SumAccum<double> in the POST-ACCUM clause to collect the value
            //    of the expression.

            dfsHelper.accumulatorsDecl.forEach(accumDecl => {
              accumulatorsDecl.add(accumDecl);
            });
            dfsHelper.accumStmts.forEach(accumStmt => {
              accumStmts.push(accumStmt);
            });

            accumulatorsDecl.add(`SumAccum<double> ${aggAccumName}`);
            const stmts = postAccumStmts.get(alias) || [];
            stmts.push(`${alias}.${aggAccumName} = ${code}`);
            postAccumStmts.set(alias, stmts);
          } // otherwise, do nothing because the GSQL code has been generated during the recursion.
        } else {
          // For aggregations with only vertex, edge or atom
          switch (aggregation.expression.getType()) {
            case DataType.VertexOrEdge:
              let setAccumType = 'edge';
              if (this.patternVertexSets.find(
                pv => pv.alias === (<AliasReference>aggregation.expression).value)) {
                  setAccumType = 'vertex';
              }
              accumulatorsDecl.add(`SetAccum<${setAccumType}> ${aggAccumName}`);
              break;
            case DataType.String:
              accumulatorsDecl.add(`SumAccum<string> ${aggAccumName}`);
              break;
            case DataType.Int:
              accumulatorsDecl.add(`SumAccum<int> ${aggAccumName}`);
              break;
            case DataType.Real:
              accumulatorsDecl.add(`SumAccum<double> ${aggAccumName}`);
              break;
            case DataType.Bool:
              accumulatorsDecl.add(`OrAccum ${aggAccumName}`);
              break;
            case DataType.Datetime:
              accumulatorsDecl.add(`SetAccum<datetime> ${aggAccumName}`);
              break;
          }
          accumStmts.push(`${alias}.${aggAccumName} = ${
            this.removeParamParenthesis(aggregation.expression.toString())}`);
        }
      });
    });

    return {
      accumStmts: accumStmts,
      postAccumStmts: postAccumStmts
    };
  }

  /**
   * Use recursion to help generate the ACCUM clauses and POST-ACCUM statements of
   * one vertex pattern in the query block.
   *
   * @private
   * @param {(Atom | BaseExpression)} expression
   * @param {string} alias
   * @param {string} aggAccumName
   * @param {boolean} aggIsAnExpression
   * @param {{
    *       hasSeenAggregationFunction: boolean,
    *       aggregationFunctionAccumId: number
    *     }} dfsHelper
   * @param {Set<string>} accumulatorsDecl
   * @param {string[]} accumStmts
   * @param {Map<string, string[]>} postAccumStmts
   *
   * @returns {string}
   * @memberof Pattern
   */
  private generateAccumStatementsHelper(
    expression: Atom | BaseExpression,
    alias: string,
    aggAccumName: string,
    aggIsAnExpression: boolean,
    dfsHelper: {
      hasSeenAggregationFunction: boolean,
      aggregationFunctionAccumId: number,
      accumulatorsDecl: Set<string>,
      accumStmts: string[],
    },
    accumulatorsDecl: Set<string>,
    accumStmts: string[],
    postAccumStmts: Map<string, string[]>
  ): string {
    if (expression instanceof ArithmeticExpression) {
      const leftStmt = this.generateAccumStatementsHelper(
        expression.operands[0],
        alias,
        aggAccumName,
        aggIsAnExpression,
        dfsHelper,
        accumulatorsDecl,
        accumStmts,
        postAccumStmts,
      );
      const operator = expression.operator;
      const rightStmt = this.generateAccumStatementsHelper(
        expression.operands[1],
        alias,
        aggAccumName,
        aggIsAnExpression,
        dfsHelper,
        accumulatorsDecl,
        accumStmts,
        postAccumStmts,
      );
      return `(${leftStmt} ${operator} ${rightStmt})`;
    } else if (expression instanceof FunctionExpression) {
      dfsHelper.hasSeenAggregationFunction = true;
      if (aggIsAnExpression) {
        aggAccumName = `${aggAccumName}_${dfsHelper.aggregationFunctionAccumId}`;
        dfsHelper.aggregationFunctionAccumId++;
      }
      switch (expression.operator) {
        case FunctionType.Sum: {
          accumulatorsDecl.add(`SumAccum<double> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
        case FunctionType.Max: {
          accumulatorsDecl.add(`MaxAccum<double> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
        case FunctionType.Min: {
          accumulatorsDecl.add(`MinAccum<double> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
        case FunctionType.Avg: {
          accumulatorsDecl.add(`AvgAccum ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
        case FunctionType.Count: {
          accumulatorsDecl.add(`SumAccum<int> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += 1`);
          break;
        }
        case FunctionType.DistinctCount: {
          accumulatorsDecl.add(`SetAccum<${
            this.getExprDataType(expression.operands[0])}> ${aggAccumName}_set`);
          accumStmts.push(`${alias}.${aggAccumName}_set += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);

          accumulatorsDecl.add(`SumAccum<int> ${aggAccumName}`);
          const stmts = postAccumStmts.get(alias) || [];
          stmts.push(`${alias}.${aggAccumName} = ${alias}.${aggAccumName}_set.size()`);
          postAccumStmts.set(alias, stmts);
          break;
        }
        case FunctionType.Collect: {
          accumulatorsDecl.add(`ListAccum<${
            this.getExprDataType(expression.operands[0])}> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
        case FunctionType.DistinctCollect: {
          accumulatorsDecl.add(`SetAccum<${
            this.getExprDataType(expression.operands[0])}> ${aggAccumName}`);
          accumStmts.push(`${alias}.${aggAccumName} += ${
            this.removeParamParenthesis(expression.operands[0].toString())}`);
          break;
        }
      }
      // If the aggregation function is at the top level of the aggregation,
      // its GSQL code will be generated during the recursion,
      // so no need to return other information to do post-processing.
      return aggIsAnExpression ? `${alias}.${aggAccumName}` : '';
    } else if (expression instanceof AttrVariableWithAlias) {
      // For an aggregation whose top level is an expression with functions inside,
      // it needs a POST-ACCUM clause; However, POST-ACCUM cannot refer to any attributes
      // on another vertex or edge; Therefore, in this case we use an additional SumAccum
      // to collect those attributes and refer to the SumAccum instead in the POST-ACCUM clause.

      // If the attribute is at the top level of the aggregation,
      // its GSQL code will be generated in the post-processing.
      if (!aggIsAnExpression) {
        return '';
      }
      let sumAccumType = '';
      switch (expression.getType()) {
        case DataType.Int: {
          sumAccumType = 'int';
          break;
        }
        case DataType.Real: {
          sumAccumType = 'double';
          break;
        }
        // TODO: Support other data types for attribute on another alias in the future.
      }
      // If the attribute type is boolean, string or datetime,
      // which means the attribute must be at the top level of the aggregation,
      // its GSQL code will be generated in the post-processing.
      if (sumAccumType === '') {
        return '';
      }
      aggAccumName = `${aggAccumName}_${dfsHelper.aggregationFunctionAccumId}`;
      dfsHelper.aggregationFunctionAccumId++;
      dfsHelper.accumulatorsDecl.add(`SumAccum<${sumAccumType}> ${aggAccumName}`);
      dfsHelper.accumStmts.push(`${alias}.${aggAccumName} = ${
        this.removeParamParenthesis(expression.toString())}`);
      return `${alias}.${aggAccumName}`;
    } else {
      return this.removeParamParenthesis(expression.toString());
    }
  }

  /**
   * Get GSQL data type based on expression data type.
   *
   * @private
   * @param {(Atom | BaseExpression)} expression
   * @returns {string}
   * @memberof Pattern
   */
  private getExprDataType(expression: Atom | BaseExpression): string {
    if (expression instanceof AliasReference) {
      const alias = expression.value;
      if (this.patternVertexSets.find(pv => pv.alias === alias)) {
        return 'vertex';
      }
      return 'edge';
    }
    let dataType: string;
    if (expression instanceof BaseExpression) {
      dataType = expression.getType();
    } else {
      dataType = expression.valueType;
    }
    switch (dataType) {
      case DataType.String: {
        return 'string';
      }
      case DataType.Int: {
        return 'int';
      }
      case DataType.Real: {
        return 'double';
      }
      case DataType.Bool: {
        return 'bool';
      }
      case DataType.Datetime: {
        return 'datetime';
      }
    }
  }

  /**
   * Collect parameters from one expression.
   *
   * @private
   * @param {(BaseCondition | BaseExpression | Atom)} model
   * @returns {PatternParam[]}
   * @memberof Pattern
   */
  private collectExprParams(model: BaseCondition | BaseExpression | Atom): PatternParam[] {
    if (model instanceof Atom) {
      return model instanceof BaseParam ? [this.getParam(model)] : [];
    }

    const params: PatternParam[] = [];
    if (model.operands) {
      for (let i = 0; i < model.operands.length; i++) {
        params.push(...this.collectExprParams(model.operands[i]));
      }
    }

    return params;
  }

  /**
   * Get the parameter of one atom level expression.
   *
   * @private
   * @param {BaseParam} model
   * @returns {PatternParam}
   * @memberof Pattern
   */
  private getParam(model: BaseParam): PatternParam {
    switch (true) {
      case model instanceof IntParam: {
        return new PatternParam(model.value, false, 'int');
      }
      case model instanceof RealParam: {
        return new PatternParam(model.value, false, 'double');
      }
      case model instanceof StringParam: {
        return new PatternParam(model.value, false, 'string');
      }
      case model instanceof DatetimeParam: {
        return new PatternParam(model.value, false, 'datetime');
      }
      case model instanceof BoolParam: {
        return new PatternParam(model.value, false, 'bool');
      }
    }
  }

  /**
   * Conduct semantic check on the pattern given a graph schema.
   *
   * @param {Graph} graph
   * @returns {PatternSemanticCheckResult}
   * @memberof Pattern
   */
  semanticCheck(graph: Graph): PatternSemanticCheckResult {
    let success = true;
    const errors: PatternSemanticError[] = [];

    // All pattern entities.
    const patternEntities: PatternEntity[] =
      (<PatternEntity[]>this.patternVertexSets).concat(this.patternEdgeSets)
      .concat(this.patternWidgets).concat(this.patternViews);

    // 0. If the pattern is empty, should report empty pattern error directly. No other errors.
    if (patternEntities.length === 0) {
      return {
        success: false,
        errors: [
          {
            message: 'The pattern is empty.',
            involvedPatternEntities: []
          }
        ]
      };
    }

    // Check if the pattern matches the schema:
    // 1. No vertex pattern should refer to non-exist vertex type(s) in schema.
    const notExistVertexTypeToPatternIdList = new Map<string, string[]>();
    for (const patternVertexSet of this.patternVertexSets) {
      const vertexType = patternVertexSet.vertices[0];
      if (!graph.getAllVertexTypes().includes(vertexType)) {
        success = false;
        const patternIdList = notExistVertexTypeToPatternIdList.get(vertexType) || [];
        patternIdList.push(patternVertexSet.id);
        notExistVertexTypeToPatternIdList.set(vertexType, patternIdList);
      }
    }
    notExistVertexTypeToPatternIdList.forEach((patternIdList, vType) => {
      errors.push({
        message: `Vertex type ${vType} does not exist in the graph schema.`,
        involvedPatternEntities: patternIdList
      });
    });

    // 2. No edge pattern should refer to non-exist edge type(s) in schema.
    const notExistEdgeTypeToPatternIdList = new Map<string, string[]>();
    for (const patternEdgeSet of this.patternEdgeSets) {
      const edgeType = patternEdgeSet.edges[0];
      if (!graph.getAllEdgeTypes().includes(edgeType)) {
        success = false;
        const patternIdList = notExistEdgeTypeToPatternIdList.get(edgeType) || [];
        patternIdList.push(patternEdgeSet.id);
        notExistEdgeTypeToPatternIdList.set(edgeType, patternIdList);
      }
    }
    notExistEdgeTypeToPatternIdList.forEach((patternIdList, vType) => {
      errors.push({
        message: `Edge type ${vType} does not exist in the graph schema.`,
        involvedPatternEntities: patternIdList
      });
    });

    // 3. Check if the pattern's expressions are valid.
    success = this.semanticCheckExpressions(graph, errors) && success;

    // 4. Check if any widget has errors.
    // For now, the only check needed is all input and output vertex patterns are of the same type.
    const widgetsWithDifferentInputAndOutputTypes: string[] = [];
    this.patternWidgets.forEach(widget => {
      const inAndOut = widget.inputPatternIds.concat(widget.outputPatternIds)
        .map(pId => (<PatternVertexSet>this.getPatternEntityById(pId)).vertices[0]);
      const isValid = inAndOut.map(type => type === inAndOut[0]).reduce((a, b) => a && b, true);
      if (!isValid) {
        success = false;
        widgetsWithDifferentInputAndOutputTypes.push(widget.id);
      }
    });
    if (widgetsWithDifferentInputAndOutputTypes.length > 0) {
      errors.push({
        message: `All the input and output vertex patterns of a widget must have same vertex type.`,
        involvedPatternEntities: widgetsWithDifferentInputAndOutputTypes
      });
    }

    // 5. Check if edge type exists between source and target vertex pattern.
    const allTriplets = Triplet.getAllTriplets(graph);
    const notExistEdgeTriplet = new Map<string, string[]>();
    this.patternEdgeSets.forEach(pe => {
      const edgeType = pe.edges[0];
      if (graph.getAllEdgeTypes().includes(edgeType)) {
        const sourceVertexType =
          (<PatternVertexSet>this.getPatternEntityById(pe.sourcePatternId)).vertices[0];
        const targetVertexType =
          (<PatternVertexSet>this.getPatternEntityById(pe.targetPatternId)).vertices[0];
        const compliedTriplets = Triplet.refineByEdge(
          Triplet.refineBySource(
            Triplet.refineByTarget(allTriplets,
              [targetVertexType]
            ),
            [sourceVertexType]
          ),
          [edgeType]
        );
        if (compliedTriplets.length === 0) {
          success = false;
          const tripletRep = `${sourceVertexType}#${targetVertexType}#${edgeType}`;
          notExistEdgeTriplet.set(
            tripletRep, (notExistEdgeTriplet.get(tripletRep) || []).concat([pe.id])
          );
        }
      }
    });
    notExistEdgeTriplet.forEach((idList, triplet) => {
      const types = triplet.split('#');
      errors.push({
        message: `Edge type ${types[2]} doesn't exist ` +
          `from vertex type ${types[0]} to vertex type ${types[1]}.`,
        involvedPatternEntities: idList
      });
    });

    const queryBlocks: {
      vertexPatternIds: string[],
      edgePatternIds: string[]
    }[] = [];
    const id2Block = new Map<string, number>();
    const upperDependencies: Set<number>[] = [];
    const lowerDependencies: Set<number>[] = [];
    this.splitPatternIntoBlocksAndGetDependencies(
      queryBlocks, id2Block, upperDependencies, lowerDependencies
    );
    // 6. There should be no circle dependencies among the connected components.
    for (let i = 0; i < queryBlocks.length; i++) {
      const checkCircle = this.patternBlocksContainsCircle(i, [i], lowerDependencies);
      if (checkCircle.hasCircle) {
        success = false;
        const widgets = this.patternWidgets
          .filter(widget =>
            widget.inputPatternIds
              .filter(entity => checkCircle.circle.includes(id2Block.get(entity))).length > 0 &&
            widget.outputPatternIds
              .filter(entity => checkCircle.circle.includes(id2Block.get(entity))).length > 0
          )
          .map(widget => widget.id);
        errors.push({
          message: 'Widget patterns cause circular dependencies among vertex patterns.',
          involvedPatternEntities: widgets
        });
        break;
      }
    }

    // 7. Check if the pattern's pattern views are valid.
    success = this.semanticCheckPatternViews(id2Block, errors) && success;

    // 8. No parameters should have same name but different data types.
    const params = this.collectParameters();
    const paramInCompatible = new Map<string, Set<string>>();
    params.forEach((param, i) => {
      // Parameters cannot have same name as vertex or edge type.
      if (graph.getAllVertexTypes().includes(param.paramName)) {
        success = false;
        errors.push({
          message: `Parameter cannot be named as ${param.paramName} because it is a vertex type.`,
          involvedPatternEntities: []
        });
      }
      if (graph.getAllEdgeTypes().includes(param.paramName)) {
        success = false;
        errors.push({
          message: `Parameter cannot be named as ${param.paramName} because it is an edge type.`,
          involvedPatternEntities: []
        });
      }
      // Same parameter cannot appear as different parameter types.
      for (let j = 0; j < i; j++) {
        if (!param.isCompatible(params[j])) {
          const paramWithSameName = paramInCompatible.get(param.paramName) || new Set<string>();
          paramWithSameName.add(param.getTypeDescription());
          paramWithSameName.add(params[j].getTypeDescription());
          paramInCompatible.set(param.paramName, paramWithSameName);
        }
      }
    });
    if (paramInCompatible.size > 0) {
      success = false;
      paramInCompatible.forEach((descList, paramName) => errors.push({
        message: `Parameter ${paramName} cannot be ` +
          `${HelperFunctions.joinStrAsPara(Array.from(descList))} at the same time.`,
        involvedPatternEntities: []
      }));
    }

    // 9. Parameter name shouldn't be duplicated with aliases.
    const paramNames = new Set<string>();
    params.forEach(param => paramNames.add(param.paramName));
    const aliases = this.patternVertexSets.map(vertexSet => vertexSet.alias)
      .concat(this.patternEdgeSets.map(edgeSet => edgeSet.alias))
      .concat(this.patternViews.map(patternView => patternView.alias));
    paramNames.forEach(pName => {
      if (aliases.includes(pName)) {
        success = false;
        errors.push({
          message: `Parameter name ${pName} duplicates with a vertex pattern, edge pattern or pattern view name.`,
          involvedPatternEntities: []
        });
      }
    });

    // 9. Vertex should not be compared with edge.
    queryBlocks.forEach(block => {
      const vertexAliases = block.vertexPatternIds.map(vid =>
        this.getPatternComponentAlias(this.findComponentById(vid)));
      const edgeAliases = block.edgePatternIds.map(eid =>
        this.getPatternComponentAlias(this.findComponentById(eid)));
      block.vertexPatternIds.forEach(vid => {
        (this.findComponentById(vid) as PatternVertexSet).filters.forEach(filter => {
          const check = this.checkVertexComparedWithEdge(filter, vertexAliases, edgeAliases);
          if (check.error) {
            success = false;
            errors.push({
              message: check.message,
              involvedPatternEntities: [vid]
            });
          }
        });
      });

      block.edgePatternIds.forEach(eid => {
        (this.findComponentById(eid) as PatternEdgeSet).filters.forEach(filter => {
          const check = this.checkVertexComparedWithEdge(filter, vertexAliases, edgeAliases);
          if (check.error) {
            success = false;
            errors.push({
              message: check.message,
              involvedPatternEntities: [eid]
            });
          }
        });
      });
    });

    // 10. Should output something.
    const patternEntitiesToBeOutput = patternEntities.filter(entity => entity.output);
    if (patternEntities.length > 0 && patternEntitiesToBeOutput.length === 0) {
      success = false;
      errors.push({
        message: 'Should output something in the pattern.',
        involvedPatternEntities: []
      });
    }

    return {
      success: success,
      errors: errors
    };
  }

  /**
   * Check if pattern blocks contains circle dependencies caused by widget connections.
   *
   * @private
   * @param {number} head
   * @param {number[]} touchedBlockIds
   * @param {Set<number>[]} outLinks
   * @returns {{
   *     hasCircle: boolean,
   *     circle: number[]
   *   }}
   * @memberof Pattern
   */
  private patternBlocksContainsCircle(
    head: number,
    touchedBlockIds: number[],
    outLinks: Set<number>[]
  ): {
    hasCircle: boolean,
    circle: number[]
  } {
    // Check if the traversal path contains a circle.
    const indexOfHead = touchedBlockIds.slice(0, touchedBlockIds.length - 1).indexOf(head);
    if (indexOfHead !== -1) {
      return {
        hasCircle: true,
        circle: touchedBlockIds.slice(indexOfHead + 1)
      };
    }

    for (const nextId of outLinks[head]) {
      touchedBlockIds.push(nextId);
      const checkCircle = this.patternBlocksContainsCircle(nextId, touchedBlockIds, outLinks);
      if (checkCircle.hasCircle) {
        return checkCircle;
      }
      touchedBlockIds.splice(touchedBlockIds.length - 2);
    }

    return {
      hasCircle: false,
      circle: []
    };
  }

  /**
   * Given a tree root, refine the compliance vertex and edge types supported at each pattern
   * vertex and edge pattern from bottom to root. At each tree node, the compliance types will be
   * the intersection of recursive refinement result backtraced from each child tree node. The
   * refinement result vertex types will be annotated onto each vertex pattern.
   * Pattern: A - B - C
   *              |
   *              D - E - F
   *                  |
   *                  G
   * Starting from B as root, you recursively refine following DFS order of the tree, and
   * do intersection at each parent node. The refinement order will follow:
   * A, G, F, E, D, C, B
   *
   * @private
   * @param {PatternVertexSet} parent
   * @param {PatternVertexSet} current
   * @param {Graph} graph
   * @param {SchemaTriplet[]} allTriplets
   * @param {Map<string, string[]>} typeAnnonation
   * @param {Set<string>} visited
   * @memberof Pattern
   */
  private bottomUpIntersectComplianceTypes(
    parent: PatternVertexSet, current: PatternVertexSet,
    graph: Graph,
    allTriplets: SchemaTriplet[],
    typeAnnonation: Map<string, string[]>,
    visited: Set<string>
  ) {
    visited.add(current.id);

    // Get the supported vertex types of current vertex pattern.
    let complianceVertexTypes = current.vertices;
    if (complianceVertexTypes.length === 0) {
      complianceVertexTypes = graph.getAllVertexTypes();
    }
    // Get all schema triplets going out from and going in to current vertex pattern compliance vertex types.
    const outgoingTriplets = Triplet.refineBySource(allTriplets, complianceVertexTypes);
    const incomingTriplets = Triplet.refineByTarget(allTriplets, complianceVertexTypes);

    // Recursive call each child vertex pattern which is not the parent.
    this.patternEdgeSets.forEach(patternEdgeSet => {
      let patternEdgeTypes = patternEdgeSet.edges;
      if (patternEdgeTypes.length === 0) {
        patternEdgeTypes = graph.getAllEdgeTypes();
      }
      if (patternEdgeSet.sourcePatternId === current.id && patternEdgeSet.targetPatternId !== parent.id) {
        const targetId = patternEdgeSet.targetPatternId;
        if (targetId && !visited.has(targetId)) {
          const childPattern = this.patternVertexSets.filter(pv => pv.id === targetId)[0];
          // Recursively refine child vertex pattern.
          this.bottomUpIntersectComplianceTypes(current, childPattern, graph, allTriplets, typeAnnonation, visited);
          // Refine outgoingTriplets by edge pattern edge types and target vertex pattern types.
          const complianceTriplets = Triplet.refineByTarget(
            Triplet.refineByEdge(outgoingTriplets, patternEdgeTypes),
            typeAnnonation.get(targetId)
          );
          // Refine complianceVertexTypes.
          complianceVertexTypes = intersectionWith(
            complianceVertexTypes, Triplet.getSourceTypes(complianceTriplets), isEqual
          );
        }
      } else if (patternEdgeSet.targetPatternId === current.id && patternEdgeSet.sourcePatternId !== parent.id) {
        const sourceId = patternEdgeSet.sourcePatternId;
        if (sourceId && !visited.has(sourceId)) {
          const childPattern = this.patternVertexSets.filter(pv => pv.id === sourceId)[0];
          // Recursively refine child vertex pattern.
          this.bottomUpIntersectComplianceTypes(current, childPattern, graph, allTriplets, typeAnnonation, visited);
          // Refine incomingTriplets by edge pattern edge types and source vertex pattern vertex types.
          const complianceTriplets = Triplet.refineBySource(
            Triplet.refineByEdge(incomingTriplets, patternEdgeTypes),
            typeAnnonation.get(sourceId)
          );
          // Refine complianceVertexTypes.
          complianceVertexTypes = intersectionWith(
            complianceVertexTypes, Triplet.getTargetTypes(complianceTriplets), isEqual
          );
        }
      }

    });

    // Update typeAnnotation map.
    typeAnnonation.set(current.id, complianceVertexTypes);
  }

  /**
   * Given a tree root, refine the compliance vertex and edge types supported at each pattern
   * vertex and edge pattern from root to leaves. At each tree node, the compliance vertex types
   * will be the intersection of top refinement result pushed down to current tree node, and the
   * compliance edge types will be refined by the edge pattern. The refinement result vertex types
   * and edge types will be annotated onto each vertex pattern and edge pattern.
   * Pattern: A - B - C
   *              |
   *              D - E - F
   *                  |
   *                  G
   * Starting from B as root, you recursively refine following DFS order of the tree, and
   * do intersection at each child node. The refinement order will follow:
   * B, A, D, E, G, F, C
   * @private
   * @param {PatternVertexSet} parent
   * @param {PatternVertexSet} current
   * @param {Graph} graph
   * @param {SchemaTriplet[]} allTriplets
   * @param {Map<string, string[]>} typeAnnonation
   * @param {Set<string>} visited
   * @memberof Pattern
   */
  private topDownIntersectComplianceTypes(
    parent: PatternVertexSet, current: PatternVertexSet,
    graph: Graph,
    allTriplets: SchemaTriplet[],
    typeAnnonation: Map<string, string[]>,
    visited: Set<string>
  ) {
    visited.add(current.id);

    // Get the supported vertex types of current vertex pattern.
    const complianceVertexTypes = typeAnnonation.get(current.id);
    const outgoingTriplets = Triplet.refineBySource(allTriplets, complianceVertexTypes);
    const incomingTriplets = Triplet.refineByTarget(allTriplets, complianceVertexTypes);

    // Recursive call each child vertex pattern which is not the parent.
    this.patternEdgeSets.forEach(patternEdgeSet => {
      let patternEdgeTypes = patternEdgeSet.edges;
      if (patternEdgeTypes.length === 0) {
        patternEdgeTypes = graph.getAllEdgeTypes();
      }
      if (patternEdgeSet.sourcePatternId === current.id && patternEdgeSet.targetPatternId !== parent.id) {
        const targetId = patternEdgeSet.targetPatternId;
        if (targetId && !visited.has(targetId)) {
          const childPattern = this.patternVertexSets.filter(pv => pv.id === targetId)[0];
          // Refine outgoingTriplets by edge pattern edge types and target vertex pattern vertex types.
          const complianceTriplets = Triplet.refineByTarget(
            Triplet.refineByEdge(outgoingTriplets, patternEdgeTypes),
            typeAnnonation.get(targetId)
          );
          // Refine edge pattern edge types.
          typeAnnonation.set(patternEdgeSet.id, Triplet.getEdgeTypes(complianceTriplets));
          // Refine target vertex pattern vertex types.
          typeAnnonation.set(targetId, Triplet.getTargetTypes(complianceTriplets));
          // Recursively refine child vertex pattern.
          this.topDownIntersectComplianceTypes(current, childPattern, graph, allTriplets, typeAnnonation, visited);
        }
      } else if (patternEdgeSet.targetPatternId === current.id && patternEdgeSet.sourcePatternId !== parent.id) {
        const sourceId = patternEdgeSet.sourcePatternId;
        if (sourceId && !visited.has(sourceId)) {
          const childPattern = this.patternVertexSets.filter(pv => pv.id === sourceId)[0];
          // Refine incomingTriplets by edge pattern edge types and source vertex pattern vertex types.
          const complianceTriplets = Triplet.refineBySource(
            Triplet.refineByEdge(incomingTriplets, patternEdgeTypes),
            typeAnnonation.get(sourceId)
          );
          // Refine edge pattern edge types.
          typeAnnonation.set(patternEdgeSet.id, Triplet.getEdgeTypes(complianceTriplets));
          // Refine source vertex pattern vertex types.
          typeAnnonation.set(sourceId, Triplet.getSourceTypes(complianceTriplets));
          // Recursively refine child vertex pattern.
          this.topDownIntersectComplianceTypes(current, childPattern, graph, allTriplets, typeAnnonation, visited);
        }
      }
    });
  }

  /**
   * Find pattern entity by id.
   *
   * @private
   * @param {string} id
   * @returns {PatternEntity}
   * @memberof Pattern
   */
  private getPatternEntityById(id: string): PatternEntity {
    const entities = (<PatternEntity[]>this.patternVertexSets)
      .concat(this.patternEdgeSets).concat(this.patternWidgets).concat(this.patternViews);
    return entities.filter(entity => entity.id === id)[0];
  }

  /**
   * Semantic check on all of the expressions of the pattern.
   *
   * @private
   * @param {Graph} graph
   * @param {PatternSemanticError[]} errors
   * @returns {boolean}
   * @memberof Pattern
   */
  private semanticCheckExpressions(graph: Graph, errors: PatternSemanticError[]): boolean {
    let success = true;

    // 1. Check edge pattern filters.
    this.patternEdgeSets.forEach(edgePattern => {
      const filterHasNoUnsupportedFeatures = edgePattern.filters
        .map(filter => this.checkOperandIsValid(filter, [], getFilterFormConfig()))
        .reduce((a, b) => a && b, true);

      if (!filterHasNoUnsupportedFeatures) {
        success = false;
        errors.push({
          message: 'One edge pattern contains unsupported features in its filters. ' +
            'Did you import a solution from a higher version of GraphStudio?',
          involvedPatternEntities: [edgePattern.id]
        });
      } else {
        const filterHasNoSemanticErrors = edgePattern.filters
          .map(filter => filter.semanticCheck().success).reduce((a, b) => a && b, true);

        if (!filterHasNoSemanticErrors) {
          success = false;
          errors.push({
            message: 'One edge pattern has errors in its filters.',
            involvedPatternEntities: [edgePattern.id]
          });
        }
      }
    });

    // 2. Check vertex pattern filters, aggregations and order-bys.
    this.patternVertexSets.forEach(vertexPattern => {
      const unsupportedFeatureSection: string[] = [];
      const errorSection: string[] = [];

      const filterHasNoUnsupportedFeatures = vertexPattern.filters
        .map(filter => this.checkOperandIsValid(filter, [], getFilterFormConfig()))
        .reduce((a, b) => a && b, true);
      if (!filterHasNoUnsupportedFeatures) {
        unsupportedFeatureSection.push('filters');
      } else {
        const filterHasNoSemanticErrors = vertexPattern.filters
          .map(filter => filter.semanticCheck().success).reduce((a, b) => a && b, true);
        if (!filterHasNoSemanticErrors) {
          errorSection.push('filters');
        }
      }

      const aggHasNoUnsupportedFeatures = vertexPattern.aggregations
        .map(agg => this.checkOperandIsValid(agg.expression, [], getAggregationFormConfig()))
        .reduce((a, b) => a && b, true);
      if (!aggHasNoUnsupportedFeatures) {
        unsupportedFeatureSection.push('attachments');
      } else {
        const aggNames = vertexPattern.aggregations.map(agg => agg.name);
        const aggHasNoSemanticErrors = vertexPattern.aggregations
          .map(
            agg => agg.expression.semanticCheck().success && agg.checkAggregationName(aggNames, graph).success
          ).reduce((a, b) => a && b, true);
        if (!aggHasNoSemanticErrors) {
          errorSection.push('attachments');
        }
      }

      const orderByHasNoUnsupportedFeatures = vertexPattern.orderBy
        .map(orderBy => this.checkOperandIsValid(orderBy.expression, [], getOrderByFormConfig()))
        .reduce((a, b) => a && b, true);
      if (!orderByHasNoUnsupportedFeatures) {
        unsupportedFeatureSection.push('order-by expressions');
      } else {
        const orderByHasNoSemanticErrors = vertexPattern.orderBy
          .map(orderBy => orderBy.expression.semanticCheck().success).reduce((a, b) => a && b, true);
        if (!orderByHasNoSemanticErrors) {
          errorSection.push('order-by expressions');
        }
      }

      if (unsupportedFeatureSection.length > 0) {
        success = false;
        errors.push({
          message: `One vertex pattern contains unsupported features in its ` +
            `${HelperFunctions.joinStrAsPara(unsupportedFeatureSection)}. ` +
            `Did you import a solution from a higher version of GraphStudio?`,
          involvedPatternEntities: [vertexPattern.id]
        });
      }

      if (errorSection.length > 0) {
        success = false;
        errors.push({
          message: `One vertex pattern has errors in its ${HelperFunctions.joinStrAsPara(errorSection)}.`,
          involvedPatternEntities: [vertexPattern.id]
        });
      }
    });

    // 3. Check pattern view order-bys.
    this.patternViews.forEach((patternView) => {
      const unsupportedFeatureSection: string[] = [];
      const errorSection: string[] = [];

      const orderByHasNoUnsupportedFeatures = patternView.orderBy
        .map((orderBy) => this.checkOperandIsValid(orderBy.expression, [], getOrderByFormConfig()))
        .reduce((a, b) => a && b, true);
      if (!orderByHasNoUnsupportedFeatures) {
        unsupportedFeatureSection.push('order-by expressions');
      } else {
        const orderByHasNoSemanticErrors = patternView.orderBy
          .map((orderBy) => orderBy.expression.semanticCheck().success)
          .reduce((a, b) => a && b, true);
        if (!orderByHasNoSemanticErrors) {
          errorSection.push('order-by expressions');
        }
      }

      if (unsupportedFeatureSection.length > 0) {
        success = false;
        errors.push({
          message:
            `One pattern view contains unsupported features in its ` +
            `${HelperFunctions.joinStrAsPara(unsupportedFeatureSection)}. ` +
            `Did you import a solution from a higher version of GraphStudio?`,
          involvedPatternEntities: [patternView.id],
        });
      }

      if (errorSection.length > 0) {
        success = false;
        errors.push({
          message: `One pattern view has errors in its ${HelperFunctions.joinStrAsPara(errorSection)}.`,
          involvedPatternEntities: [patternView.id],
        });
      }
    });

    return success;
  }

  /**
   * Semantic check on all of patten views of the pattern.
   *
   * @private
   * @param {Map<string, number>} id2Block
   * @param {PatternSemanticError[]} errors
   *
   * @returns {boolean}
   * @memberof Pattern
   */
  private semanticCheckPatternViews(
    id2Block: Map<string, number>,
    errors: PatternSemanticError[],
  ): boolean {
    let success = true;

    // 1. Check if the pattern views do not overlap with each other,
    // namely, no vertex patterns should belong to more than one pattern view.
    // TODO: We may loose this constraint in the future.

    // <key = vertex set id, value = id of the pattern view that the vertex set belongs to>
    const vertexSetId2PatternViewIds = new Map<string, Set<string>>();
    this.patternViews.forEach(patternView => {
      if (!success) {
        return;
      }

      patternView.vertexSetIds.forEach(vertexSetId => {
        const patternViewIds = vertexSetId2PatternViewIds.get(vertexSetId) || new Set<string>();
        patternViewIds.add(patternView.id);

        if (patternViewIds.size > 1) {
          success = false;
          errors.push({
            message: `We currently do not support pattern views overlap with each other.`,
            involvedPatternEntities: Array.from(patternViewIds.values()),
          });
          return;
        }

        vertexSetId2PatternViewIds.set(vertexSetId, patternViewIds);
      });
    });

    // 2. Check if the pattern views do not across more than one connected component,
    // namely, no pattern views should contain vertex patterns that belong to multiple blocks.
    this.patternViews.forEach(patternView => {
      if (!success) {
        return;
      }

      let blockIndex = -1;
      patternView.vertexSetIds.forEach(vertexSetIdInView => {
        if (blockIndex === -1) {
          blockIndex = id2Block.get(vertexSetIdInView);
        } else if (blockIndex !== id2Block.get(vertexSetIdInView)) {
          success = false;
          errors.push({
            message: `We currently do not support one pattern view across different connected components.`,
            involvedPatternEntities: [patternView.id]
          });
          return;
        }
      });
    });

    // 3. Check pattern view filters and order-bys.
    // TODO: Support semantic check on attachment in the future.
    this.patternViews.forEach(patternView => {
      const filterHasNoErrors = patternView.filters
        .map(filter => filter.semanticCheck().success).reduce((a, b) => a && b, true);
      const orderByHasNoErrors = patternView.orderBy
        .map(orderBy => orderBy.expression.semanticCheck().success).reduce((a, b) => a && b, true);

      const errorSection: string[] = [];
      if (!filterHasNoErrors) {
        errorSection.push('filters');
      }
      if (!orderByHasNoErrors) {
        errorSection.push('order-by expressions');
      }

      if (errorSection.length > 0) {
        success = false;
        errors.push({
          message: `The pattern view has errors in its ${HelperFunctions.joinStrAsPara(errorSection)}.`,
          involvedPatternEntities: [patternView.id]
        });
      }
    });

    // 4. Check if each pattern view has an alias.
    this.patternViews.forEach(patternView => {
      if (!patternView.alias) {
        success = false;
        errors.push({
          message: `Each pattern view should have its name.`,
          involvedPatternEntities: [patternView.id]
        });
      }
    });

    return success;
  }

  /**
   * Remove parameter parenthesis from the expression.
   *
   * @private
   * @param {string} expr
   * @returns {string}
   * @memberof Pattern
   */
  private removeParamParenthesis(expr: string): string {
    return expr.replace(new RegExp(`{{`, 'g'), '').replace(new RegExp(`}}`, 'g'), '');
  }

  /**
   * Replace auto-generated components ids with a counter to make the query human-readable.
   *
   * @private
   * @param {string} query
   * @returns {string}
   * @memberof Pattern
   */
  private makeQueryHumanReadable(query: string): string {
    const queryPOIs = [
      'alias_schema',
      'Agg',
      'EdgeSet',
      'FinalVertexSet',
      'FinalEdgeSet',
      'OrderHeap',
      'OrderTuple',
      'OrderVertexSet',
      'VertexSet',
    ];

    const componentIDs = this.patternVertexSets.map(v => v.id)
      .concat(this.patternEdgeSets.map(e => e.id))
      .concat(this.patternWidgets.map(w => w.id))
      .concat(this.patternViews.map(pv => pv.id));

    let counter = 0;
    componentIDs.forEach(id => {
      // Find next safe name.
      counter++;
      while (queryPOIs.find(poi =>
        query.search(new RegExp(`\\b${poi}_${counter}_`, 'g')) > -1
        || query.search(new RegExp(`\\b${poi}_${counter}\\b`, 'g')) > -1
      )) {
        counter++;
      }

      // Replace the id with counter.
      query = query
        .replace(new RegExp(`_${id}\\b`, 'g'), `_${counter}`)
        .replace(new RegExp(`_${id}_`, 'g'), `_${counter}_`);
    });

    return query;
  }

  /**
   * Recursively check if the operand of an atom or each operand's operator in a (nested) condition/expression
   * corresponds to a valid operand type allowed by the given config and the given operands types for each
   * (nested) condition/expression.
   *
   * @param {(Atom | BaseExpression | BaseCondition)} operand
   * @param {string[]} operandsTypes
   * @param {ExpressionFormConfig} config
   * @return {boolean}
   * @memberof Pattern
   */
  checkOperandIsValid(
    operand: Atom | BaseExpression | BaseCondition,
    operandsTypes: string[],
    config: ExpressionFormConfig,
  ): boolean {
    // Construct a list of valid operand types by filtering out the operand types that are not allowed in
    // the given config or in the operandsTypes.
    let validOperandsTypes: string[];
    if (operand instanceof Atom) {
      validOperandsTypes = defaultOptList;
    } else if (operand instanceof BaseExpression) {
      validOperandsTypes = defaultExprList;
    } else if (operand instanceof BaseCondition) {
      validOperandsTypes = defaultCondList;
    }

    validOperandsTypes = validOperandsTypes
      .filter(validOperandType => config.supportOptions.includes(validOperandType));
    if (operandsTypes.length > 0) {
      validOperandsTypes = validOperandsTypes
        .filter(validOperandType => operandsTypes.includes(validOperandType));
    }
    if (
      validOperandsTypes.includes('Function')
      &&
      (config.supportFunctions && config.supportFunctions.length > 0)
    ) {
      const funcList = defaultFuncList
        .filter(functionType => config.supportFunctions.includes(functionType));
      validOperandsTypes = validOperandsTypes.concat(funcList);
    }

    // Check if the operand type of an atom or the operand's operator in a condition/expression corresponds
    // to a valid operand type.
    const operandType = operand.toJson().type;

    if (operand instanceof Atom) {
      return validOperandsTypes.includes(operandType);
    } else if (operand instanceof BaseExpression) {
      if (operandType !== 'NullExpression' && !validOperandsTypes.includes(operand.operator)) {
        return false;
      }
    } else if (operand instanceof BaseCondition) {
      if (operandType !== 'NullCondition' && !validOperandsTypes.includes(operand.operator)) {
        return false;
      }
    }

    for (let i = 0; i < operand.operands.length; i++) {
      if (!this.checkOperandIsValid(operand.operands[i], operand.operandsTypes[i], config)) {
        return false;
      }
    }
    return true;
  }
}
