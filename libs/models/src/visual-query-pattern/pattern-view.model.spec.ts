import { DataType } from '../data';
import { RelationalOperator } from '../expression';

import { PatternView, <PERSON><PERSON><PERSON>iew<PERSON><PERSON> } from './pattern-view.model';

describe('PatternViewModel', () => {
  it('should get pattern view as json type', () => {
    const mockPatternViewJson: PatternViewJson = {
      vertexSetIds: ['v1', 'v2', 'v3'],
      edgeSetIds: ['e1', 'e2'],
      filters: [
        {
          operands: [
            {
              valueType: DataType.Int,
              value: '1',
              type: 'IntParam'
            },
            {
              value: '1.0',
              valueType: DataType.Real,
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        }
      ],
      orderBy: [
        {
          asc: true,
          expression: {
            valueType: DataType.String,
            value: 'a',
            type: 'StringParam'
          }
        }
      ],
      hasLimit: false,
      limit: 10,
      id: '1',
      output: false,
      existentialCheck: false,
    };

    const mockPatternView = new PatternView(mockPatternViewJson);
    expect(mockPatternView.toJson()).toEqual({
      vertexSetIds: ['v1', 'v2', 'v3'],
      edgeSetIds: ['e1', 'e2'],
      filters: [
        {
          operands: [
            {
              value: '1',
              type: 'IntParam'
            },
            {
              value: '1.0',
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        },
      ],
      orderBy: [
        {
          asc: true,
          expression: {
            value: 'a',
            type: 'StringParam'
          }
        }
      ],
      hasLimit: false,
      limit: 10,
      id: '1',
      alias: undefined,
      output: false,
      position: {
        x: 0,
        y: 0
      },
      existentialCheck: false,
    });
  });
});
