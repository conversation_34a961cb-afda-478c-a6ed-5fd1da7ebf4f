import { DataType } from '../data';

import { Pattern<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PatternOrderBy } from './pattern-order-by.model';

describe('PatternOrderByModel', () => {
  it('should get order by pattern as a json type', () => {
    const mockPatternOrderByJson: PatternOrderByJson = {
      asc: false,
      expression: {
        type: 'RealParam',
        operands: [
          {
            valueType: DataType.String,
            value: 'e',
            type: 'StringParam'
          }
        ]
      }
    };
    const mockPatternOrderBy = new PatternOrderBy(mockPatternOrderByJson);
    expect(mockPatternOrderBy.toJson()).toEqual({
      asc: false,
      expression: {
        type: 'RealParam',
        value: undefined
      }
    });
  });
});
