import { ConditionJson, BaseCondition, parseExprJson } from '../expression';
import { Graph } from '../topology/graph.model';

import { PatternEntity, PatternEntityJson } from './pattern-entity.model';
import { PatternOrderBy, PatternOrderByJson } from './pattern-order-by.model';

export interface PatternViewJson extends PatternEntityJson {
  vertexSetIds: string[];
  edgeSetIds: string[];
  filters: ConditionJson[];
  // TODO: Support attachment in the future.
  orderBy: PatternOrderByJson[];
  hasLimit: boolean;
  limit: number;
}

export class PatternView extends PatternEntity {
  vertexSetIds: string[];
  edgeSetIds: string[];
  filters: BaseCondition[];
  // TODO: Support attachment in the future.
  orderBy: PatternOrderBy[];
  hasLimit: boolean;
  limit: number;

  constructor(data: PatternViewJson, schema?: Graph) {
    super(data);
    this.vertexSetIds = data.vertexSetIds;
    this.edgeSetIds = data.edgeSetIds;
    this.filters = data.filters.map(filterJson => <BaseCondition>parseExprJson(filterJson, schema));
    // TODO: Support attachment in the future.
    this.orderBy = data.orderBy.map(orderByJson => new PatternOrderBy(orderByJson, schema));
    this.hasLimit = data.hasLimit || false;
    this.limit = data.limit;
  }

  /**
   * Dump the pattern into JSON.
   *
   * @returns {PatternViewJson}
   * @memberof PatternView
   */
  toJson(): PatternViewJson {
    return {
      ...super.toJson(),
      vertexSetIds: this.vertexSetIds,
      edgeSetIds: this.edgeSetIds,
      filters: this.filters.map(filter => filter.toJson()),
      // TODO: Support attachment in the future.
      orderBy: this.orderBy.map(orderBy => orderBy.toJson()),
      hasLimit: this.hasLimit,
      limit: this.limit,
    };
  }
}
