import { DataType } from '../data';
import { ArithmeticOperator, FunctionType, RelationalOperator } from '../expression';
import { Graph, GSQLGraphJson } from '../topology';
import { GraphStudioError } from '../error';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from './pattern.model';
import { PatternEdgeSetJson, PatternEdgeSet } from './pattern-edge-set.model';
import { PatternParam } from './pattern-param.model';
import { PatternVertexSet, PatternVertexSetJson } from './pattern-vertex-set.model';
import { PatternWidgetJson, PathType, VertexSetOperation } from './pattern-widget.model';
import { PatternWidgetFactory } from './pattern-widget-factory.model';
import { PatternView, PatternViewJson } from './pattern-view.model';

describe('PatternModel', () => {
  const mockGSQLGraphJsonGlobal: GSQLGraphJson = {
    GraphName: 'MyGraph',
    VertexTypes: [
      {
        Name: 'person',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'STRING'
          }
        },
        Attributes: [],
        Config: {
          STATS: 'OUTDEGREE_BY_EDGETYPE',
          PRIMARY_ID_AS_ATTRIBUTE: false
        },
        IsLocal: true
      },
      {
        Name: 'movie',
        PrimaryId: {
          AttributeName: 'id',
          AttributeType: {
            Name: 'STRING'
          }
        },
        Attributes: [
          {
            AttributeName: 'title',
            AttributeType: {
              Name: 'STRING'
            }
          },
          {
            AttributeName: 'genres',
            AttributeType: {
              Name: 'STRING'
            }
          }
        ],
        Config: {
          STATS: 'OUTDEGREE_BY_EDGETYPE',
          PRIMARY_ID_AS_ATTRIBUTE: false
        }
      }
    ],
    EdgeTypes: [
      {
        Name: 'rate',
        FromVertexTypeName: 'person',
        ToVertexTypeName: 'movie',
        IsDirected: true,
        Config: {
          REVERSE_EDGE: 'reverse_rate'
        },
        Attributes: [
          {
            AttributeName: 'rating',
            AttributeType: {
              Name: 'DOUBLE'
            }
          },
          {
            AttributeName: 'rated_at',
            AttributeType: {
              Name: 'DATETIME'
            }
          }
        ]
      }
    ]
  };
  const mockPatternJsonGlobal: PatternJson = {
      name: 'RecommendMovie',
      description: '',
      patternVertexSets: [
       {
         output: true,
         existentialCheck: false,
         vertices: ['person'],
         isParam: true,
         ids: [],
         id: '1',
         alias: 'p',
         filters: [
           {
             operands: [
               {
                 valueType: DataType.Int,
                 value: 'a',
                 type: 'IntParam'
               },
               {
                 value: 'b',
                 valueType: DataType.Real,
                 type: 'RealParam'
               }
             ],
             operator: RelationalOperator.Equal,
             type: 'ComparisonCondition'
           }
         ],
         aggregations: [
           {
             name: 'd',
             expression: {
              valueType: DataType.Real,
              value: 'c',
              type: 'RealParam'
             },
           }
         ],
         orderBy: [
           {
             asc: true,
             expression: {
              valueType: DataType.String,
              value: 'e',
              type: 'StringParam'
            }
           }
         ],
         hasLimit: false,
         limit: 10,
         paramName: 'p'
       },
       {
         output: false,
         existentialCheck: false,
         vertices: ['movie'],
         isParam: false,
         ids: [],
         filters: [],
         aggregations: [],
         orderBy: [],
         hasLimit: false,
         limit: 10,
         id: '2'
       }
      ],
      patternEdgeSets: [
        {
          output: true,
          existentialCheck: false,
          sourcePatternId: '1',
          targetPatternId: '2',
          edges: ['rate'],
          repeatAtLeast: 1,
          repeatAtMost: 1,
          alias: 'r',
          filters: [
            {
              operands: [
                {
                  value: 'r1',
                  valueType: DataType.Int,
                  type: 'IntParam'
                },
                {
                  value: 'r2',
                  valueType: DataType.Int,
                  type: 'IntParam'
                }
              ],
              operator: RelationalOperator.NotEqual,
              type: 'ComparisonCondition'
            }
          ],
          notExist: false
        }
      ],
      patternWidgets: [],
      patternViews: [
        {
          vertexSetIds: ['1', '2'],
          edgeSetIds: [],
          filters: [
            {
              operands: [
                {
                  valueType: DataType.Int,
                  value: 'f',
                  type: 'IntParam'
                },
                {
                  value: 'g',
                  valueType: DataType.Real,
                  type: 'RealParam'
                }
              ],
              operator: RelationalOperator.Equal,
              type: 'ComparisonCondition'
            }
          ],
          orderBy: [
            {
              asc: true,
              expression: {
               valueType: DataType.String,
               value: 'h',
               type: 'StringParam'
             }
            }
          ],
          hasLimit: false,
          limit: 10,
          id: 'view1',
          alias: 'view1',
          output: false,
          existentialCheck: false,
        }
      ],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };
  it('should find component by id', () => {
    // Pattern 1 is an empty pattern.
    const mockPatternJson: PatternJson = {
      name: 'my_pattern',
      description: '',
      patternVertexSets: [],
      patternEdgeSets: [],
      patternWidgets: [],
      patternViews: [],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };
    const mockPattern1 = new Pattern(mockPatternJson);

    // Pattern 2 is a pattern with vertex set.
    const mockPatternVertexSetJson: PatternVertexSetJson = {
      vertices: [],
      isParam: false,
      ids: [],
      filters: [],
      aggregations: [],
      orderBy: [],
      hasLimit: false,
      limit: -1,
      output: false,
      existentialCheck: false,
      id: '1'
    };
    mockPatternJson.patternVertexSets.push(mockPatternVertexSetJson);
    const mockPattern2 = new Pattern(mockPatternJson);

    // Pattern 3 is a pattern with edge set.
    const mockPatternEdgeSetJson: PatternEdgeSetJson = {
      edges: [],
      repeatAtLeast: 0,
      repeatAtMost: 0,
      notExist: false,
      filters: [],
      output: false,
      existentialCheck: false,
      sourcePatternId: '0',
      targetPatternId: '0',
      id: '2'
    };
    mockPatternJson.patternVertexSets = [];
    mockPatternJson.patternEdgeSets.push(mockPatternEdgeSetJson);
    const mockPattern3 = new Pattern(mockPatternJson);

    // Pattern 4 is a pattern with widget.
    const mockPatternWidgetJson: PatternWidgetJson = {
      type: PathType.AllPaths,
      inputPatternIds: [],
      outputPatternIds: [],
      existentialCheck: false,
      output: false,
      id: '3'
    };
    mockPatternJson.patternEdgeSets = [];
    mockPatternJson.patternWidgets.push(mockPatternWidgetJson);
    const mockPattern4 = new Pattern(mockPatternJson);
    mockPatternJson.patternWidgets = [];

    // Pattern 5 is a pattern with pattern view.
    const mockPatternViewJson: PatternViewJson = {
      vertexSetIds: [],
      edgeSetIds: [],
      filters: [],
      orderBy: [],
      hasLimit: false,
      limit: -1,
      existentialCheck: false,
      output: false,
      id: '4'
    };
    mockPatternJson.patternViews.push(mockPatternViewJson);
    const mockPattern5 = new Pattern(mockPatternJson);

    // Pattern 6 is a pattern with vertex set, edge set, widget and pattern view.
    mockPatternJson.patternEdgeSets.push(mockPatternEdgeSetJson);
    mockPatternJson.patternVertexSets.push(mockPatternVertexSetJson);
    mockPatternJson.patternWidgets.push(mockPatternWidgetJson);
    const mockPattern6 = new Pattern(mockPatternJson);

    expect(mockPattern1.findComponentById('1')).toEqual(undefined);
    expect(mockPattern2.findComponentById('1').id).toEqual('1');
    expect(mockPattern3.findComponentById('2').id).toEqual('2');
    expect(mockPattern4.findComponentById('3').id).toEqual('3');
    expect(mockPattern5.findComponentById('4').id).toEqual('4');
    expect(mockPattern6.findComponentById('1').id).toEqual('1');
    expect(mockPattern6.findComponentById('2').id).toEqual('2');
    expect(mockPattern6.findComponentById('3').id).toEqual('3');
    expect(mockPattern6.findComponentById('4').id).toEqual('4');
  });

  it('should get pattern as a json type', () => {
    const mockPatternVertexSetJson: PatternVertexSetJson = {
      vertices: [],
      isParam: false,
      ids: [],
      filters: [],
      aggregations: [],
      orderBy: [],
      hasLimit: false,
      limit: -1,
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternEdgeSetJson: PatternEdgeSetJson = {
      edges: [],
      repeatAtLeast: 0,
      repeatAtMost: 0,
      notExist: false,
      filters: [],
      output: false,
      existentialCheck: false,
      sourcePatternId: '0',
      targetPatternId: '0',
      id: '2'
    };
    const mockPatternWidgetJson: PatternWidgetJson = {
      type: PathType.AllPaths,
      inputPatternIds: [],
      outputPatternIds: [],
      existentialCheck: false,
      output: false,
      id: '3'
    };
    const mockPatternViewJson: PatternViewJson = {
      vertexSetIds: [],
      edgeSetIds: [],
      filters: [],
      orderBy: [],
      hasLimit: false,
      limit: -1,
      existentialCheck: false,
      output: false,
      id: '4'
    };
    const mockPatternJson: PatternJson = {
      name: 'my_pattern',
      description: '',
      patternVertexSets: [mockPatternVertexSetJson, mockPatternVertexSetJson],
      patternEdgeSets: [mockPatternEdgeSetJson, mockPatternEdgeSetJson, mockPatternEdgeSetJson],
      patternWidgets: [
        mockPatternWidgetJson, mockPatternWidgetJson, mockPatternWidgetJson, mockPatternWidgetJson
      ],
      patternViews: [mockPatternViewJson],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };
    const mockJson = {
      name: 'my_pattern',
      description: '',
      patternVertexSets: [
        new PatternVertexSet(mockPatternVertexSetJson).toJson(),
        new PatternVertexSet(mockPatternVertexSetJson).toJson()
      ],
      patternEdgeSets: [
        new PatternEdgeSet(mockPatternEdgeSetJson).toJson(),
        new PatternEdgeSet(mockPatternEdgeSetJson).toJson(),
        new PatternEdgeSet(mockPatternEdgeSetJson).toJson()
      ],
      patternWidgets: [
        new PatternWidgetFactory().getPatternWidget(mockPatternWidgetJson).toJson(),
        new PatternWidgetFactory().getPatternWidget(mockPatternWidgetJson).toJson(),
        new PatternWidgetFactory().getPatternWidget(mockPatternWidgetJson).toJson(),
        new PatternWidgetFactory().getPatternWidget(mockPatternWidgetJson).toJson()
      ],
      patternViews: [
        new PatternView(mockPatternViewJson).toJson(),
      ],
      version: '',
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };
    const mockPattern = new Pattern(mockPatternJson);
    expect(mockPattern.toJson()).toEqual(mockJson);
  });

  it('should get pattern as GSQL', () => {
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
    const mockPattern = new Pattern(mockPatternJsonGlobal);

    const toGSQL = mockPattern.toGSQL(mockGraph);
    expect(
      toGSQL
        .includes('CREATE QUERY RecommendMovie(')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('set<vertex<person>> p, int a, double b, double c, string e, int r1, int r2')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes(') FOR GRAPH MyGraph API("v2") SYNTAX v2 {')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('WHERE (a == b) AND')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('(r1 != r2)')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('ACCUM')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('d = c')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('ORDER BY e ASC')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('PRINT')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('AS p')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('AS r')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('@@EdgeSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('VertexSet')
    ).toBeTruthy();
  });

  it('should generate GSQL code for a pattern that outputs both vertex pattern with LIMIT statement and edge pattern', () => {
    const mockPatternJson: PatternJson = {
      name: 'RecommendMovie',
      description: '',
      patternVertexSets: [
       {
         output: true,
         existentialCheck: false,
         vertices: ['person'],
         isParam: true,
         ids: [],
         id: '1',
         alias: 'p',
         filters: [
           {
             operands: [
               {
                 valueType: DataType.Int,
                 value: 'a',
                 type: 'IntParam'
               },
               {
                 value: 'b',
                 valueType: DataType.Real,
                 type: 'RealParam'
               }
             ],
             operator: RelationalOperator.Equal,
             type: 'ComparisonCondition'
           }
         ],
         aggregations: [
           {
             name: 'd',
             expression: {
              valueType: DataType.Real,
              value: 'c',
              type: 'RealParam'
             },
           }
         ],
         orderBy: [
           {
             asc: true,
             expression: {
              valueType: DataType.String,
              value: 'e',
              type: 'StringParam'
            }
           }
         ],
         hasLimit: false,
         limit: 10,
         paramName: 'p'
       },
       {
         output: true,
         existentialCheck: false,
         vertices: ['movie'],
         isParam: false,
         ids: [],
         filters: [],
         aggregations: [],
         orderBy: [],
         hasLimit: false,
         limit: 10,
         id: '2'
       }
      ],
      patternEdgeSets: [
        {
          output: true,
          existentialCheck: false,
          sourcePatternId: '1',
          targetPatternId: '2',
          edges: ['rate'],
          repeatAtLeast: 1,
          repeatAtMost: 1,
          alias: 'r',
          filters: [
            {
              operands: [
                {
                  value: 'r1',
                  valueType: DataType.Int,
                  type: 'IntParam'
                },
                {
                  value: 'r2',
                  valueType: DataType.Int,
                  type: 'IntParam'
                }
              ],
              operator: RelationalOperator.NotEqual,
              type: 'ComparisonCondition'
            }
          ],
          notExist: false
        }
      ],
      patternWidgets: [],
      patternViews: [],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    };
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
    const mockPattern = new Pattern(mockPatternJson);

    const toGSQL = mockPattern.toGSQL(mockGraph);
    expect(
      toGSQL
        .includes('SetAccum<edge> @@FinalEdgeSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('SetAccum<vertex<person>> @@FinalVertexSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('SetAccum<vertex<movie>> @@FinalVertexSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('POST-ACCUM @@FinalVertexSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('+= p')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('PRINT @@FinalEdgeSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('AS r')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('PRINT VertexSet')
    ).toBeTruthy();
    expect(
      toGSQL
        .includes('AS p')
    ).toBeTruthy();
  });

  it('should generate GSQL code for a pure pattern view', () => {
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
    const mockPattern = new Pattern({
      name: 'PurePatternView',
      description: '',
      patternVertexSets: [
        {
          vertices: ['person'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'p1',
          output: false,
          existentialCheck: false,
        },
        {
          vertices: ['movie'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'm1',
          output: false,
          existentialCheck: false,
        },
        {
          vertices: ['person'],
          isParam: false,
          ids: [],
          filters: [],
          aggregations: [],
          orderBy: [],
          hasLimit: false,
          limit: -1,
          id: 'p2',
          output: false,
          existentialCheck: false,
        },
      ],
      patternEdgeSets: [
        {
          edges: ['rate'],
          sourcePatternId: 'p1',
          targetPatternId: 'm1',
          repeatAtLeast: 1,
          repeatAtMost: 1,
          notExist: false,
          filters: [],
          id: 'e1',
          output: false,
          existentialCheck: false,
        },
        {
          edges: ['rate'],
          sourcePatternId: 'p2',
          targetPatternId: 'm1',
          repeatAtLeast: 1,
          repeatAtMost: 1,
          notExist: false,
          filters: [],
          id: 'e2',
          output: false,
          existentialCheck: false,
        }
      ],
      patternWidgets: [],
      patternViews: [
        {
          vertexSetIds: ['p1', 'm1'],
          edgeSetIds: ['e1'],
          filters: [],
          orderBy: [],
          hasLimit: false,
          limit: 10,
          id: 'v1',
          alias: 'myView',
          output: true,
          existentialCheck: false,
        }
      ],
      filters: [],
      aggregations: [],
      postAggregationFilters: [],
      orderBy: [],
      skip: 0,
      limit: -1,
    });

    const toGSQL = mockPattern.toGSQL(mockGraph);

    expect(
      toGSQL
        .includes('CREATE QUERY PurePatternView(')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes(') FOR GRAPH MyGraph API("v2") SYNTAX v2 {')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('TYPEDEF TUPLE<')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('> Tuple_myView;')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('SetAccum<vertex<person>> @@myView_')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('SetAccum<vertex<movie>> @@myView_')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('SetAccum<Tuple_myView> @@Result_myView;')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('ACCUM @@Result_myView += Tuple_myView(')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('FOREACH result IN @@Result_myView DO')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('END;')
    ).toBeTruthy();

    expect(
      toGSQL
        .includes('PRINT myView_')
    ).toBeTruthy();

    // TODO: For 2nd iteration: generate GSQL code for filter on pattern view.
    // TODO: For 3rd iteration: generate GSQL code for order-by and limit on pattern view.
  });

  it('should collect parameters', () => {
    const mockPattern = new Pattern(mockPatternJsonGlobal);

    const parameters = mockPattern.collectParameters();
    expect(parameters.length).toEqual(10);
    expect(parameters[0]).toEqual(
      new PatternParam('p', true, 'vertex', 'person')
    );
    expect(parameters[1]).toEqual(
      new PatternParam('a', false, 'int', undefined)
    );
    expect(parameters[2]).toEqual(
      new PatternParam('b', false, 'double', undefined)
    );
    expect(parameters[3]).toEqual(
      new PatternParam('c', false, 'double', undefined)
    );
    expect(parameters[4]).toEqual(
      new PatternParam('e', false, 'string', undefined)
    );
    expect(parameters[5]).toEqual(
      new PatternParam('r1', false, 'int', undefined)
    );
    expect(parameters[6]).toEqual(
      new PatternParam('r2', false, 'int', undefined)
    );
    expect(parameters[7]).toEqual(
      new PatternParam('f', false, 'int', undefined)
    );
    expect(parameters[8]).toEqual(
      new PatternParam('g', false, 'double', undefined)
    );
    expect(parameters[9]).toEqual(
      new PatternParam('h', false, 'string', undefined)
    );
  });

  describe('unsupported features check', () => {
    it('should not throw a GraphStudio error when there is no actual data in the json value for an unsupported json key', () => {
      const mockSupportedPatternJson = {
        name: 'my_pattern',
        patternVertexSets: [],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        mockFeature1: undefined,
        mockFeature2: null,
        mockFeature3: [],
        mockFeature4: {},
        skip: 0,
        limit: -1,
        orderBy: [],
        description: ''
      };
      expect(() => new Pattern(mockSupportedPatternJson, new Graph()))
        .not.toThrow(new GraphStudioError('Pattern my_pattern contains unsupported features. ' +
          'Did you import a solution from a higher version of GraphStudio?'));
    });

    it('should throw a Graphstudio error when the pattern json contains unsupported keys', () => {
      const mockUnsupportedPatternJson = {
        name: 'my_pattern',
        patternVertexSets: [],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [],
        description: '',
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
        newFeature1: {
          key1: 'newFeature1',
          key2: {
            key21: [1, 2, 3, 4],
            key22: 'key22',
            key23: ['1', '2', '3', '4']
          },
          key3: false,
          key4: -1
        },
      };
      const error = new GraphStudioError('Pattern my_pattern contains unsupported features. ' +
        'Did you import a solution from a higher version of GraphStudio?');

      mockUnsupportedPatternJson['mockFeature'] = 1;
      expect(() => new Pattern(mockUnsupportedPatternJson, new Graph())).toThrow(error);
      mockUnsupportedPatternJson['mockFeature'] = '1';
      expect(() => new Pattern(mockUnsupportedPatternJson, new Graph())).toThrow(error);
      mockUnsupportedPatternJson['mockFeature'] = [1];
      expect(() => new Pattern(mockUnsupportedPatternJson, new Graph())).toThrow(error);
      mockUnsupportedPatternJson['mockFeature'] = {'1': 1};
      expect(() => new Pattern(mockUnsupportedPatternJson, new Graph())).toThrow(error);
    });
  });

  describe('semantic check', () => {
    it('should report empty pattern error when the pattern is empty', () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);

      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [
          {
            message: 'The pattern is empty.',
            involvedPatternEntities: []
          }
        ]
      });
    });

    it('should refer to non-exist vertex type(s) in schema when there is no vertex in pattern', () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person1'],
            isParam: false,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          }
        ],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [{
          message: 'Vertex type person1 does not exist in the graph schema.',
          involvedPatternEntities: ['1']
        }]
      });
    });

    it('should refer to non-exist edge type(s) in schema when there is no edge in pattern', () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person'],
            isParam: false,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '2',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          }
        ],
        patternEdgeSets: [
          {
            output: false,
            existentialCheck: false,
            sourcePatternId: '1',
            targetPatternId: '2',
            edges: ['rate1'],
            repeatAtLeast: 1,
            repeatAtMost: 1,
            filters: [],
            notExist: false,
            id: '3'
          }
        ],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [{
          message: 'Edge type rate1 does not exist in the graph schema.',
          involvedPatternEntities: ['3']
        }]
      });
    });

    describe('Expressions on vertex pattern or edge pattern', () => {
      it('should report error when the edge pattern filters has error', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [
            {
              output: true,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: '1',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['movie'],
              isParam: false,
              ids: [],
              id: '2',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            }
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: '1',
              targetPatternId: '2',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [
                {
                  operands: [
                    {
                      valueType: DataType.Int,
                      value: '',
                      type: 'IntParam'
                    },
                    {
                      valueType: DataType.Int,
                      value: '',
                      type: 'IntParam'
                    }
                  ],
                  operator: RelationalOperator.NotEqual,
                  type: 'ComparisonCondition'
                }
              ],
              notExist: false,
              id: '3'
            }
          ],
          patternWidgets: [],
          patternViews: [],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };
        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [{
            message: 'One edge pattern has errors in its filters.',
            involvedPatternEntities: ['3']
          }]
        });
      });

      it('should report error when the vertex pattern filters/aggregations/order-bys or ' +
        'edge pattern filters contain unsupported features', () => {
        const mockUnsupportedPatternJson: PatternJson = {
          name: 'UnsupportedPatternWithNestedExpression',
          description: '',
          patternVertexSets: [
            {
              output: true,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: '1',
              filters: [
                {
                  operands: [
                    {
                      value: 0,
                      type: 'IntConstant'
                    },
                    {
                      operands: [
                        {
                          value: 2,
                          type: 'IntConstant'
                        }
                      ],
                      operator: FunctionType.DistinctCount,
                      type: 'FunctionExpression'
                    }
                  ],
                  operator: <any>'NewOperator',
                  type: 'ComparisonCondition'
                }
              ],
              aggregations: [
                {
                  name: 'nestedExpression',
                  expression: {
                    operands: [
                      {
                        operands: [
                          {
                            value: 2,
                            type: 'IntConstant'
                          },
                          {
                            value: 3,
                            type: 'IntConstant'
                          }
                        ],
                        operator: <any>'NewOperator',
                        type: 'ArithmeticExpression'
                      },
                      {
                        value: 2,
                        type: 'RealConstant'
                      }
                    ],
                    operator: ArithmeticOperator.Plus,
                    type: 'ArithmeticExpression'
                  },
                }
              ],
              orderBy: [
                {
                  asc: true,
                  expression: {
                    operands: [
                      {
                        value: 2,
                        type: 'RealConstant'
                      }
                    ],
                    operator: <any>'NewOperator',
                    type: 'FunctionExpression'
                  }
                }
              ],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['movie'],
              isParam: false,
              ids: [],
              id: '2',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            }
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: '1',
              targetPatternId: '2',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [
                {
                  operands: [
                    {
                      value: 0,
                      type: 'IntConstant'
                    },
                    {
                      operands: [
                        {
                          value: 2,
                          type: 'IntConstant'
                        }
                      ],
                      operator: FunctionType.DistinctCount,
                      type: 'FunctionExpression'
                    }
                  ],
                  operator: <any>'NewOperator',
                  type: 'ComparisonCondition'
                }
              ],
              notExist: false,
              id: '3'
            }
          ],
          patternWidgets: [],
          patternViews: [],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };

        const mockUnsupportedPattern = new Pattern(mockUnsupportedPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockUnsupportedPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [
            {
              message: 'One edge pattern contains unsupported features in its filters. ' +
                'Did you import a solution from a higher version of GraphStudio?',
              involvedPatternEntities: ['3']
            },
            {
              message: 'One vertex pattern contains unsupported features in its filters, attachments and order-by expressions. ' +
                'Did you import a solution from a higher version of GraphStudio?',
              involvedPatternEntities: ['1']
            }
          ]
        });
      });

      it('should report error when the vertex pattern filters/aggregations/orderby has error', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [
            {
              output: true,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: '1',
              filters: [
                {
                  operands: [
                    {
                      valueType: DataType.Int,
                      value: '.d',
                      type: 'IntParam'
                    },
                    {
                      value: 'b',
                      valueType: DataType.Real,
                      type: 'RealParam'
                    }
                  ],
                  operator: RelationalOperator.Equal,
                  type: 'ComparisonCondition'
                }
              ],
              aggregations: [
                {
                  name: '--',
                  expression: {
                   valueType: DataType.Real,
                   value: '',
                   type: 'RealParam'
                  },
                }
              ],
              orderBy: [
                {
                  asc: true,
                  expression: {
                   valueType: DataType.String,
                   value: '12',
                   type: 'StringParam'
                 }
                }
              ],
              hasLimit: false,
              limit: -1
            }
          ],
          patternEdgeSets: [],
          patternWidgets: [],
          patternViews: [],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };
        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [{
            message: 'One vertex pattern has errors in its filters, attachments and order-by expressions.',
            involvedPatternEntities: ['1']
          }]
        });
      });
    });

    it('should report error if all the input and output vertex pattern of a widget not have same vertex type',
    () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person'],
            isParam: false,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '2',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['person'],
            isParam: false,
            ids: [],
            id: '4',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '5',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          }
        ],
        patternEdgeSets: [
          {
            output: false,
            existentialCheck: false,
            sourcePatternId: '1',
            targetPatternId: '2',
            edges: ['rate'],
            repeatAtLeast: 1,
            repeatAtMost: 1,
            filters: [],
            notExist: false,
            id: '3'
          }
        ],
        patternWidgets: [
          {
            id: '6',
            output: false,
            existentialCheck: false,
            inputPatternIds: ['1', '1'],
            outputPatternIds: ['5'],
            type: VertexSetOperation.Intersect,
          }
        ],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [{
          message: 'All the input and output vertex patterns of a widget must have same vertex type.',
          involvedPatternEntities: ['6']
        }]
      });
    });

    it('should report error if edge type not-exists between source and target vertex pattern', () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person'],
            isParam: false,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '2',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          }
        ],
        patternEdgeSets: [
          {
            output: false,
            existentialCheck: false,
            sourcePatternId: '1',
            targetPatternId: '2',
            edges: ['rate'],
            repeatAtLeast: 1,
            repeatAtMost: 1,
            filters: [],
            notExist: false,
            id: '3'
          },
          {
            output: false,
            existentialCheck: false,
            sourcePatternId: '1',
            targetPatternId: '1',
            edges: ['rate'],
            repeatAtLeast: 1,
            repeatAtMost: 1,
            filters: [],
            notExist: false,
            id: '4'
          }
        ],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [{
          message: 'Edge type rate doesn\'t exist from vertex type person to vertex type person.',
          involvedPatternEntities: ['4']
        }]
      });
    });

    it('should report error if there is a circle dependencies among the connected components', () => {
      const mockPatternJson: PatternJson = {
        name: 'my_pattern',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person'],
            isParam: false,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '2',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '3',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '4',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          },
          {
            output: false,
            existentialCheck: false,
            vertices: ['movie'],
            isParam: false,
            ids: [],
            id: '5',
            filters: [],
            aggregations: [],
            orderBy: [],
            hasLimit: false,
            limit: -1
          }
        ],
        patternEdgeSets: [
          {
            output: false,
            existentialCheck: false,
            sourcePatternId: '1',
            targetPatternId: '2',
            edges: ['rate'],
            repeatAtLeast: 1,
            repeatAtMost: 1,
            filters: [],
            notExist: false,
            id: '6'
          }
        ],
        patternWidgets: [
          {
            id: '7',
            output: false,
            existentialCheck: false,
            inputPatternIds: ['2', '3'],
            outputPatternIds: ['4'],
            type: VertexSetOperation.Intersect
          },
          {
            id: '8',
            output: false,
            existentialCheck: false,
            inputPatternIds: ['3', '4'],
            outputPatternIds: ['5'],
            type: VertexSetOperation.Intersect
          },
          {
            id: '9',
            output: false,
            existentialCheck: false,
            inputPatternIds: ['4', '5'],
            outputPatternIds: ['2'],
            type: VertexSetOperation.Intersect
          }
        ],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [{
          message: 'Widget patterns cause circular dependencies among vertex patterns.',
          involvedPatternEntities: ['7', '8', '9']
        }]
      });
    });

    describe('Pattern view', () => {
      it('should report error when the pattern view filter/orderby has error', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [],
          patternEdgeSets: [],
          patternWidgets: [],
          patternViews: [
            {
              vertexSetIds: ['v1', 'v2', 'v3'],
              edgeSetIds: ['e1', 'e2'],
              filters: [
                {
                  operands: [
                    {
                      valueType: DataType.Int,
                      value: '1',
                      type: 'IntParam'
                    },
                    {
                      value: 'a',
                      valueType: DataType.String,
                      type: 'StringParam'
                    }
                  ],
                  operator: RelationalOperator.Equal,
                  type: 'ComparisonCondition'
                }
              ],
              orderBy: [
                {
                  asc: true,
                  expression: {
                   valueType: DataType.String,
                   value: '12',
                   type: 'StringParam'
                 }
                }
              ],
              hasLimit: false,
              limit: 10,
              id: 'view1',
              alias: 'view1',
              output: true,
              existentialCheck: false,
            },
          ],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };
        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [
            {
              message: 'One pattern view has errors in its order-by expressions.',
              involvedPatternEntities: ['view1'],
            },
            {
              message: 'The pattern view has errors in its filters and order-by expressions.',
              involvedPatternEntities: ['view1']
            }
          ]
        });
      });

      it ('should report error if pattern views overlap with each other', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v1',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v2',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['movie'],
              isParam: false,
              ids: [],
              id: 'v3',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: 'v1',
              targetPatternId: 'v3',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [],
              notExist: false,
              id: 'e1'
            },
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: 'v2',
              targetPatternId: 'v3',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [],
              notExist: false,
              id: 'e2'
            },
          ],
          patternWidgets: [],
          patternViews: [
            {
              vertexSetIds: ['v1', 'v3'],
              edgeSetIds: ['e1'],
              filters: [],
              orderBy: [],
              hasLimit: false,
              limit: 10,
              id: 'view1',
              alias: 'view1',
              output: true,
              existentialCheck: false,
            },
            {
              vertexSetIds: ['v2', 'v3'],
              edgeSetIds: ['e2'],
              filters: [],
              orderBy: [],
              hasLimit: false,
              limit: 10,
              id: 'view2',
              alias: 'view2',
              output: true,
              existentialCheck: false,
            },
          ],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };

        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [{
            message: 'We currently do not support pattern views overlap with each other.',
            involvedPatternEntities: ['view1', 'view2']
          }]
        });
      });

      it ('should report error if one pattern view across more than one connected components', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v1',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v2',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v3',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v4',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v5',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
          ],
          patternEdgeSets: [],
          patternWidgets: [
            {
              id: 'w1',
              output: false,
              existentialCheck: false,
              inputPatternIds: ['v1', 'v2'],
              outputPatternIds: ['v3'],
              type: VertexSetOperation.Intersect,
            },
            {
              id: 'w1',
              output: false,
              existentialCheck: false,
              inputPatternIds: ['v3', 'v4'],
              outputPatternIds: ['v5'],
              type: VertexSetOperation.Intersect,
            },
          ],
          patternViews: [
            {
              vertexSetIds: ['v1', 'v5'],
              edgeSetIds: [],
              filters: [],
              orderBy: [],
              hasLimit: false,
              limit: 10,
              id: 'view1',
              alias: 'view1',
              output: true,
              existentialCheck: false,
            },
          ],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };

        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [{
            message: 'We currently do not support one pattern view across different connected components.',
            involvedPatternEntities: ['view1']
          }]
        });
      });

      it ('should report error if pattern view does not have an alias', () => {
        const mockPatternJson: PatternJson = {
          name: 'my_pattern',
          description: '',
          patternVertexSets: [
            {
              output: false,
              existentialCheck: false,
              vertices: ['person'],
              isParam: false,
              ids: [],
              id: 'v1',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
            {
              output: false,
              existentialCheck: false,
              vertices: ['movie'],
              isParam: false,
              ids: [],
              id: 'v3',
              filters: [],
              aggregations: [],
              orderBy: [],
              hasLimit: false,
              limit: -1
            },
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: 'v1',
              targetPatternId: 'v3',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [],
              notExist: false,
              id: 'e1'
            },
          ],
          patternWidgets: [],
          patternViews: [
            {
              vertexSetIds: ['v1', 'v3'],
              edgeSetIds: ['e1'],
              filters: [],
              orderBy: [],
              hasLimit: false,
              limit: 10,
              id: 'view1',
              output: true,
              existentialCheck: false,
            },
          ],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };

        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [{
            message: 'Each pattern view should have its name.',
            involvedPatternEntities: ['view1']
          }]
        });
      });
    });

    describe('should report error if there are parameters with same name but different data types', () => {
      it('such as parameters with vertex or edge', () => {
        const mockPatternJson: PatternJson = {
          name: 'RecommendMovie',
          description: '',
          patternVertexSets: [
           {
             output: true,
             existentialCheck: false,
             vertices: ['person'],
             isParam: true,
             ids: [],
             id: '1',
             filters: [
               {
                 operands: [
                   {
                     valueType: DataType.Int,
                     value: 'rate',
                     type: 'IntParam'
                   },
                   {
                     value: 'person',
                     valueType: DataType.Real,
                     type: 'RealParam'
                   }
                 ],
                 operator: RelationalOperator.Equal,
                 type: 'ComparisonCondition'
               }
             ],
             aggregations: [
               {
                 name: 'd',
                 expression: {
                  valueType: DataType.Real,
                  value: 'person',
                  type: 'RealParam'
                 },
               }
             ],
             orderBy: [
               {
                 asc: true,
                 expression: {
                  valueType: DataType.String,
                  value: 'movie',
                  type: 'StringParam'
                }
               }
             ],
             hasLimit: false,
             limit: 10,
             paramName: 'p'
           },
           {
             output: false,
             existentialCheck: false,
             vertices: ['movie'],
             isParam: false,
             ids: [],
             filters: [],
             aggregations: [],
             orderBy: [],
             hasLimit: false,
             limit: 10,
             id: '2'
           }
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: '1',
              targetPatternId: '2',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [
                {
                  operands: [
                    {
                      value: 'rate',
                      valueType: DataType.Int,
                      type: 'IntParam'
                    },
                    {
                      value: 'rate',
                      valueType: DataType.Int,
                      type: 'IntParam'
                    }
                  ],
                  operator: RelationalOperator.NotEqual,
                  type: 'ComparisonCondition'
                }
              ],
              notExist: false
            }
          ],
          patternWidgets: [],
          patternViews: [],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };
        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [
            {
              message: 'Parameter cannot be named as rate because it is an edge type.',
              involvedPatternEntities: []
            },
            {
              message: 'Parameter cannot be named as person because it is a vertex type.',
              involvedPatternEntities: []
            },
            {
              message: 'Parameter cannot be named as movie because it is a vertex type.',
              involvedPatternEntities: []
            }
          ]
        });
      });

      it('and should report error if there was same parameter appeared as different parameter', () => {
        const mockPatternJson: PatternJson = {
          name: 'RecommendMovie',
          description: '',
          patternVertexSets: [
           {
             output: true,
             existentialCheck: false,
             vertices: ['person'],
             isParam: true,
             ids: [],
             id: '1',
             filters: [
               {
                 operands: [
                   {
                     valueType: DataType.Int,
                     value: 'a',
                     type: 'IntParam'
                   },
                   {
                     value: 'a',
                     valueType: DataType.Real,
                     type: 'RealParam'
                   }
                 ],
                 operator: RelationalOperator.Equal,
                 type: 'ComparisonCondition'
               }
             ],
             aggregations: [
               {
                 name: 'd',
                 expression: {
                  valueType: DataType.Real,
                  value: 'a',
                  type: 'RealParam'
                 },
               }
             ],
             orderBy: [
               {
                 asc: true,
                 expression: {
                  valueType: DataType.String,
                  value: 'a',
                  type: 'StringParam'
                }
               }
             ],
             hasLimit: false,
             limit: 10,
             paramName: 'a'
           },
           {
             output: false,
             existentialCheck: false,
             vertices: ['movie'],
             isParam: false,
             ids: [],
             filters: [],
             aggregations: [],
             orderBy: [],
             hasLimit: false,
             limit: 10,
             id: '2'
           }
          ],
          patternEdgeSets: [
            {
              output: false,
              existentialCheck: false,
              sourcePatternId: '1',
              targetPatternId: '2',
              edges: ['rate'],
              repeatAtLeast: 1,
              repeatAtMost: 1,
              filters: [
                {
                  operands: [
                    {
                      value: 'a',
                      valueType: DataType.Int,
                      type: 'IntParam'
                    },
                    {
                      value: 'a',
                      valueType: DataType.Int,
                      type: 'IntParam'
                    }
                  ],
                  operator: RelationalOperator.NotEqual,
                  type: 'ComparisonCondition'
                }
              ],
              notExist: false
            }
          ],
          patternWidgets: [],
          patternViews: [
            {
              vertexSetIds: ['1', '2'],
              edgeSetIds: [],
              filters: [
                {
                  operands: [
                    {
                      valueType: DataType.Datetime,
                      value: 'a',
                      type: 'DatetimeParam'
                    },
                    {
                      value: 'a',
                      valueType: DataType.Datetime,
                      type: 'DatetimeParam'
                    }
                  ],
                  operator: RelationalOperator.Equal,
                  type: 'ComparisonCondition'
                }
              ],
              orderBy: [
                {
                  asc: true,
                  expression: {
                   valueType: DataType.Bool,
                   value: 'a',
                   type: 'BoolParam'
                 }
                }
              ],
              hasLimit: false,
              limit: 10,
              id: 'view1',
              alias: 'view1',
              output: true,
              existentialCheck: false,
            },
          ],
          filters: [],
          aggregations: [],
          postAggregationFilters: [],
          orderBy: [],
          skip: 0,
          limit: -1,
        };
        const mockPattern = new Pattern(mockPatternJson);
        const mockGraph = new Graph();
        mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
        expect(mockPattern.semanticCheck(mockGraph)).toEqual({
          success: false,
          errors: [
            {
              message: 'One pattern view contains unsupported features in its order-by expressions. Did you import a solution from a higher version of GraphStudio?',
              involvedPatternEntities: ['view1']
            },
            {
              message: 'Parameter a cannot be int, person vertex, double, string, datetime and bool at the same time.',
              involvedPatternEntities: []
            }
          ]
        });
      });
    });

    it('should report error if parameter name duplicated with aliases', () => {
      const mockPatternJson: PatternJson = {
        name: 'RecommendMovie',
        description: '',
        patternVertexSets: [
          {
            output: true,
            existentialCheck: false,
            vertices: ['person'],
            isParam: true,
            ids: [],
            id: '1',
            filters: [],
            aggregations: [],
            orderBy: [ ],
            hasLimit: false,
            limit: 10,
            paramName: 'p',
            alias: 'p'
          }
        ],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [
          {
            vertexSetIds: ['1'],
            edgeSetIds: [],
            filters: [],
            orderBy: [],
            hasLimit: false,
            limit: 10,
            id: 'view1',
            alias: 'p',
            output: true,
            existentialCheck: false,
          },
        ],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };

      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [
          {
            message: 'Parameter name p duplicates with a vertex pattern, edge pattern or pattern view name.',
            involvedPatternEntities: []
          }
        ]
      });
    });

    it('should report error if there is no output', () => {
      const mockPatternJson: PatternJson = {
        name: 'RecommendMovie',
        description: '',
        patternVertexSets: [
         {
           output: false,
           existentialCheck: false,
           vertices: ['person'],
           isParam: true,
           ids: [],
           id: '1',
           filters: [],
           aggregations: [],
           orderBy: [ ],
           hasLimit: false,
           limit: 10,
           paramName: 'p'
         }
        ],
        patternEdgeSets: [],
        patternWidgets: [],
        patternViews: [],
        filters: [],
        aggregations: [],
        postAggregationFilters: [],
        orderBy: [],
        skip: 0,
        limit: -1,
      };
      const mockPattern = new Pattern(mockPatternJson);
      const mockGraph = new Graph();
      mockGraph.loadFromGSQLJson(mockGSQLGraphJsonGlobal);
      expect(mockPattern.semanticCheck(mockGraph)).toEqual({
        success: false,
        errors: [
          {
            message: 'Should output something in the pattern.',
            involvedPatternEntities: []
          }
        ]
      });
    });
  });
});
