import { BaseExpression, ExpressionJson, parseExpr<PERSON><PERSON>, <PERSON><PERSON>, Atom<PERSON>son } from '../expression';
import { Graph } from '../topology';
import { ValidateR<PERSON>ult, FormatValidator } from '../utils';

export interface PatternAggregationJson {
  name: string;
  expression: AtomJson | ExpressionJson;
}

export class PatternAggregation {
  name: string;
  expression: Atom | BaseExpression;

  constructor(data: PatternAggregationJson, schema?: Graph) {
    this.name = data.name;
    this.expression = <Atom | BaseExpression>parseExprJson(data.expression, schema);
  }

  toJson(): PatternAggregationJson {
    return {
      name: this.name,
      expression: this.expression.toJson()
    };
  }

  /**
   * Check whether aggergation name is valid.
   *
   * @param {string[]} otherAggNames
   * @param {Graph} schema
   * @returns {ValidateResult}
   * @memberof PatternAggregation
   */
  checkAggregationName(otherAggNames: string[], schema: Graph, aliasSchema?: Graph): ValidateResult {
    if (!this.name) {
      return {
        success: false,
        message: 'Name cannot be empty.'
      };
    }
    // Check name duplication.
    if (otherAggNames.filter(aggName => aggName === this.name).length > 1) {
      return {
        success: false,
        message: 'Name cannot be duplicated.'
      };
    }
    // Check name cannot be same as attribute name.
    if (schema) {
      const attrNames: string[] = [];
      schema.vertexTypes.forEach(
        vType => attrNames.push(...vType.attributes.map(attr => attr.name))
      );
      schema.edgeTypes.forEach(
        eType => attrNames.push(...eType.attributes.map(attr => attr.name))
      );
      if (attrNames.includes(this.name)) {
        return {
          success: false,
          message: 'Cannot use an attribute name.'
        };
      }
    }

    if (aliasSchema) {
      const vertexAndEdgeAlias: string[] = [];
      aliasSchema.vertexTypes.forEach(
        vType => vertexAndEdgeAlias.push(vType.name)
      );
      aliasSchema.edgeTypes.forEach(
        eType => vertexAndEdgeAlias.push(eType.name)
      );
      if (vertexAndEdgeAlias.includes(this.name)) {
        return {
          success: false,
          message: 'Cannot use an alias of vertex or edge.'
        };
      }
    }

    return FormatValidator.isName(this.name);
  }
}
