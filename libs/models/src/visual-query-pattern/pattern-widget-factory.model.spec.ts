import { PatternExpansion, PatternExpansionJson } from './pattern-expansion.model';
import { PatternPath, PatternPathJson } from './pattern-path.model';
import { PatternVertexSetOperation, PatternVertexSetOperationJson } from './pattern-vertex-set-operation.model';
import { PatternWidgetJson, ExpansionType, PathType, VertexSetOperation } from './pattern-widget.model';
import { PatternWidgetFactory } from './pattern-widget-factory.model';

describe('PatternWidgetFactoryModel', () => {
  it('should get widget pattern', () => {
    const mockPatternWidgetJson1: PatternWidgetJson = {
      type: PathType.AllPaths,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson2: PatternWidgetJson = {
      type: PathType.SingleShortestPath,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson3: PatternWidgetJson = {
      type: PathType.AllShortestPaths,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson4: PatternWidgetJson = {
      type: ExpansionType.OneStepExpansion,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson5: PatternWidgetJson = {
      type: VertexSetOperation.Intersect,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson6: PatternWidgetJson = {
      type: VertexSetOperation.Subtract,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternWidgetJson7: PatternWidgetJson = {
      type: VertexSetOperation.Union,
      inputPatternIds: ['1', '2', '3'],
      outputPatternIds: ['4', '5', '6'],
      output: false,
      existentialCheck: false,
      id: '1'
    };

    const mockPatternWidgetFactory = new PatternWidgetFactory();
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson1)).toEqual(
      new PatternPath(<PatternPathJson>mockPatternWidgetJson1)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson2)).toEqual(
      new PatternPath(<PatternPathJson>mockPatternWidgetJson2)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson3)).toEqual(
      new PatternPath(<PatternPathJson>mockPatternWidgetJson3)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson4)).toEqual(
      new PatternExpansion(<PatternExpansionJson>mockPatternWidgetJson4)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson5)).toEqual(
      new PatternVertexSetOperation(<PatternVertexSetOperationJson>mockPatternWidgetJson5)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson6)).toEqual(
      new PatternVertexSetOperation(<PatternVertexSetOperationJson>mockPatternWidgetJson6)
    );
    expect(mockPatternWidgetFactory.getPatternWidget(mockPatternWidgetJson7)).toEqual(
      new PatternVertexSetOperation(<PatternVertexSetOperationJson>mockPatternWidgetJson7)
    );
  });
});
