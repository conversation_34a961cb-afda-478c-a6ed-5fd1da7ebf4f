
import { PatternEntity, PatternEntityJson } from './pattern-entity.model';

export enum PathType {
  SingleShortestPath = 'singleShortestPath',
  AllShortestPaths = 'allShortestPaths',
  AllPaths = 'allPaths'
}

export enum ExpansionType {
  OneStepExpansion = 'oneStepExpansion'
}

export enum VertexSetOperation {
  Intersect = 'intersect',
  Union = 'union',
  Subtract = 'subtract',
  Within = 'within'
}

export interface PatternWidgetJson extends PatternEntityJson {
  type: PathType | ExpansionType | VertexSetOperation;
  inputPatternIds: string[];
  outputPatternIds: string[];
}

export abstract class PatternWidget extends PatternEntity {
  type: PathType | ExpansionType | VertexSetOperation;
  inputPatternIds: string[];
  outputPatternIds: string[];

  constructor(data: PatternWidgetJson) {
    super(data);
    this.inputPatternIds = data.inputPatternIds;
    this.outputPatternIds = data.outputPatternIds;
    this.type = data.type;
  }

  toJson(): PatternWidgetJson {
    return {
      ...super.toJson(),
      inputPatternIds: this.inputPatternIds,
      outputPatternIds: this.outputPatternIds,
      type: this.type
    };
  }
}
