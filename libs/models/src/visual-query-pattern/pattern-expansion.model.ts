import { ExpansionType, PatternWidget, PatternWidgetJson } from './pattern-widget.model';

export interface PatternExpansion<PERSON>son extends PatternWidgetJson {
  type: ExpansionType;
  limit: number;
  repeatAtLeast: number;
  repeatAtMost: number;
}

export class PatternExpansion extends PatternWidget {
  type: ExpansionType;
  limit: number;
  repeatAtLeast: number;
  repeatAtMost: number;

  constructor(data: PatternExpansionJson) {
    super(data);
    this.type = data.type;
    this.limit = data.limit;
    this.repeatAtLeast = data.repeatAtLeast || 1;
    this.repeatAtMost = data.repeatAtMost || 1;
  }

  toJson(): PatternExpansionJson {
    return {
      ...super.toJson(),
      type: this.type,
      limit: this.limit,
      repeatAtLeast: this.repeatAtLeast,
      repeatAtMost: this.repeatAtMost
    };
  }
}
