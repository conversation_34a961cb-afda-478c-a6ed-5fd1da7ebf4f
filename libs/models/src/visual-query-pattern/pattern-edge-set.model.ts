import { ConditionJson, BaseCondition, parseExprJson } from '../expression';
import { Graph } from '../topology';

import { PatternEntity, PatternEntityJson } from './pattern-entity.model';
import { Linkable, PatternLink } from './pattern-link.interface';

export interface PatternEdgeSetJson extends PatternEntityJson, PatternLink {
  edges: string[];
  repeatAtLeast: number;
  repeatAtMost: number;
  notExist: boolean;
  filters: ConditionJson[];
}

export class PatternEdgeSet extends Linkable(PatternEntity) {
  edges: string[];
  repeatAtLeast: number;
  repeatAtMost: number;
  notExist: boolean;
  filters: BaseCondition[];

  constructor(data: PatternEdgeSetJson, schema?: Graph) {
    super(data);
    this.sourcePatternId = data.sourcePatternId;
    this.targetPatternId = data.targetPatternId;
    this.edges = data.edges || [];
    this.repeatAtLeast = data.repeatAtLeast === undefined ? 1 : data.repeatAtLeast;
    this.repeatAtMost = data.repeatAtMost || 1;
    this.notExist = data.notExist || false;
    this.filters = data.filters
      ? data.filters.map(filterJson => <BaseCondition>parseExprJson(filterJson, schema))
      : [];
  }

  toJson(): PatternEdgeSetJson {
    return {
      ...super.toJson(),
      sourcePatternId: this.sourcePatternId,
      targetPatternId: this.targetPatternId,
      edges: this.edges.slice(),
      repeatAtLeast: this.repeatAtLeast,
      repeatAtMost: this.repeatAtMost,
      notExist: this.notExist,
      filters: this.filters.map(filter => filter.toJson()),
    };
  }
}
