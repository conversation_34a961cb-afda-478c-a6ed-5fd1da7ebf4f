import { DataType } from '../data';
import { Graph, GSQLGraphJson } from '../topology';

import { PatternAggregation, PatternAggregationJson } from './pattern-aggregation.model';

describe('PatternAggregationModel', () => {
  it('should get a aggregation pattern as a json type', () => {
    const mockPatternAggregationJson: PatternAggregationJson = {
      name: 'my_aggregation',
      expression: {
        type: 'RealParam',
        valueType: DataType.Real,
        value: 'value'
      }
    };
    const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);

    expect(mockPatternAggregation.toJson()).toEqual({
      name: 'my_aggregation',
      expression: {
        'type': 'RealParam',
        'value': 'value'
      }
    });
  });

  it('should report error if name was empty', () => {
    const mockPatternAggregationJson: PatternAggregationJson = {
      name: '',
      expression: {
        type: 'RealParam',
        valueType: DataType.Real,
        value: 'value'
      }
    };
    const mockOtherAggNames = ['my_aggregation', 'other_aggregation', 'my_aggregation'];
    const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);

    expect(mockPatternAggregation.checkAggregationName(mockOtherAggNames, new Graph())).toEqual({
      success: false,
      message: 'Name cannot be empty.'
    });
  });

  it('should report error if name duplicated', () => {
    const mockPatternAggregationJson: PatternAggregationJson = {
      name: 'my_aggregation',
      expression: {
        type: 'RealParam',
        valueType: DataType.Real,
        value: 'value'
      }
    };
    const mockOtherAggNames = ['my_aggregation', 'other_aggregation', 'my_aggregation'];
    const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);

    expect(mockPatternAggregation.checkAggregationName(mockOtherAggNames, new Graph())).toEqual({
      success: false,
      message: 'Name cannot be duplicated.'
    });
  });

  it('should report error if name was same as attribute name', () => {
    const mockPatternAggregationJson: PatternAggregationJson = {
      name: 'abc',
      expression: {
        type: 'RealParam',
        valueType: DataType.Real,
        value: 'value'
      }
    };
    const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);
    const mockGSQLGraphJson: GSQLGraphJson = {
      GraphName: 'MyGraph',
      VertexTypes: [
        {
          Name: 'person',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'abc',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ],
          Config: {
            STATS: 'OUTDEGREE_BY_EDGETYPE',
            PRIMARY_ID_AS_ATTRIBUTE: false
          },
          IsLocal: true
        },
        {
          Name: 'movie',
          PrimaryId: {
            AttributeName: 'id',
            AttributeType: {
              Name: 'STRING'
            }
          },
          Attributes: [
            {
              AttributeName: 'title',
              AttributeType: {
                Name: 'STRING'
              }
            },
            {
              AttributeName: 'genres',
              AttributeType: {
                Name: 'STRING'
              }
            }
          ],
          Config: {
            STATS: 'OUTDEGREE_BY_EDGETYPE',
            PRIMARY_ID_AS_ATTRIBUTE: false
          }
        }
      ],
      EdgeTypes: [
        {
          Name: 'rate',
          FromVertexTypeName: 'person',
          ToVertexTypeName: 'movie',
          IsDirected: true,
          Config: {
            REVERSE_EDGE: 'reverse_rate'
          },
          Attributes: [
            {
              AttributeName: 'rating',
              AttributeType: {
                Name: 'DOUBLE'
              }
            },
            {
              AttributeName: 'rated_at',
              AttributeType: {
                Name: 'DATETIME'
              }
            }
          ]
        }
      ]
    };
    const mockGraph = new Graph();
    mockGraph.loadFromGSQLJson(mockGSQLGraphJson);
    expect(mockPatternAggregation.checkAggregationName([], mockGraph)).toEqual({
      success: false,
      message: 'Cannot use an attribute name.'
    });
    mockPatternAggregationJson.name = 'title';
    expect(new PatternAggregation(mockPatternAggregationJson)
      .checkAggregationName([], mockGraph)).toEqual({
        success: false,
        message: 'Cannot use an attribute name.'
      });
    mockPatternAggregationJson.name = 'genres';
    expect(new PatternAggregation(mockPatternAggregationJson)
      .checkAggregationName([], mockGraph)).toEqual({
        success: false,
        message: 'Cannot use an attribute name.'
      });
    mockPatternAggregationJson.name = 'rating';
    expect(new PatternAggregation(mockPatternAggregationJson)
      .checkAggregationName([], mockGraph)).toEqual({
        success: false,
        message: 'Cannot use an attribute name.'
      });
    mockPatternAggregationJson.name = 'rated_at';
    expect(new PatternAggregation(mockPatternAggregationJson)
      .checkAggregationName([], mockGraph)).toEqual({
        success: false,
        message: 'Cannot use an attribute name.'
      });
  });

  describe('should report error if the name', () => {
    it('inlcude reserved word' , () => {
      const mockPatternAggregationJson: PatternAggregationJson = {
        name: 'heapaccum',
        expression: {
          type: 'RealParam',
          valueType: DataType.Real,
          value: 'value'
        }
      };
      const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);
      expect(mockPatternAggregation.checkAggregationName([], new Graph())).toEqual({
        success: false,
        message: '"heapaccum" is a reserved word.'
      });
    });

    it('was incorrect format' , () => {
      const mockPatternAggregationJson: PatternAggregationJson = {
        name: '__.abc',
        expression: {
          type: 'RealParam',
          valueType: DataType.Real,
          value: 'value'
        }
      };
      const mockPatternAggregation = new PatternAggregation(mockPatternAggregationJson);
      expect(mockPatternAggregation.checkAggregationName([], new Graph())).toEqual({
        success: false,
        message: 'Valid name starts with a letter, followed by letters or digits.'
      });
    });
  });
});
