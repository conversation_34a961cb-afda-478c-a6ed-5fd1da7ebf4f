import { PatternParam } from './pattern-param.model';

describe('PatternParamModel', () => {
  it('should get type define', () => {
    const mockPatternParam1 = new PatternParam(
      'params_name',
      false,
      'element_type',
      'vertex_type'
    );

    const mockPatternParam2 = new PatternParam(
      'params_name',
      true,
      'element_type',
      'vertex_type'
    );

    const mockPatternParam3 = new PatternParam(
      'params_name',
      true,
      'element_type',
      ''
    );

    expect(mockPatternParam1.getTypeDef()).toEqual('element_type<vertex_type>');
    expect(mockPatternParam2.getTypeDef()).toEqual('set<element_type<vertex_type>>');
    expect(mockPatternParam3.getTypeDef()).toEqual('set<element_type>');
  });

  it('should return false if two parameters can not happen together', () => {
    const mockPatternParam1 = new PatternParam(
      'params_name',
      false,
      'element_type',
      'vertex_type'
    );
    const mockPatternParam2 = new PatternParam(
      'params_name',
      true,
      'element_type',
      'vertex_type'
    );
    const mockPatternParam3 = new PatternParam(
      'params',
      false,
      'element_type',
      ''
    );
    const mockPatternParam4 = new PatternParam(
      'params',
      false,
      'element_type',
      'vertex_type'
    );

    expect(mockPatternParam1.isCompatible(mockPatternParam2)).toEqual(false);
    expect(mockPatternParam1.isCompatible(mockPatternParam3)).toEqual(true);
    expect(mockPatternParam1.isCompatible(mockPatternParam4)).toEqual(true);
  });

  it('should get param as a string type', () => {
    const mockPatternParam = new PatternParam(
      'params_name',
      true,
      'element_type',
      'vertex_type'
    );
    expect(mockPatternParam.toString()).toEqual('set<element_type<vertex_type>> params_name');
  });

  it('should get type description', () => {
    const mockPatternParam1 = new PatternParam(
      'params_name',
      false,
      'vertex',
      'vertex_type'
    );
    const mockPatternParam2 = new PatternParam(
      'params_name',
      false,
      'element_type',
      'vertex_type'
    );

    expect(mockPatternParam1.getTypeDescription()).toEqual('vertex_type vertex');
    expect(mockPatternParam2.getTypeDescription()).toEqual('element_type');
  });
});
