import { DataType } from '../data';
import { RelationalOperator } from '../expression';

import { PatternVertexSetJson, PatternVertexSet } from './pattern-vertex-set.model';

describe('PatternVertexSetModel', () => {
  it('should get vertex set pattern as json type', () => {
    const mockPatternVertexSetJson: PatternVertexSetJson = {
      vertices: ['a', 'b', 'c'],
      isParam: false,
      ids: ['1', '2', '3'],
      filters: [
        {
          operands: [
            {
              valueType: DataType.Int,
              value: 'a',
              type: 'IntParam'
            },
            {
              value: 'b',
              valueType: DataType.Real,
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        },
        {
          operands: [
            {
              valueType: DataType.Int,
              value: 'a',
              type: 'IntParam'
            },
            {
              value: 'b',
              valueType: DataType.Real,
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        }
      ],
      aggregations: [
        {
          name: 'd',
          expression: {
            valueType: DataType.Real,
            value: 'e',
            type: 'RealParam'
          },
        },
        {
          name: 'd',
          expression: {
            valueType: DataType.Real,
            value: 'e',
            type: 'RealParam'
          },
        }
      ],
      orderBy: [
        {
          asc: true,
          expression: {
            valueType: DataType.String,
            value: 'f',
            type: 'StringParam'
          }
        },
        {
          asc: true,
          expression: {
            valueType: DataType.String,
            value: 'f',
            type: 'StringParam'
          }
        }
      ],
      hasLimit: false,
      limit: 10,
      output: false,
      existentialCheck: false,
      id: '1'
    };
    const mockPatternVertexSet = new PatternVertexSet(mockPatternVertexSetJson);

    expect(mockPatternVertexSet.toJson()).toEqual({
      vertices: ['a', 'b', 'c'],
      isParam: false,
      ids: ['1', '2', '3'],
      filters: [
        {
          operands: [
            {
              value: 'a',
              type: 'IntParam'
            },
            {
              value: 'b',
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        },
        {
          operands: [
            {
              value: 'a',
              type: 'IntParam'
            },
            {
              value: 'b',
              type: 'RealParam'
            }
          ],
          operator: RelationalOperator.Equal,
          type: 'ComparisonCondition'
        }
      ],
      aggregations: [
        {
          name: 'd',
          expression: {
            value: 'e',
            type: 'RealParam'
          },
        },
        {
          name: 'd',
          expression: {
            value: 'e',
            type: 'RealParam'
          },
        }
      ],
      orderBy: [
        {
          asc: true,
          expression: {
            value: 'f',
            type: 'StringParam'
          }
        },
        {
          asc: true,
          expression: {
            value: 'f',
            type: 'StringParam'
          }
        }
      ],
      hasLimit: false,
      limit: 10,
      output: false,
      existentialCheck: false,
      id: '1',
      alias: undefined,
      position: {
        x: 0,
        y: 0
      },
      paramName: undefined
    });
  });
});
