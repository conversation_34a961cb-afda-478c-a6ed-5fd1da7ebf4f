import { BaseExpress<PERSON>, ExpressionJson, parseExp<PERSON><PERSON><PERSON>, <PERSON>om<PERSON><PERSON> } from '../expression';
import { Graph } from '../topology';

export interface PatternOrderByJson {
  asc: boolean;
  expression: ExpressionJson | AtomJson;
}

export class PatternOrderBy {
  asc: boolean;
  expression: BaseExpression;

  constructor(data: PatternOrderByJson, schema?: Graph) {
    this.asc = data.asc;
    this.expression = <BaseExpression>parseExprJson(data.expression, schema);
  }

  toJson(): PatternOrderByJson {
    return {
      asc: this.asc,
      expression: this.expression.toJson()
    };
  }
}
