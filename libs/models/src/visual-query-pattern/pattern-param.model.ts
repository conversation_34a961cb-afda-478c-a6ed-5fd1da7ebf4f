export class PatternParam {
  paramName: string;

  isSet: boolean;
  elementType: string;
  vertexType?: string;

  constructor(paramName: string, isSet: boolean, elementType: string, vertexType?: string) {
    this.paramName = paramName;
    this.isSet = isSet;
    this.elementType = elementType;
    this.vertexType = vertexType;
  }

  /**
   * Check if two parameters can happen together.
   * If two parameter names are not same, that's fine.
   * If two parameter names are same, they must have same data type.
   *
   * @param {PatternParam} other
   * @returns {boolean}
   * @memberof PatternParam
   */
  isCompatible(other: PatternParam): boolean {
    return this.paramName !== other.paramName || this.getTypeDef() === other.getTypeDef();
  }

  /**
   * Get the parameter definition string.
   *
   * @returns {string}
   * @memberof PatternParam
   */
  toString(): string {
    return `${this.getTypeDef()} ${this.paramName}`;
  }

  /**
   * Get the parameter type definition.
   *
   * @returns {string}
   * @memberof PatternParam
   */
  getTypeDef(): string {
    let type = this.elementType + (this.vertexType ? `<${this.vertexType}>` : '');
    if (this.isSet) {
      type = `set<${type}>`;
    }
    return type;
  }

  /**
   * Get the human-readble description of the data type.
   *
   * @returns {string}
   * @memberof PatternParam
   */
  getTypeDescription(): string {
    switch (this.elementType) {
      case 'vertex': {
        return `${this.vertexType} vertex`;
      }
      default: {
        return this.elementType;
      }
    }
  }
}
