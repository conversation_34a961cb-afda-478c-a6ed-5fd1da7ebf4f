import { intersectionWith, isEqual, unionWith } from 'lodash';

import { Graph } from '../topology';
import { HelperFunctions } from '../utils/helper-functions';

/**
 * Schema triplet describing one source-edge-target triplet in schema.
 *
 * @export
 * @interface SchemaTriplet
 */
export interface SchemaTriplet {
  source: string;
  edge: string;
  target: string;
}

export namespace Triplet {

  /**
   * Get all schema triplets of given graph schema.
   *
   * @export
   * @param {Graph} schema
   * @returns {SchemaTriplet[]}
   */
  export function getAllTriplets(schema: Graph): SchemaTriplet[] {
    const result: SchemaTriplet[] = [];
    schema.vertexTypes.forEach(source => {
      schema.edgeTypes.forEach(edge => {
        schema.vertexTypes.forEach(target => {
          // 1. Following edge direction triplet.
          if (
            edge.fromToVertexTypePairs.filter(
              pair => pair.from === source.name && pair.to === target.name
            ).length > 0
          ) {
            result.push({ source: source.name, edge: edge.name, target: target.name});
          }
          // 2. If it's an undirected edge, and following reverse edge direction.
          if (
            !edge.directed &&
            edge.fromToVertexTypePairs.filter(
              pair => pair.to === source.name && pair.from === target.name
            ).length > 0
          ) {
            result.push({ source: source.name, edge: edge.name, target: target.name});
          }
        });
      });
    });
    return result;
  }

  /**
   * Refine schema triplets by source vertex type.
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @param {string[]} vertexTypes
   * @returns {SchemaTriplet[]}
   */
  export function refineBySource(triplets: SchemaTriplet[], vertexTypes: string[]): SchemaTriplet[] {
    return triplets.filter(triplet => vertexTypes.includes(triplet.source));
  }

  /**
   * Refine schema triplets by edge type.
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @param {string[]} edgeTypes
   * @returns {SchemaTriplet[]}
   */
  export function refineByEdge(triplets: SchemaTriplet[], edgeTypes: string[]): SchemaTriplet[] {
    return triplets.filter(triplet => edgeTypes.includes(triplet.edge));
  }

  /**
   * Refine schema triplets by target vertex type.
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @param {string[]} vertexTypes
   * @returns {SchemaTriplet[]}
   */
  export function refineByTarget(triplets: SchemaTriplet[], vertexTypes: string[]): SchemaTriplet[] {
    return triplets.filter(triplet => vertexTypes.includes(triplet.target));
  }

  /**
   * Intersect two sets of schema triplets.
   *
   * @export
   * @param {SchemaTriplet[]} leftTriplets
   * @param {SchemaTriplet[]} rightTriplets
   * @returns {SchemaTriplet[]}
   */
  export function intersectTriplets(leftTriplets: SchemaTriplet[], rightTriplets: SchemaTriplet[]): SchemaTriplet[] {
    return intersectionWith(leftTriplets, rightTriplets, isEqual);
  }

  /**
   * Union two sets of schema triplets.
   *
   * @export
   * @param {SchemaTriplet[]} leftTriplets
   * @param {SchemaTriplet[]} rightTriplets
   * @returns {SchemaTriplet[]}
   */
  export function unionTriplets(leftTriplets: SchemaTriplet[], rightTriplets: SchemaTriplet[]): SchemaTriplet[] {
    return unionWith(leftTriplets, rightTriplets, isEqual);
  }

  /**
   * Exclude rhs schema triplets from lhs schema triplets.
   *
   * @export
   * @param {SchemaTriplet[]} leftTriplets
   * @param {SchemaTriplet[]} rightTriplets
   * @returns {SchemaTriplet[]}
   */
  export function excludeTriplets(leftTriplets: SchemaTriplet[], rightTriplets: SchemaTriplet[]): SchemaTriplet[] {
    return HelperFunctions.exclude(leftTriplets, rightTriplets);
  }

  /**
   * Get all source vertex types of given set of schema triplets (de-duplicated).
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @returns {string[]}
   */
  export function getSourceTypes(triplets: SchemaTriplet[]): string[] {
    const result: Set<string> = new Set([]);
    triplets.forEach(triplet => result.add(triplet.source));
    return Array.from(result.values());
  }

  /**
   * Get all edge types of given set of schema triplets (de-duplicated).
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @returns {string[]}
   */
  export function getEdgeTypes(triplets: SchemaTriplet[]): string[] {
    const result: Set<string> = new Set([]);
    triplets.forEach(triplet => result.add(triplet.edge));
    return Array.from(result.values());
  }

  /**
   * Get all target vertex types of given set of schema triplets (de-duplicated).
   *
   * @export
   * @param {SchemaTriplet[]} triplets
   * @returns {string[]}
   */
  export function getTargetTypes(triplets: SchemaTriplet[]): string[] {
    const result: Set<string> = new Set([]);
    triplets.forEach(triplet => result.add(triplet.target));
    return Array.from(result.values());
  }
}
