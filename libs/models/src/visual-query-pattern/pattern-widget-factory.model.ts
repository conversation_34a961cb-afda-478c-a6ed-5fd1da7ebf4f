import { PatternExpansion, PatternExpansionJson } from './pattern-expansion.model';
import { Pattern<PERSON><PERSON>, Pattern<PERSON>ath<PERSON><PERSON> } from './pattern-path.model';
import {
  PatternVertexSetOperation, PatternVertexSetOperationJson
} from './pattern-vertex-set-operation.model';
import {
  ExpansionType, PathType, PatternWidget,
  PatternWidgetJson, VertexSetOperation
} from './pattern-widget.model';

export class PatternWidgetFactory {
  getPatternWidget(data: PatternWidgetJson): PatternWidget {
    switch (data.type) {
      case PathType.SingleShortestPath:
      case PathType.AllShortestPaths:
      case PathType.AllPaths: {
        return new PatternPath(<PatternPathJson>data);
      }
      case ExpansionType.OneStepExpansion: {
        return new PatternExpansion(<PatternExpansionJson>data);
      }
      case VertexSetOperation.Intersect:
      case VertexSetOperation.Union:
      case VertexSetOperation.Subtract:
      case VertexSetOperation.Within: {
        return new PatternVertexSetOperation(<PatternVertexSetOperationJson>data);
      }
    }
  }
}
