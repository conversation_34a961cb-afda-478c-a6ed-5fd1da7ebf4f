import { VertexSetOperation, PatternWidget, PatternWidgetJson } from './pattern-widget.model';

export interface PatternVertexSetOperationJson extends PatternWidgetJson {
  type: VertexSetOperation;
}

export class PatternVertexSetOperation extends PatternWidget {
  type: VertexSetOperation;
  depth: number;

  constructor(data: PatternVertexSetOperationJson) {
    super(data);
    this.type = data.type;
  }

  toJson(): PatternVertexSetOperationJson {
    return {
      ...super.toJson(),
      type: this.type,
    };
  }
}
