import { ArithmeticOperator, FunctionType, PredicateOperator, RelationalOperator } from '../expression';

export interface ExpressionFormConfig {
  pageType: string;
  isExpression?: boolean;
  supportOptions?: string[];
  supportFunctions?: FunctionType[];
  columnList?: string[];
  // Used for attribute variable.
  entityType?: string;
  // Used for attribute variable from another pattern component.
  otherEntityTypeList?: string[];
}

/**
 * Filter form configurations.
 *
 * @export
 * @returns {ExpressionFormConfig}
 */
 export function getFilterFormConfig(): ExpressionFormConfig {
  return {
    pageType: 'PathFilter',
    supportOptions: [
      'NullCondition',
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'BoolConstant',
      'StringConstant',
      'DatetimeConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'DatetimeParam',
      'AliasReference',
      'AttrVariable',
      'AttrVariableWithAlias',
      'ArithmeticExpression',
      ArithmeticOperator.Divide,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Plus,
      RelationalOperator.Equal,
      RelationalOperator.Larger,
      RelationalOperator.LargerOrEqual,
      RelationalOperator.NotEqual,
      RelationalOperator.Smaller,
      RelationalOperator.SmallerOrEqual,
      PredicateOperator.And,
      PredicateOperator.Not,
      PredicateOperator.Or
    ]
  };
}

/**
 * Aggregation form configurations.
 *
 * @export
 * @returns {ExpressionFormConfig}
 */
 export function getAggregationFormConfig(): ExpressionFormConfig {
  return {
    pageType: 'PathAggregation',
    isExpression: true,
    supportOptions: [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'BoolConstant',
      'StringConstant',
      'DatetimeConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'DatetimeParam',
      'AliasReference',
      'AttrVariable',
      'AttrVariableWithAlias',
      'Function',
      'ArithmeticExpression',
      ArithmeticOperator.Divide,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Plus,
    ],
    supportFunctions: [
      FunctionType.Count,
      FunctionType.DistinctCount,
      FunctionType.Sum,
      FunctionType.Min,
      FunctionType.Max,
      FunctionType.Avg,
      FunctionType.Collect,
      FunctionType.DistinctCollect
    ],
  };
}

/**
 * Order by form configurations.
 *
 * @export
 * @returns {ExpressionFormConfig}
 */
 export function getOrderByFormConfig(): ExpressionFormConfig {
  return {
    pageType: 'PathOrderBy',
    isExpression: true,
    supportOptions: [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'IntParam',
      'RealParam',
      'StringParam',
      'AttrVariable',
      'ArithmeticExpression',
      ArithmeticOperator.Divide,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Plus
    ]
  };
}
