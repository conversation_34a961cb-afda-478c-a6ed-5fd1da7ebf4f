import { DataType } from '../data';
import { RelationalOperator } from '../expression';
import { Graph } from '../topology';

import { PatternEdgeSetJson, PatternEdgeSet } from './pattern-edge-set.model';

describe('PatternEdgeSetModel', () => {
  it('should get edge set pattern as a json type', () => {
    const mockPatternEdgeSetJson: PatternEdgeSetJson = {
      output: false,
      existentialCheck: false,
      sourcePatternId: '1',
      targetPatternId: '2',
      edges: ['rate', 'rate1'],
      repeatAtLeast: 1,
      repeatAtMost: 1,
      filters: [
        {
          operands: [
            {
              value: 'r1',
              valueType: DataType.Int,
              type: 'IntParam'
            },
            {
              value: 'r2',
              valueType: DataType.Int,
              type: 'IntParam'
            }
          ],
          operator: RelationalOperator.NotEqual,
          type: 'ComparisonCondition'
        },
        {
          operands: [
            {
              value: 'r1',
              valueType: DataType.Int,
              type: 'IntParam'
            },
            {
              value: 'r2',
              valueType: DataType.Int,
              type: 'IntParam'
            }
          ],
          operator: RelationalOperator.NotEqual,
          type: 'ComparisonCondition'
        }
      ],
      notExist: false,
      id: '3'
    };
    const mockPatternEdgeSet = new PatternEdgeSet(mockPatternEdgeSetJson, new Graph());

    expect(mockPatternEdgeSet.toJson()).toEqual({
      sourcePatternId: '1',
      targetPatternId: '2',
      edges: ['rate', 'rate1'],
      repeatAtLeast: 1,
      repeatAtMost: 1,
      notExist: false,
      filters: [
        {
          operands: [
            {
              value: 'r1',
              type: 'IntParam'
            },
            {
              value: 'r2',
              type: 'IntParam'
            }
          ],
          operator: RelationalOperator.NotEqual,
          type: 'ComparisonCondition'
        },
        {
          operands: [
            {
              value: 'r1',
              type: 'IntParam'
            },
            {
              value: 'r2',
              type: 'IntParam'
            }
          ],
          operator: RelationalOperator.NotEqual,
          type: 'ComparisonCondition'
        }
      ],
      id: '3',
      alias: undefined,
      output: false,
      existentialCheck: false,
      position: {
        x: 0,
        y: 0
      }
    });
  });
});
