import { Condition<PERSON>son, BaseCondition, parseExpr<PERSON>son } from '../expression';
import { Graph } from '../topology';

import { PatternAggregationJson, PatternAggregation } from './pattern-aggregation.model';
import { PatternEntity, PatternEntityJson } from './pattern-entity.model';
import { Pattern<PERSON>rde<PERSON><PERSON><PERSON><PERSON><PERSON>, PatternOrderBy } from './pattern-order-by.model';

export interface PatternVertexSetJson extends PatternEntityJson {
  vertices: string[];
  isParam: boolean;
  ids: string[];
  paramName?: string;
  filters: ConditionJson[];
  aggregations: PatternAggregationJson[];
  orderBy: PatternOrderByJson[];
  // limitIsParam: boolean;
  // limitParamName?: string;
  hasLimit: boolean;
  limit: number;
}

export class PatternVertexSet extends PatternEntity {
  vertices: string[];
  isParam: boolean;
  ids: string[];
  paramName?: string;
  filters: BaseCondition[];
  aggregations: PatternAggregation[];
  orderBy: PatternOrderBy[];
  // limitIsParam: boolean;
  // limitParamName?: string;
  hasLimit: boolean;
  limit: number;

  constructor(data: PatternVertexSetJson, schema?: Graph) {
    super(data);
    this.vertices = data.vertices || [];
    this.isParam = data.isParam;
    this.ids = data.ids;
    this.paramName = data.paramName;
    this.filters = data.filters
      ? data.filters.map(filterJson => <BaseCondition>parseExprJson(filterJson, schema))
      : [];
    this.aggregations = data.aggregations
      ? data.aggregations.map(aggJson => new PatternAggregation(aggJson, schema))
      : [];
    this.orderBy = data.orderBy
      ? data.orderBy.map(orderByJson => new PatternOrderBy(orderByJson, schema))
      : [];
    // this.limitIsParam = data.limitIsParam;
    // this.limitParamName = data.limitParamName;
    this.hasLimit = data.hasLimit || false;
    this.limit = data.limit;
  }

  toJson(): PatternVertexSetJson {
    return {
      ...super.toJson(),
      vertices: this.vertices.slice(),
      isParam: this.isParam,
      ids: this.ids.slice(),
      paramName: this.paramName,
      filters: this.filters.map(filter => filter.toJson()),
      aggregations: this.aggregations.map(agg => agg.toJson()),
      orderBy: this.orderBy.map(orderBy => orderBy.toJson()),
      // limitIsParam: this.limitIsParam,
      // limitParamName: this.limitParamName,
      hasLimit: this.hasLimit,
      limit: this.limit,
    };
  }
}
