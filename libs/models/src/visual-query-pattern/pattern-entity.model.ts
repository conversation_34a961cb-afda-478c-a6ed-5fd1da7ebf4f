import { HelperFunctions } from '../utils';

export interface PatternEntityJson {
  id?: string;
  alias?: string;
  output: boolean;
  existentialCheck: boolean;
  position?: { x: number, y: number };
}

export class PatternEntity {
  id: string;
  alias?: string;
  output: boolean;
  position: { x: number, y: number };

  // Not used
  existentialCheck: boolean;

  constructor(data: PatternEntity<PERSON>son) {
    this.id = data.id || HelperFunctions.idGenerator();
    this.alias = data.alias;
    this.output = data.output;
    this.existentialCheck = data.existentialCheck;
    this.position = data.position || { x: 0, y: 0 };
  }

  to<PERSON>son(): PatternEntity<PERSON>son {
    return {
      id: this.id,
      alias: this.alias,
      output: this.output,
      existentialCheck: this.existentialCheck,
      position: { ...this.position }
    };
  }
}
