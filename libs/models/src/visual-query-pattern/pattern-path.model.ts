import { PathType, PatternWidget, PatternWidgetJson } from './pattern-widget.model';

export interface PatternPath<PERSON>son extends PatternWidgetJson {
  type: PathType;
  depth: number;
}

export class PatternPath extends PatternWidget {
  type: PathType;
  depth: number;

  constructor(data: Pattern<PERSON><PERSON><PERSON><PERSON>) {
    super(data);
    this.type = data.type;
    this.depth = data.depth;
  }

  to<PERSON>son(): Pattern<PERSON><PERSON><PERSON><PERSON> {
    return {
      ...super.toJson(),
      type: this.type,
      depth: this.depth
    };
  }
}
