export interface ValidateResult {
  success: boolean;
  message?: string;
}

export const reservedWords: string[] = [
  // _
  '_internal_attr_tag',
  // a
  'accum',
  'add',
  'admin',
  'all',
  'allocate',
  'alter',
  'and',
  'any',
  'as',
  'asc',
  'avg',
  // b
  'bag',
  'batch',
  'between',
  'bigint',
  'blob',
  'bool',
  'boolean',
  'both',
  'break',
  'by',
  // c
  'call',
  'cascade',
  'case',
  'catch',
  'char',
  'character',
  'check',
  'clob',
  'coalesce',
  'compress',
  'const',
  'constraint',
  'continue',
  'cost',
  'count',
  'create',
  'current_date',
  'current_time',
  'current_timestamp',
  'cursor',
  // d
  'datetime',
  'datetime_add',
  'datetime_sub',
  'decimal',
  'declare',
  'delete',
  'desc',
  'discriminator',
  'distributed',
  'do',
  'double',
  'drop',
  // e
  'edge',
  'else',
  'elseif',
  'end',
  'escape',
  'exception',
  'exists',
  // f
  'false',
  'file',
  'filter',
  'fixed_binary',
  'float',
  'for',
  'foreach',
  'from',
  'function',
  // g
  'global',
  'grants',
  'graph',
  'group',
  'groupbyaccum',
  'gsql_sys_tag',
  // h
  'having',
  'header',
  'heapaccum',
  // i
  'if',
  'ignore',
  'in',
  'index',
  'input_line_filter',
  'insert',
  'int',
  'int16',
  'int32',
  'int32_t',
  'int64_t',
  'int8',
  'integer',
  'interpret',
  'intersect',
  'into',
  'is',
  'isempty',
  // j
  'job',
  'join',
  'jsonarray',
  'jsonobject',
  // k
  'kafka',
  'key',
  // l
  'leading',
  'like',
  'limit',
  'list',
  'load',
  'loadaccum',
  'log',
  'long',
  // m
  'map',
  'max',
  'min',
  'minus',
  // n
  'nobody',
  'not',
  'now',
  'null',
  // o
  'offset',
  'on',
  'opencypher',
  'or',
  'order',
  // p
  'package',
  'pinned',
  'post-accum',
  'post_accum',
  'primary',
  'primary_id',
  'print',
  'proxy',
  // q
  'query',
  'quit',
  // r
  'raise',
  'range',
  'reduce',
  'replace',
  'reset_collection_accum',
  'return',
  'returns',
  'run',
  // s
  's3',
  'sample',
  'select',
  'selectvertex',
  'set',
  'static',
  'string',
  'sum',
  'sys.filename',
  'sys.internal_id',
  // t
  'target',
  'temp_table',
  'then',
  'to',
  'to_csv',
  'to_datetime',
  'trailing',
  'translatesql',
  'trim',
  'true',
  'try',
  'tuple',
  'type',
  'typedef',
  // u
  'uint',
  'uint16',
  'uint32',
  'uint32_t',
  'uint64_t',
  'uint8',
  'uint8_t',
  'union',
  'update',
  'upsert',
  'using',
  // v
  'values',
  'vertex',
  // w
  'when',
  'where',
  'while',
  'with'
];

export const reservedWordCaseSensitive = [];

export const nonReservedWords = [];

export const uIntegerRegex = '^[0-9]+$';
export const integerRegex = '^-?[0-9]+$';
export const nameRegex = '^[a-zA-Z_]([a-zA-Z0-9_])*$';
export const realRegex = '^-?([0-9]+([.][0-9]*)?|[0-9]*[.][0-9]+)$';
export const boolRegex = '^([tT][rR][uU][eE]|[fF][aA][lL][sS][eE]|0|1)$';
export const datetimeRegex = [
  '^[0-9]{4}-[0-9]{2}-[0-9]{2}$', // %Y-%m-%d
  '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$', // %Y-%m-%d %H:%M:%S
  '^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}z$', // %Y-%m-%dT%H:%M:%S.000z
  '^[0-9]{4}/[0-9]{2}/[0-9]{2}$', // %Y/%m/%d
  '^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$', // %Y/%m/%d %H:%M:%S，
  '^[0-9]*$', // timestamp
];

export enum DatetimeFormat {
  DashDay,
  DashSecond,
  DashMillisecond,
  SlashDay,
  SlashSecond,
  Timestamp
}


export namespace FormatValidator {
  /**
   * Test if string is a valid name.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isName(val: string): ValidateResult {
    if (val && !nonReservedWords.includes(val.toLowerCase())) {
      if (
        reservedWords.includes(val.toLowerCase()) ||
        reservedWordCaseSensitive.includes(val)
      ) {
        return {
          success: false,
          message: `"${val}" is a reserved word.`
        };
      }
    }

    return test(
      val,
      new RegExp(nameRegex),
      'Valid name starts with a letter, followed by letters or digits.'
    );
  }

  /**
   * Test if string is a valid accum name.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isAccum(val: string): ValidateResult {
    const toCheck = (val && val[0] === '@') ? val.slice(1) : val;
    return test(
      toCheck,
      new RegExp(nameRegex),
      'Valid name starts with a letter, followed by letters or digits.'
    );
  }

  /**
   * Test if string is an unsigned integer.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isUInt(val: string): ValidateResult {
    const regExp = new RegExp(uIntegerRegex);
    return test(val, regExp, 'Should be non-negative integer.');
  }

  /**
   * Test if string is an integer.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isInt(val: string): ValidateResult {
    const regExp = new RegExp(integerRegex);
    return test(val, regExp, 'Should be integer.');
  }

  /**
   * Test if string is a real number.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isReal(val: string): ValidateResult {
    const regExp = new RegExp(realRegex);
    return test(val, regExp, 'Should be real number.');
  }

  /**
   * Test if string is boolean.
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isBoolean(val: string): ValidateResult {
    const regExp = new RegExp(boolRegex);
    return test(val, regExp, 'Should be "true" or "false".');
  }

  /**
   * Test if string is datetime.
   * The datetime must follow yyyy-MM-dd hh:mm:ss format or yyyy-MM-dd format.
   *
   * Make sure year, month, day, hour, minute and second are valid.
   * - year: 0000 - 9999
   * - month: 01 - 12
   * - day: leap year Feb has 29 days, others have 28 days.
   * - hour: 00 - 23
   * - minute: 00 - 59
   * - second: 00 - 59
   *
   * @export
   * @param {string} val
   * @returns {ValidateResult}
   */
  export function isDatetime(val: string, required = true): ValidateResult {
    let i = 0;
    let regExp: RegExp;
    let year: number;
    let month: number;
    let day: number;
    let hour: number;
    let minute: number;
    let second: number;
    let yearMonthDay: string;
    let hourMinuteSecond: string;

    if (required || (!required && val !== '')) {
      // Check the string must be number format, and must be a valid datetime
      for (; i < datetimeRegex.length; i++) {
        regExp = new RegExp(datetimeRegex[i]);
        if (regExp.test(val)) {
          break;
        }
      }

      switch (i) {
        case DatetimeFormat.DashDay:
          [year, month, day] = val.split('-').map(value => parseInt(value, 10));
          break;
        case DatetimeFormat.DashSecond:
          [yearMonthDay, hourMinuteSecond] = val.split(' ');
          [year, month, day] = yearMonthDay.split('-').map(value => parseInt(value, 10));
          [hour, minute, second] = hourMinuteSecond.split(':').map(value => parseInt(value, 10));
          break;
        case DatetimeFormat.DashMillisecond:
          [yearMonthDay, hourMinuteSecond] = val.split('.')[0].split('T');
          [year, month, day] = yearMonthDay.split('-').map(value => parseInt(value, 10));
          [hour, minute, second] = hourMinuteSecond.split(':').map(value => parseInt(value, 10));
          break;
        case DatetimeFormat.SlashDay:
          [year, month, day] = val.split('/').map(value => parseInt(value, 10));
          break;
        case DatetimeFormat.SlashSecond:
          [yearMonthDay, hourMinuteSecond] = val.split(' ');
          [year, month, day] = yearMonthDay.split('/').map(value => parseInt(value, 10));
          [hour, minute, second] = hourMinuteSecond.split(':').map(value => parseInt(value, 10));
          break;
        case DatetimeFormat.Timestamp:
          const datetime = new Date(parseInt(val, 10) * 1000);
          year = datetime.getFullYear();
          month = datetime.getMonth() + 1;
          day = datetime.getDate();
          hour = datetime.getHours();
          minute = datetime.getMinutes();
          second = datetime.getSeconds();
          break;
        default:
          return {
            success: false,
            message: 'Invalid datetime. Valid format: yyyy-MM-dd or yyyy-MM-dd hh:mm:ss.' +
              'For more formats, please refer to the documentation.'
          };
      }
    }

    // Month between 1 and 12.
    if (month < 1 || month > 12) {
      return {
        success: false,
        message: 'Invalid datetime. Month should be between 1 and 12.'
      };
    }
    // Day depends on month.
    let dayUpperLimit = 31;
    switch (month) {
      case 4:
      case 6:
      case 9:
      case 11: {
        dayUpperLimit = 30;
        break;
      }
      case 2: {
        dayUpperLimit = (!(year % 400) || (!(year % 4) && year % 100)) ? 29 : 28;
      }
    }
    if (day < 1 || day > dayUpperLimit) {
      return {
        success: false,
        message: `Invalid datetime. Day should be between 1 and ${dayUpperLimit}.`
      };
    }
    if (hourMinuteSecond) {
      if (hour < 0 || hour > 23) {
        return {
          success: false,
          message: `Invalid datetime. Hour should be between 0 and 23.`
        };
      }
      if (minute < 0 || minute > 59) {
        return {
          success: false,
          message: `Invalid datetime. Minute should be between 0 and 59.`
        };
      }
      if (second < 0 || second > 59) {
        return {
          success: false,
          message: `Invalid datetime. Second should be between 0 and 59.`
        };
      }
    }

    return {
      success: true,
      message: ''
    };
  }

  /**
   * Get dateitme format for input string.
   *
   * @param {string} date
   * @returns {DatetimeFormat}
   */
  export function getDateTimeFormat(date: string): DatetimeFormat {
    let i = 0;
    let regExp: RegExp;
    for (; i < datetimeRegex.length; i++) {
      regExp = new RegExp(datetimeRegex[i]);
      if (regExp.test(date)) {
        break;
      }
    }

    return i;
  }

  /**
   * Test whether the input string follows corresponding format or not.
   *
   * @param {string} val
   * @param {RegExp} regExp
   * @param {string} msg
   * @returns {ValidateResult}
   */
  function test(val: string, regExp: RegExp, msg: string): ValidateResult {
    if (val === undefined) {
      return {
        success: false,
        message: 'Empty string.'
      };
    }

    if (val && regExp.test(val)) {
      return {
        success: true,
        message: ''
      };
    } else {
      return {
        success: false,
        message: msg
      };
    }
  }
}
