import { Observable, Subject } from 'rxjs';
import { startWith, switchMap } from 'rxjs/operators';

export interface ResettableStream<T> {
  reset: () => void;
  subject: Subject<T>;
  observable: Observable<T>;
}

/**
 * Build a stream that can be reset.
 * Source: https://stackoverflow.com/questions/51145664/resetting-replaysubject-in-rxjs-6
 *
 * The idea is to wrap the stream with a sink subject and a source subject.
 * The sink subject passes any data that is published to its stream to the inner stream.
 * The source subject switches its stream to the inner stream so that subscriber
 * can receive data from the inner stream. When the inner stream need to be reset,
 * it is disposed and recreated. The sink subject will pass data to this new inner stream
 * and the source subject switches its stream to this new inner stream.
 *
 * @export
 * @template T
 * @param {() => Subject<T>} factory
 * @returns {ResettableStream<T>}
 */
export function resettable<T>(factory: () => Subject<T>): ResettableStream<T> {
  const sink = new Subject<T>();
  const source = new Subject<void>();

  let destination = factory(); // create the inner stream.
  let subscription = sink.subscribe(destination); // pass data to inner stream.

  return {
    reset: () => {
      subscription.unsubscribe();
      destination = factory();
      subscription = sink.subscribe(destination);
      source.next(); // switch to new inner stream.
    },
    subject: sink,
    observable: source.asObservable().pipe(
      startWith(undefined),
      switchMap(() => destination) // switch to inner stream.
    )
  };
}
