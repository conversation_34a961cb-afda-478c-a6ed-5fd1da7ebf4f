import { HelperFunctions } from './helper-functions';

describe('HelperFunctions', () => {

  it('should process file name to contain only letters, numbers and underscored', () => {
    expect(HelperFunctions.fileNameToId('/user/folder/1.csv')).toBe('_user_folder_1_csv');
    expect(HelperFunctions.fileNameToId('D:\\\\user\\folder\\~1.csv')).toBe('D___user_folder__1_csv');
  });

  it('should remove concat chars and convert to lowercase', () => {
    expect(HelperFunctions.removeConcatCharsAndToLowerCase('A b\tc-d 109')).toBe('abcd109');
    expect(HelperFunctions.removeConcatCharsAndToLowerCase('__ 中文/Ar')).toBe('中文ar');
  });

  it('should correctly judge if two points are near each other', () => {
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 100, 100, 30)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 100, 100, 300)).toBeTruthy();

    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 0, 5, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 1, 5, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 20, 11, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 19, 11, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 5, 0, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 7, 1, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 5, 20, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 7, 18, 10)).toBeTruthy();

    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 0, 0, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 20, 20, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 20, 0, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 0, 20, 10)).toBeFalsy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 1, 1, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 19, 19, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 19, 1, 10)).toBeTruthy();
    expect(HelperFunctions.twoPointsNearEachOther(10, 10, 1, 19, 10)).toBeTruthy();
  });

  it('should correctly truncate UTF8 string', () => {
    expect(HelperFunctions.truncateUTF8String('1234567', 3)).toBe('...');
    expect(HelperFunctions.truncateUTF8String('1234567', 4)).toBe('1...');
    expect(HelperFunctions.truncateUTF8String('1234567', 6)).toBe('123...');
    expect(HelperFunctions.truncateUTF8String('1234567', 12)).toBe('1234567');
    expect(HelperFunctions.truncateUTF8String('中国人1', 3)).toBe('...');
    expect(HelperFunctions.truncateUTF8String('中国人1', 4)).toBe('...');
    expect(HelperFunctions.truncateUTF8String('中国人1', 5)).toBe('中...');
    expect(HelperFunctions.truncateUTF8String('中国人1', 6)).toBe('中...');
    expect(HelperFunctions.truncateUTF8String('中国人1', 7)).toBe('中国人1');
    expect(HelperFunctions.truncateUTF8String('中国人1', 8)).toBe('中国人1');
    expect(HelperFunctions.truncateUTF8String('中国人1', 9)).toBe('中国人1');
    expect(HelperFunctions.truncateUTF8String('中国人1', 10)).toBe('中国人1');
  });

  it('should retrieve file name from path correctly', () => {
    expect(HelperFunctions.retrieveFileNameFromPath('source.txt')).toBe('source.txt');
    expect(HelperFunctions.retrieveFileNameFromPath('/source.txt')).toBe('source.txt');
    expect(HelperFunctions.retrieveFileNameFromPath('src/source.txt')).toBe('source.txt');
    expect(HelperFunctions.retrieveFileNameFromPath('/src/source.txt')).toBe('source.txt');
  });

  it('should replace graph name placeholder into graph name correctly', () => {
    expect(HelperFunctions.applyGraphNameToURL('MyGraph', '/engine/graph/[GRAPH_NAME]/vertices'))
      .toBe('/engine/graph/MyGraph/vertices');
    expect(HelperFunctions.applyGraphNameToURL('MyGraph', '/engine/graph/vertices'))
      .toBe('/engine/graph/vertices');
  });

  it('should capitalize the first character of the given word', () => {
    expect(HelperFunctions.capitalizeFirstCharacter('abcdef'))
      .toBe('Abcdef');
  });

  it('should return empty string if the word needed to be capitalized is undefined or empty string', () => {
    let testString;
    expect(HelperFunctions.capitalizeFirstCharacter(testString)).toBe('');
    testString = '';
    expect(HelperFunctions.capitalizeFirstCharacter(testString)).toBe('');
  });

  it('should return empty when escaping an empty or undefined string', () => {
    let testString;
    expect(HelperFunctions.escapeHtml(testString)).toBe('');
    testString = '';
    expect(HelperFunctions.escapeHtml(testString)).toBe('');
  });

  it('should escape html tags in a non empty string', () => {
    expect(HelperFunctions.escapeHtml(`&`)).toBe('&amp;');
    expect(HelperFunctions.escapeHtml(`<`)).toBe('&lt;');
    expect(HelperFunctions.escapeHtml(`>`)).toBe('&gt;');
    expect(HelperFunctions.escapeHtml(`"`)).toBe('&quot;');
    expect(HelperFunctions.escapeHtml(`'`)).toBe('&#039;');
    expect(HelperFunctions.escapeHtml(`<div>hi</div>`)).toBe('&lt;div&gt;hi&lt;/div&gt;');
    expect(HelperFunctions.escapeHtml(`<input type='text' class="btn">`))
      .toBe('&lt;input type=&#039;text&#039; class=&quot;btn&quot;&gt;');
  });

  it('should retrieve attribute and accumulator value correctly', () => {
    expect(HelperFunctions.retrieveAttributeAndAccumValue({
      attr: 'some value'
    }, 'attr')).toBe('some value');

    expect(HelperFunctions.retrieveAttributeAndAccumValue({
      'S2.attr': 'some value'
    }, 'attr')).toBe('some value');

    expect(HelperFunctions.retrieveAttributeAndAccumValue({
      'S2.attr': 'some value'
    }, 'attr3')).toBeUndefined();
  });

  it('should convert epoch to human-readable date and time format', () => {
    expect(HelperFunctions.epochToDatetime(1581120130000, 0)).toBe('2020-02-08 00:02:10');
    expect(HelperFunctions.epochToDatetime(1425980130000, 0)).toBe('2015-03-10 09:35:30');
    expect(HelperFunctions.epochToDatetime(1236370130000, 0)).toBe('2009-03-06 20:08:50');
  });

  it('should convert seconds to beautified time period correctly', () => {
    expect(HelperFunctions.beautifySeconds(0)).toBe('0s');
    expect(HelperFunctions.beautifySeconds(0.1)).toBe('0.1s');
    expect(HelperFunctions.beautifySeconds(10)).toBe('10s');
    expect(HelperFunctions.beautifySeconds(60)).toBe('1m 0s');
    expect(HelperFunctions.beautifySeconds(3599)).toBe('59m 59s');
    expect(HelperFunctions.beautifySeconds(3600)).toBe('1h 0m 0s');
    expect(HelperFunctions.beautifySeconds(3666)).toBe('1h 1m 6s');
    expect(HelperFunctions.beautifySeconds(86399)).toBe('23h 59m 59s');
    expect(HelperFunctions.beautifySeconds(86400)).toBe('1d 0h 0m 0s');
    expect(HelperFunctions.beautifySeconds(126456)).toBe('1d 11h 7m 36s');
  });

  it('should convert bytes to abbreviate size correctly', () => {
    expect(HelperFunctions.abbreviateByte(null)).toBe('NaN');
    expect(HelperFunctions.abbreviateByte(0)).toBe('0Bytes');
    expect(HelperFunctions.abbreviateByte(30)).toBe('30Bytes');
    expect(HelperFunctions.abbreviateByte(1024)).toBe('1KB');
    expect(HelperFunctions.abbreviateByte(1024 * 1024, 1)).toBe('1.0MB');
    expect(HelperFunctions.abbreviateByte(1024 * 1024 * 1024, 1)).toBe('1.0GB');
    expect(HelperFunctions.abbreviateByte(1024 * 1024 * 1024 * 1024, 1)).toBe('1.0TB');
    expect(HelperFunctions.abbreviateByte(1024 * 1024 * 1024 * 1024 * 1024, 1)).toBe('1.0PB');
    expect(HelperFunctions.abbreviateByte(1024 * 1024 * 1024 * 1024 * 1024 * 1024)).toBe('1EB');
    expect(HelperFunctions.abbreviateByte(Math.round(1024 * 1024 * 1.3), 1)).toBe('1.3MB');
  });

  it('should convert number to abbreviate number correctly', () => {
    expect(HelperFunctions.abbreviateNumber(null)).toBe('NaN');
    expect(HelperFunctions.abbreviateNumber(0)).toBe('0');
    expect(HelperFunctions.abbreviateNumber(30)).toBe('30');
    expect(HelperFunctions.abbreviateNumber(30, 2)).toBe('30');
    expect(HelperFunctions.abbreviateNumber(1000)).toBe('1K');
    expect(HelperFunctions.abbreviateNumber(1000 * 1000, 1)).toBe('1.0M');
    expect(HelperFunctions.abbreviateNumber(1000 * 1000 * 1000, 1)).toBe('1.0B');
    expect(HelperFunctions.abbreviateNumber(1000 * 1000 * 1000 * 1000, 1)).toBe('1.0T');
    expect(HelperFunctions.abbreviateNumber(1000 * 1000 * 1000 * 1000 * 1000, 1)).toBe('1.0KT');
    expect(HelperFunctions.abbreviateNumber(1000 * 1000 * 1000 * 1000 * 1000 * 1000)).toBe('1MT');
    expect(HelperFunctions.abbreviateNumber(Math.round(1000 * 1000 * 1.3), 1)).toBe('1.3M');
  });

  it('should convert number to beautified number correctly', () => {
    expect(HelperFunctions.beautifyNumber(0)).toBe('0');
    expect(HelperFunctions.beautifyNumber(30)).toBe('30');
    expect(HelperFunctions.beautifyNumber(1000)).toBe('1,000');
    expect(HelperFunctions.beautifyNumber(1023)).toBe('1,023');
    expect(HelperFunctions.beautifyNumber(10000)).toBe('10,000');
    expect(HelperFunctions.beautifyNumber(1232432)).toBe('1,232,432');
    expect(HelperFunctions.beautifyNumber(10000000000000000)).toBe('10,000,000,000,000,000');
    expect(HelperFunctions.abbreviateNumber(0)).toBe('0');
    expect(HelperFunctions.beautifyNumber(-30)).toBe('-30');
    expect(HelperFunctions.beautifyNumber(-1000)).toBe('-1,000');
    expect(HelperFunctions.beautifyNumber(-1023)).toBe('-1,023');
    expect(HelperFunctions.beautifyNumber(-10000)).toBe('-10,000');
    expect(HelperFunctions.beautifyNumber(-1232432)).toBe('-1,232,432');
    expect(HelperFunctions.beautifyNumber(-10000000000000000)).toBe('-10,000,000,000,000,000');
  });

  it('should generate id correctly', () => {
    expect(HelperFunctions.idGenerator().length).toBeGreaterThan(7);
  });

  it('should join a list of string nicely', () => {
    expect(HelperFunctions.joinStrAsPara([])).toBe('');
    expect(HelperFunctions.joinStrAsPara(['a'])).toBe('a');
    expect(HelperFunctions.joinStrAsPara(['a', 'b'])).toBe('a and b');
    expect(HelperFunctions.joinStrAsPara(['a', 'b', 'c'])).toBe('a, b and c');
    expect(HelperFunctions.joinStrAsPara(['a', 'b', 'c', 'd'])).toBe('a, b, c and d');
  });

  it('should convert map to JSON object', () => {
    expect(HelperFunctions.convertMapToObject(undefined)).toBeUndefined();
    expect(HelperFunctions.convertMapToObject(new Map([['1', 1]]))).toEqual({ '1': 1 });
  });

  it('should convert JSON object to map', () => {
    expect(HelperFunctions.convertObjectToMap(undefined)).toBeUndefined();
    expect(HelperFunctions.convertObjectToMap({ '1': 1 })).toEqual(new Map([['1', 1]]));
  });

  it('should number formatting', () => {
    expect(HelperFunctions.numberFormatting(
      1234.56789,
      {
        enabled: true,
        value: '10'
      },
      {
        enabled: true,
        value: '.'
      },
      {
        enabled: true,
        value: ','
      }
    )).toBe('1.234,5678900000');
  });

  it('should number formatting for attrs', () => {
    expect(HelperFunctions.numberFormattingForAttrs(1234.56789, <any>{
      numberFormatting: {
        unifyDecimalSpaces: {
          enabled: true,
          value: '10'
        },
        thousandsSeparator: {
          enabled: true,
          value: '.'
        },
        decimalSeparator: {
          enabled: true,
          value: ','
        }
      }
    })).toBe('1.234,5678900000');
  });
});
