import { DatetimeFormat, FormatValidator } from './format-validator';

describe('Validator', () => {
  const emptyString = 'Empty string.';

  const invalidId = 'Valid name starts with a letter, ' +
    'followed by letters or digits.';

  const invalidUInt = 'Should be non-negative integer.';
  const invalidInt = 'Should be integer.';
  const invalidReal = 'Should be real number.';
  const invalidBoolean = 'Should be "true" or "false".';
  const invalidDatetime =
    'Invalid datetime. Valid format: yyyy-MM-dd or yyyy-MM-dd hh:mm:ss.' +
    'For more formats, please refer to the documentation.';

  it('should fail checking because of undefined string', () => {
    let result = FormatValidator.isName(undefined);
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(emptyString);

    result = FormatValidator.isUInt(undefined);
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(emptyString);

    result = FormatValidator.isInt(undefined);
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(emptyString);

    result = FormatValidator.isBoolean(undefined);
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(emptyString);

    result = FormatValidator.isReal(undefined);
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(emptyString);
  });

  it('should fail id checking because of mal-format', () => {
    let result = FormatValidator.isName('123');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('.src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('s..');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('u123.id2#23.abc');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('u123.id2.23.abc');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);

    result = FormatValidator.isName('u.1.2.3.4.5');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidId);
  });

  it('should fail id checking because conflict reversed words', () => {
    let result = FormatValidator.isName('primary_id');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('"primary_id" is a reserved word.');

    result = FormatValidator.isName('Order');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('"Order" is a reserved word.');

    result = FormatValidator.isName('select');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('"select" is a reserved word.');

    // non-reserved word
    result = FormatValidator.isName('password');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should pass id checking', () => {
    let result = FormatValidator.isName('u123');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isName('normal_id');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isName('___');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail uint checking because of mal-format', () => {
    let result = FormatValidator.isUInt('-123');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidUInt);

    result = FormatValidator.isUInt('src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidUInt);

    result = FormatValidator.isUInt('3.5');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidUInt);

    result = FormatValidator.isUInt('');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidUInt);
  });

  it('should pass uint checking', () => {
    let result = FormatValidator.isUInt('0');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isUInt('1');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isUInt('32767');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail int checking because of mal-format', () => {
    let result = FormatValidator.isInt('src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidInt);

    result = FormatValidator.isInt('3.5');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidInt);

    result = FormatValidator.isInt('');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidInt);
  });

  it('should pass int checking', () => {
    let result = FormatValidator.isInt('0');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isInt('1');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isInt('32767');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isInt('-32768');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail boolean checking because of mal-format', () => {
    let result = FormatValidator.isBoolean('src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidBoolean);

    result = FormatValidator.isBoolean('3.5');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidBoolean);

    result = FormatValidator.isBoolean('');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidBoolean);
  });

  it('should pass boolean checking', () => {
    let result = FormatValidator.isBoolean('true');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('True');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('tRue');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('trUe');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('truE');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('TRUE');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('false');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('False');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('fAlse');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('faLse');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('falSe');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('falsE');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isBoolean('FALSE');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail real checking because of mal-format', () => {
    let result = FormatValidator.isReal('src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidReal);

    result = FormatValidator.isReal('3.5.3');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidReal);

    result = FormatValidator.isReal('');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidReal);

    result = FormatValidator.isReal('.');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidReal);
  });

  it('should pass real checking', () => {
    let result = FormatValidator.isReal('3.55');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isReal('0');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isReal('-3450');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isReal('-0.');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isReal('-.5');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isReal('.57');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail datetime checking because of mal-format', () => {
    let result = FormatValidator.isDatetime('src');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017 11 20');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017 11 20 12:12:12');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017 11 20 12 12 12');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-11-21 12:12:12.1234z');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-11-21 12:12:12.14z');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-13-20');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Month should be between 1 and 12.');

    result = FormatValidator.isDatetime('2017-11-100');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-11-20 10:12:61');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Second should be between 0 and 59.');

    result = FormatValidator.isDatetime('2017-11-20 10:0:0');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-1-20');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual(invalidDatetime);

    result = FormatValidator.isDatetime('2017-01-20 25:00:00');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Hour should be between 0 and 23.');

    result = FormatValidator.isDatetime('2017-02-29 20:00:00');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Day should be between 1 and 28.');

    result = FormatValidator.isDatetime('2017-03-32');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Day should be between 1 and 31.');

    result = FormatValidator.isDatetime('2017-04-31');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Day should be between 1 and 30.');

    result = FormatValidator.isDatetime('2300-02-29');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Day should be between 1 and 28.');

    result = FormatValidator.isDatetime('2400-02-30');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Day should be between 1 and 29.');

    result = FormatValidator.isDatetime('2017-01-20 20:60:41');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Invalid datetime. Minute should be between 0 and 59.');
  });

  it('should pass datetime checking', () => {
    let result = FormatValidator.isDatetime('2017-01-01 01:02:14');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('1444444444');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2017/11/20 11:02:14');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2017-11-20T11:02:14.123z');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2017/11/20');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2017-11-20');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2020-02-29 23:00:00');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isDatetime('2400-02-29');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should pass accumulator checking', () => {
    let result = FormatValidator.isAccum('@intAccum');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isAccum('@boolAccum');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isAccum('intAccum');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');

    result = FormatValidator.isAccum('@int');
    expect(result.success).toEqual(true);
    expect(result.message).toEqual('');
  });

  it('should fail accumulator checking', () => {
    let result = FormatValidator.isAccum('@123');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Valid name starts with a letter, followed by letters or digits.');

    result = FormatValidator.isAccum('123');
    expect(result.success).toEqual(false);
    expect(result.message).toEqual('Valid name starts with a letter, followed by letters or digits.');
  });

  it('should get correct datetime format', () => {
    let format = FormatValidator.getDateTimeFormat('2020-01-01');
    expect(format).toEqual(DatetimeFormat.DashDay);

    format = FormatValidator.getDateTimeFormat('2020-01-01 00:00:00');
    expect(format).toEqual(DatetimeFormat.DashSecond);

    format = FormatValidator.getDateTimeFormat('2017-11-20T11:02:14.123z');
    expect(format).toEqual(DatetimeFormat.DashMillisecond);

    format = FormatValidator.getDateTimeFormat('2020/01/01');
    expect(format).toEqual(DatetimeFormat.SlashDay);

    format = FormatValidator.getDateTimeFormat('2020/01/01 00:00:00');
    expect(format).toEqual(DatetimeFormat.SlashSecond);

    format = FormatValidator.getDateTimeFormat('1662677585');
    expect(format).toEqual(DatetimeFormat.Timestamp);
  });
});
