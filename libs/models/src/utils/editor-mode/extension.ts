import * as CodeMirror from 'codemirror';

let graphInfo: string[] = [];
let wordsInQuery: string[] = [];

export function setGraphInfo(newInfo: string[]) {
  graphInfo = newInfo;
}

export function setWordsInQuery(newWordsInQuery: string[]) {
  wordsInQuery = newWordsInQuery;
}

/**
 * Remove current unfinished word from auto suggestion.
 *
 * @param curWord
 */
function removeUnfinishedCurWord(curWord: string) {
  const countCurWord = wordsInQuery.filter(word => word === curWord).length;
  // Remove curWord from auto suggestion list only when it does not exist in other place.
  if (countCurWord === 1) {
    wordsInQuery = wordsInQuery.filter(word => word !== curWord);
  }
}

/**
 * Retrieve the matched words from dictionary based on current input string.
 *
 * @export
 * @param {*} editor
 * @returns {{ list: string[]; from: CodeMirror.Position; to: CodeMirror.Position }}
 */
export function getHint(
  editor: any,
  dictionary: string[]
): { list: string[]; from: CodeMirror.Position; to: CodeMirror.Position } {
  const cur = editor.getCursor(),
    curLine = editor.getLine(cur.line);
  let start = cur.ch,
    end = start;

  while (end < curLine.length && /[\w$]+/.test(curLine.charAt(end))) {
    ++end;
  }
  while (start && /[\w@$]+/.test(curLine.charAt(start - 1))) {
    --start;
  }

  const curWord = start !== end && curLine.slice(start, end);
  const regex = new RegExp('^' + curWord, 'i');

  removeUnfinishedCurWord(curWord);
  const allHints = [...new Set(dictionary.concat(graphInfo, wordsInQuery))];

  return {
    list: (!curWord
      ? []
      : allHints.filter(item => item.match(regex))).sort(),
    from: CodeMirror.Pos(cur.line, start),
    to: CodeMirror.Pos(cur.line, end)
  };
}
