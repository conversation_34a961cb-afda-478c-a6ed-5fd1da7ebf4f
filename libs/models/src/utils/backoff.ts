import {
  MonoTypeOperatorFunction, Subject,
  range, throwError, timer, zip
} from 'rxjs';
import { mergeMap, retryWhen, takeUntil } from 'rxjs/operators';

/**
 * Retry an observable a configurable number of times with exponential backoff.
 *
 * @export
 * @template T
 * @param {number} maxTries The retry times.
 * @param {number} ms Initial backoff timeout.
 * @param {Subject<any>} destroyed Signal to stop retrying.
 * @returns {MonoTypeOperatorFunction<T>}
 */
export function backoff<T>(
  maxTries: number,
  ms: number,
  destroyed: Subject<any>
): MonoTypeOperatorFunction<T> {
  return retryWhen(
    attempts => zip(attempts, range(1, maxTries + 1))
      .pipe(
        takeUntil(destroyed),
        mergeMap(([error, i]) => {
          if (i > maxTries) {
            return throwError(error);
          }
          return timer(i * i * ms);
        })
      )
  );
}
