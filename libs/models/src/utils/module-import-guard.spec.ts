import { throwIfAlreadyLoaded } from './module-import-guard';

describe('ModuleImportGuard', () => {
  it('should throw error if the module is being re-imported', () => {
    const parentModule = 'ParentModule';
    const moduleName = 'CoreModule';
    const errorMessage = `${moduleName} has already been loaded. Import Core modules in the AppModule only.`;
    expect(() => throwIfAlreadyLoaded(parentModule, moduleName)).toThrowError(errorMessage);
  });

  it(`should not throw error if the module hasn't been imported`, () => {
    const parentModule = undefined;
    const moduleName = 'CoreModule';
    const errorMessage = `${moduleName} has already been loaded. Import Core modules in the AppModule only.`;
    expect(() => throwIfAlreadyLoaded(parentModule, moduleName)).not.toThrowError(errorMessage);
  });
});
