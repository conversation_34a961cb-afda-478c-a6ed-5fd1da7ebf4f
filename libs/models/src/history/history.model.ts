export class History<T> {
  private historyRecord: T[];
  private _currentPtr: number;

  /**
   * Creates an instance of History.
   *
   * @memberof History
   */
  constructor() {
    this.clear();
  }

  /**
   * Return the current history pointer.
   *
   * @readonly
   * @type {number}
   * @memberof History
   */
  get currentPointer(): number {
    return this._currentPtr;
  }

  /**
   * Clear the history.
   *
   * @memberof History
   */
  clear() {
    this.historyRecord = [];
    this._currentPtr = -1;
  }

  /**
   * Update one new record into the history.
   *
   * @param {T} newRecord
   * @returns {T}
   * @memberof History
   */
  update(newRecord: T): T {
    this.historyRecord = this.historyRecord.splice(0, this._currentPtr + 1);
    this.historyRecord.push(newRecord);
    this._currentPtr++;
    return newRecord;
  }

  /**
   * Remove the last record of the history.
   *
   * @returns {T}
   * @memberof History
   */
  removeLast(): T {
    this.historyRecord = this.historyRecord.splice(0, this._currentPtr);
    this._currentPtr--;
    return this.historyRecord[this._currentPtr];
  }

  /**
   * Back to previous state in the history.
   *
   * @returns {T}
   * @memberof History
   */
  undo(): T {
    if (this._currentPtr < 0) {
      return undefined;
    }
    if (this._currentPtr > 0) {
      this._currentPtr--;
    }
    return this.historyRecord[this._currentPtr];
  }

  /**
   * Forward to next state in the history.
   *
   * @returns {T}
   * @memberof History
   */
  redo(): T {
    if (this._currentPtr < 0) {
      return undefined;
    }
    if (this._currentPtr < this.historyRecord.length - 1) {
      this._currentPtr++;
    }
    return this.historyRecord[this._currentPtr];
  }

  /**
   * Get the record in history of given index.
   * If no index is given, return the current active record.
   *
   * @param {number} [index=this._currentPtr]
   * @returns {T}
   * @memberof History
   */
  getRecord(index = this._currentPtr): T {
    return this.historyRecord[index];
  }

  /**
   * Get the history length.
   * Should use _currentPtr instead of historyRecord.length
   * in case user undo some of the history.
   *
   * @returns {number}
   * @memberof History
   */
  getHistoryLength(): number {
    return this._currentPtr + 1;
  }

  /**
   * Get the length of the history record.
   *
   * @returns {number}
   * @memberof History
   */
  getHistoryRecordLength(): number {
    return this.historyRecord.length;
  }
}
