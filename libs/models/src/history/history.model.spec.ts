import { History } from './history.model';

describe('HistoryModel', () => {
  let history: History<string>;

  beforeEach(() => {
    history = new History<string>();
  });

  it('should get undefined if the history is clear', () => {
    expect(history.redo()).toBeUndefined();
    expect(history.redo()).toBeUndefined();

    expect(history.update('a')).toBe('a');
    history.clear();
    expect(history.redo()).toBeUndefined();
    expect(history.redo()).toBeUndefined();
  });

  it('should get the history correctly', () => {
    expect(history.update('a')).toBe('a');
    expect(history.redo()).toBe('a');
    expect(history.redo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');

    expect(history.update('b')).toBe('b');
    expect(history.update('c')).toBe('c');
    expect(history.update('d')).toBe('d');

    expect(history.undo()).toBe('c');
    expect(history.undo()).toBe('b');
    expect(history.redo()).toBe('c');
    expect(history.redo()).toBe('d');
    expect(history.redo()).toBe('d');
    expect(history.redo()).toBe('d');
    expect(history.redo()).toBe('d');
    expect(history.redo()).toBe('d');
    expect(history.undo()).toBe('c');
    expect(history.undo()).toBe('b');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.update('1')).toBe('1');
    expect(history.redo()).toBe('1');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.redo()).toBe('1');
    expect(history.update('2')).toBe('2');
    expect(history.redo()).toBe('2');
    expect(history.undo()).toBe('1');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.undo()).toBe('a');
    expect(history.update('s1')).toBe('s1');
    expect(history.update('s2')).toBe('s2');
    expect(history.undo()).toBe('s1');
    expect(history.undo()).toBe('a');
  });

  it('should get undefined if call undo or redo after clear', () => {
    expect(history.undo()).toBeUndefined();
    expect(history.redo()).toBeUndefined();
  });

});
