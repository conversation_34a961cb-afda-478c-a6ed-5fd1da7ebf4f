export interface RDBMSMeta {
  type: string;
  server: string;
  port: string;
  database: string;
  username: string;
  password: string;
  tableMetas: RDBMSTableMeta[];
}

export interface RDBMSTableMeta {
  tableName: string;
  columns: RDBMSColumnMeta[];
  primaryKey: string[];
  foreignKeys: RDBMSForeignKeyMeta[];
  indices: RDBMSIndexMeta[];
}

export interface RDBMSColumnMeta {
  name: string;
  type: string;
  size: number;
}

export interface RDBMSForeignKeyMeta {
  pkTableName: string;
  pkColumnName: string;
  fkTableName: string;
  fkColumnName: string;
}

export interface RDBMSIndexMeta {
  name: string;
  columns: string[];
}
