// jdbc name -> display name -> default port
export const supportedRDBMS = [
  // FIXME: Demo only, cannot be used in production since it's LGPL license.
  // ['mariadb:aurora', 'Amazon Aurora', '3306'],
  // TODO: License not ok for public use.
  // ['redshift', 'Amazon Redshift'],
  // ['sqlserver', 'Microsoft SQL Server', '1433'],
  // FIXME: Demo only, cannot be used in production since it's LGPL license.
  // ['mariadb', 'MariaDB', '3306'],
  ['postgresql', 'PostgreSQL', '5432'],
  ['mysql', 'MySQL', '3306'],
  // TODO: License not ok for public use.
  // ['db2', 'DB2', '5021'],
];

// Attribute type used in RDBMS and its corresponding type in TigerGraph.
export const rdbmsAttrType2GraphAttrType: [string, string[]][] = [
  ['INT', [
    'tinyint', 'smallint', 'mediumint', 'int', 'bigint', 'year', 'integer',
    'int2', 'int4', 'int8'
  ]],
  ['UINT', [
    'smallserial', 'serial', 'bigserial',
  ]],
  ['BOOL', [
    'boolean',
  ]],
  ['FLOAT', [
    'float', 'real'
  ]],
  ['DOUBLE', [
    'decimal', 'double', 'dec', 'numeric', 'double precision'
  ]],
  ['DATETIME', [
    'date', 'datetime', 'timestamp', 'datetime2', 'smalldatetime'
  ]],
];
