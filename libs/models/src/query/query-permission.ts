import { intersection } from 'lodash';
import { GLOBAL_GRAPH_NAME, GSQLPrivilege } from '../utils';
import { User, UserInfo } from '../user';
import { compare } from 'compare-versions';

const hasAllWriteQueriesPermssions = (user: User, graphName: string) => {
  // 1. super user can access all queries
  if (user.isSuperUser()) {
    return true;
  }

  // 2. FOR Roles
    // 2.1 graph admin can visit graph related queries(roles[graphName] has admin or designer)
    if (['admin', 'designer'].some(p => user.getRolesForGraph(graphName).includes(p))) {
    return true;
  }

  return false;
}

const hasPrivilegesToQuery = (
  user: User,
  graphName: string,
  queryName: string,
  privileges: GSQLPrivilege[]
) => {
  if (hasAllWriteQueriesPermssions(user, graphName)) {
    return true;
  }

  if (privileges.length <= 0 || !queryName) {
    return false;
  }

  // 3.3 privileges query level
  const queryPermissions = user.getChildPermissionsForGraph(graphName);
  const res = intersection(
    queryPermissions[queryName]?.privileges,
    privileges
  );

  return res.length > 0;
}

export const canEditQuery = (userInfo: UserInfo, graphName: string, queryName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    return hasPrivilegesToQuery(user, graphName, queryName, [GSQLPrivilege.Owner, GSQLPrivilege.UpdateQuery]);
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
};

export const canInstallQuery = (userInfo: UserInfo, graphName: string, queryName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    return hasPrivilegesToQuery(user, graphName, queryName, [GSQLPrivilege.Owner, GSQLPrivilege.InstallQuery]);
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
};

export const canDropQuery = (userInfo: UserInfo, graphName: string, queryName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    return hasPrivilegesToQuery(user, graphName, queryName, [GSQLPrivilege.Owner, GSQLPrivilege.DropQuery]);
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
};

export const canRunQuery = (userInfo: UserInfo, graphName: string, queryName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    return hasPrivilegesToQuery(user, graphName, queryName, [GSQLPrivilege.Owner, GSQLPrivilege.ExecuteQuery]);
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
};

export const canCreateQuery = (userInfo: UserInfo, graphName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    if (hasAllWriteQueriesPermssions(user, graphName) || user.hasPrivilege(GSQLPrivilege.CreateQuery, graphName)) {
      return true;
    }
    return false;
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
}
export const canReadQuery = (userInfo: UserInfo, graphName: string, queryName: string, tgVersion: string) => {
  const user = new User(userInfo);
  // cloud-portal workspace will return tg_version like `4.1.0-123456`.
  // In the context of SemVer, a pre-release version like 4.1.0-123456 has lower precedence than the associated normal version
  if (compare(tgVersion, '4.0.x', '>')) {
    return hasPrivilegesToQuery(user, graphName, queryName, [GSQLPrivilege.Owner, GSQLPrivilege.ReadQuery]);
  } else {
    return user.hasPrivilege(GSQLPrivilege.WriteQuery, graphName);
  }
}

