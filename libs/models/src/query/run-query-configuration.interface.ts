/**
 * Interface for run configuration.
 *
 * @export
 * @interface RunConfiguration
 */
export interface RunConfiguration {
  timeOut: {
    defaultValue: number;
    value: number;
    limit: boolean;
  }
  runMemory: {
    defaultValue: number;
    value: number;
    limit: boolean;
  }
  GSQLReplica: {
    defaultValue: number;
    value: number;
    limit: boolean;
  }
  queryProfile: boolean;
}
/**
 * Interface for run configuration.
 *
 * @export
 * @interface RunConfiguration
 */
export interface QueryFormConfiguration {
  timeOut: number,
  runMemory: number,
  timeOutLimit: boolean,
  memoryLimit: boolean,
  GSQLReplica?: number,
  GSQLReplicaLimit?: boolean,
  queryProfile?: boolean,
}
