
export const queryParamTypes: string[] = [
  'INT',
  'UINT',
  'BOOL',
  'FLOAT',
  'DOUBLE',
  'STRING',
  'DATETIME',
  'VERTEX',
  'LIST'
];

/**
 * QueryParamType represents one query parameter data type.
 * - type: one of queryParamTypes
 * - ... extensible
 *
 * @export
 * @interface QueryParamType
 */
export interface QueryParamType {
  type: string;
  [key: string]: any;
}

/**
 * VERTEX type query parameter.
 * - vertexType: one vertex type name in graph schema, or * representing any vertex type
 *
 * @export
 * @interface QueryParamVertexType
 * @extends {QueryParamType}
 */
export interface QueryParamVertexType extends QueryParamType {
  vertexType: string;
}

/**
 * LIST type query parameter.
 * Note: In GQuery we support SET and BAG parameter, but at endpoint level they are same as
 * accepting a list of elements. Only at runtime SET will de-duplicate the elements.
 * - elementType: SET element data type
 *
 * @export
 * @interface QueryParamListType
 * @extends {QueryParamType}
 */
export interface QueryParamListType extends QueryParamType {
  elementType: QueryParamType;
}

export interface QueryParamMapType extends QueryParamType {
  keyType: QueryParamType;
  valueType: QueryParamType;
}
