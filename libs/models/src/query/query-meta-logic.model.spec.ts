import { QueryMetaLogic } from './query-meta-logic.model';
import {
  QueryParamVertexType,
  QueryParamListType,
  QueryParamType
} from './query-param-type.interface';
import { GsqlQueryMeta } from './gsql-query-meta.interface';

describe('QueryMetaLogicModel', () => {

  it('should successfully load from GsqlQueryMeta', () => {
    const mockGsqlQueryMeta: GsqlQueryMeta = {
      optimizedLevel: 0,
      installed: true,
      enabled: true,
      callerQueries: [],
      installing: false,
      installMode: 'UDF',
      graphUpdate: false,
      isHidden: true,
      isACLSpecified: true,
      endpoint: {
        query: {
          MyGraph: {
            q2: {
              'GET/POST': {
                summary: 'This is query entrance',
                scheduler: 'GleQueryScheduler',
                payload: [{
                  rule: 'AS_QUERY_STRING'
                }],
                function: 'queryDispatcher',
                action: 'query',
                parameters: {
                  b: {
                    type: 'BOOL',
                    min_count: 0,
                    index: 0
                  },
                  d: {
                    type: 'DOUBLE',
                    min_count: 0,
                    index: 1
                  },
                  city_set: {
                    max_count: 9999,
                    id_type: 'City',
                    type: 'STRING',
                    is_id: 'true',
                    min_count: 0,
                    index: 2
                  },
                  f: {
                    type: 'FLOAT',
                    min_count: 0,
                    index: 3,
                  },
                  query: {
                    default: 'q2',
                    type: 'STRING',
                    index: 4
                  },
                  set_int: {
                    max_count: 9999,
                    type: 'INT64',
                    min_count: 0,
                    index: 5
                  },
                  i: {
                    type: 'INT64',
                    min_count: 0,
                    index: 6
                  },
                  vc: {
                    id_type: 'City',
                    type: 'STRING',
                    is_id: 'true',
                    min_count: 0,
                    index: 7
                  },
                  dt: {
                    type: 'STRING',
                    min_count: 0,
                    index: 8
                  },
                  bag_any_vertex: {
                    max_count: 9999,
                    id_type: '$bag_any_vertex.type',
                    type: 'STRING',
                    is_id: 'true',
                    min_count: 0,
                    index: 9
                  },
                  ui: {
                    type: 'UINT64',
                    min_count: 0,
                    index: 10
                  },
                  bag_int: {
                    max_count: 9999,
                    type: 'INT64',
                    min_count: 0,
                    index: 11
                  },
                  'bag_any_vertex.type': {
                    max_count: 9999,
                    type: 'STRING',
                    min_count: 0,
                    index: 12
                  },
                  any_vertex: {
                    id_type: '$any_vertex.type',
                    type: 'STRING',
                    is_id: 'true',
                    min_count: 0,
                    index: 13
                  },
                  'any_vertex.type': {
                    type: 'STRING',
                    min_count: 0,
                    index: 14
                  },
                },
                target: 'GPE'
              }
            }
          }
        }
      },
      name: 'q2',
      code: 'original code',
      draft: 'draft code',
      graphName: 'MyGraph'
    };

    const queryMeta = QueryMetaLogic.loadFromGSQL('MyGraph', mockGsqlQueryMeta);

    expect(queryMeta.queryName).toBe('q2');
    expect(queryMeta.installed).toBeTruthy();
    expect(queryMeta.installing).toBeFalsy();
    expect(queryMeta.draftCode).toBe('draft code');
    expect(queryMeta.originalCode).toBe('original code');
    expect(queryMeta.isHidden).toBeTruthy();
    expect(queryMeta.isACLSpecified).toBeTruthy();

    // check params
    expect(queryMeta.params.length).toBe(12);

    // param 0: bool b
    expect(queryMeta.params[0].paramName).toBe('b');
    expect(queryMeta.params[0].paramType.type).toBe('BOOL');
    // param 1: double d
    expect(queryMeta.params[1].paramName).toBe('d');
    expect(queryMeta.params[1].paramType.type).toBe('DOUBLE');
    // param 2: set<vertex<City>> city_set
    expect(queryMeta.params[2].paramName).toBe('city_set');
    expect(queryMeta.params[2].paramType.type).toBe('LIST');
    expect((<QueryParamListType>queryMeta.params[2].paramType).elementType.type).toBe('VERTEX');
    expect((<QueryParamVertexType>(<QueryParamListType>queryMeta.params[2].paramType)
      .elementType).vertexType).toBe('City');
    // param 3: float f
    expect(queryMeta.params[3].paramName).toBe('f');
    expect(queryMeta.params[3].paramType.type).toBe('FLOAT');
    // param 4: set<int> set_int
    expect(queryMeta.params[4].paramName).toBe('set_int');
    expect(queryMeta.params[4].paramType.type).toBe('LIST');
    expect((<QueryParamListType>queryMeta.params[4].paramType).elementType.type).toBe('INT');
    // param 5: int i
    expect(queryMeta.params[5].paramName).toBe('i');
    expect(queryMeta.params[5].paramType.type).toBe('INT');
    // param 6: vertex<City> vc
    expect(queryMeta.params[6].paramName).toBe('vc');
    expect(queryMeta.params[6].paramType.type).toBe('VERTEX');
    expect((<QueryParamVertexType>queryMeta.params[6].paramType).vertexType).toBe('City');
    // param 7: string dt
    expect(queryMeta.params[7].paramName).toBe('dt');
    expect(queryMeta.params[7].paramType.type).toBe('STRING');
    // param 8: bag<vertex> bag_any_vertex
    expect(queryMeta.params[8].paramName).toBe('bag_any_vertex');
    expect(queryMeta.params[8].paramType.type).toBe('LIST');
    expect((<QueryParamListType>queryMeta.params[8].paramType).elementType.type).toBe('VERTEX');
    expect((<QueryParamVertexType>(<QueryParamListType>queryMeta.params[8].paramType)
      .elementType).vertexType).toBe('*');
    // param 9: uint ui
    expect(queryMeta.params[9].paramName).toBe('ui');
    expect(queryMeta.params[9].paramType.type).toBe('UINT');
    // param 10: bag<int> bag_int
    expect(queryMeta.params[10].paramName).toBe('bag_int');
    expect(queryMeta.params[10].paramType.type).toBe('LIST');
    expect((<QueryParamListType>queryMeta.params[10].paramType).elementType.type).toBe('INT');
    // param 11: vertex any_vertex
    expect(queryMeta.params[11].paramName).toBe('any_vertex');
    expect(queryMeta.params[11].paramType.type).toBe('VERTEX');
    expect((<QueryParamVertexType>queryMeta.params[11].paramType).vertexType).toBe('*');
  });

  it('should successfully load from GsqlQueryMeta with empty endpoint', () => {
    const mockGsqlQueryMeta: GsqlQueryMeta = {
      optimizedLevel: 0,
      installed: true,
      enabled: true,
      callerQueries: [],
      installing: false,
      installMode: 'UDF',
      graphUpdate: false,
      isHidden: false,
      isACLSpecified: false,
      endpoint: {},
      name: 'q2',
      code: 'original code',
      draft: 'draft code',
      graphName: 'graph_name'
    };

    const queryMeta = QueryMetaLogic.loadFromGSQL('MyGraph', mockGsqlQueryMeta);

    expect(queryMeta.queryName).toBe('q2');
    expect(queryMeta.installed).toBeTruthy();
    expect(queryMeta.installing).toBeFalsy();
    expect(queryMeta.draftCode).toBe('draft code');
    expect(queryMeta.originalCode).toBe('original code');
    expect(queryMeta.isHidden).toBeFalsy();
    expect(queryMeta.isACLSpecified).toBeFalsy();

    // check params
    expect(queryMeta.params.length).toBe(0);
  });

  it('should unescape string param default value', () => {
    const paramType: QueryParamType = {
      type: 'STRING',
    };
    const testCases: { value: string; expected: string }[] = [
      {
        value: "\\\"inside quotes\\\"",
        expected: "\"inside quotes\"",
      },
      {
        value: "test\\\\",
        expected: "test\\"
      },
      {
        value: "test\\",
        expected: "test\\",
      },
      {
        value: "test\\atest",
        expected: "testatest",
      },
      {
        value: "test\\ntest",
        expected: "test\ntest",
      },
      {
        value: "test\\ttest",
        expected: "test\ttest",
      }
    ];

    for (let tc of testCases) {
      expect(QueryMetaLogic.handleParamDefaultValue(paramType, tc.value)).toBe(tc.expected);
    }
  });
});
