import { GsqlParameter, GsqlQueryMeta } from "./gsql-query-meta.interface";
import { QueryMeta } from "./query-meta.interface";
import {
  QueryParamType,
  QueryParamVertexType,
  QueryParamMapType,
} from "./query-param-type.interface";
import { QueryParam } from "./query-param.interface";

const handlePrimitiveType = (queryParamType: string) => {
  // fix INT64 and UINT64
  if (queryParamType === "INT64" || queryParamType === "INT32") {
    return "INT";
  }

  if (queryParamType === "UINT64" || queryParamType === "UINT32") {
    return "UINT";
  }

  return queryParamType;
};

export class QueryMetaLogic {
  static loadFromGSQL(graphName: string, gsqlQueryMeta: GsqlQueryMeta): QueryMeta {
    // Init the query meta object
    const queryMeta: QueryMeta = {
      queryName: gsqlQueryMeta.name,
      installed: gsqlQueryMeta.installed,
      enabled: gsqlQueryMeta.enabled,
      callerQueries: gsqlQueryMeta.callerQueries,
      installing: gsqlQueryMeta.installing,
      installMode: gsqlQueryMeta.installMode || "UDF",
      params: [],
      originalCode: gsqlQueryMeta.code,
      draftCode: gsqlQueryMeta.draft,
      graphUpdate: gsqlQueryMeta.graphUpdate,
      isHidden: gsqlQueryMeta.isHidden,
      isACLSpecified: gsqlQueryMeta.isACLSpecified,
      syntax: gsqlQueryMeta.syntax || "GSQL",
    };

    // get the parameters
    const parameters = gsqlQueryMeta.endpoint?.query
      ? gsqlQueryMeta.endpoint.query[graphName][gsqlQueryMeta.name]["GET/POST"].parameters
      : {};

    queryMeta.params = this.convertGSQLParameters(parameters);
    return queryMeta;
  }

  static convertGSQLParameters(parameters: Record<string, GsqlParameter>): QueryParam[] {
    const result: QueryParam[] = [];

    const orderedParams = Object.keys(parameters)
      // query itself is ignored, vertex type is ignored
      .filter((paramName) => paramName !== "query" && !paramName.endsWith(".type"))
      .sort((p1, p2) => (parameters[p1].index || 0) - (parameters[p2].index || 0));

    orderedParams.forEach((paramName) => {
      // const param type based on param meta
      const paramMeta = parameters[paramName];
      const queryParamType: QueryParamType = {
        type: handlePrimitiveType(paramMeta.type),
      };
      // handle VERTEX type attribute
      if (paramMeta.is_id !== undefined) {
        queryParamType.type = "VERTEX";
        // if paramName.type exists in parameters, means the vertex is any type vertex
        if (paramName + ".type" in parameters) {
          (<QueryParamVertexType>queryParamType).vertexType = "*";
        } else {
          // otherwise the vertex type is given in id_type
          (<QueryParamVertexType>queryParamType).vertexType = paramMeta.id_type;
        }
      }

      // Push the param meta into the query meta.
      // If map_entry_types exist in the param, it is a map params
      // Else If max_count exists in the param meta and map_entry_types not exist in the param, it is a list param
      // otherwise it is normal param
      let paramType: QueryParamType;
      if (Array.isArray(paramMeta.map_entry_types) && paramMeta.map_entry_types.length >= 2) {
        paramType = {
          type: "MAP",
          keyType: {
            type: handlePrimitiveType(paramMeta.map_entry_types[0].type),
          },
          valueType: {
            type: handlePrimitiveType(paramMeta.map_entry_types[1].type),
          },
        } as QueryParamMapType;
      } else if (paramMeta.max_count && paramMeta.max_count > 1) {
        paramType = {
          type: "LIST",
          elementType: queryParamType,
        };
      } else {
        paramType = queryParamType;
      }

      // If the parameter has default value, set default value.
      if (typeof parameters[paramName].defaultValue !== 'undefined') {
        const defaultValue = parameters[paramName].defaultValue;
        result.push({
          paramName: paramName,
          paramType: paramType,
          paramDefaultValue: this.handleParamDefaultValue(paramType, defaultValue),
        });
        // restpp endpoints use "default"
      } else if (typeof parameters[paramName].default !== 'undefined') {
        const defaultValue = parameters[paramName].default;
        result.push({
          paramName: paramName,
          paramType: paramType,
          paramDefaultValue: this.handleParamDefaultValue(paramType, defaultValue),
        });
      }
      else {
        result.push({
          paramName: paramName,
          paramType: paramType,
        });
      }
    });

    return result;
  }

  static handleParamDefaultValue(paramType: QueryParamType, paramDefaultValue: any) {
    return paramType.type === "STRING" ? this.unescapeStr(paramDefaultValue) : paramDefaultValue;
  }

  static unescapeStr(str: string): string {
    return str.replace(/\\(.)/g, function (match, char) {
      if (char == "\\") return "\\";
      if (char == "n") return "\n";
      if (char == "t") return "\t";
      if (char == "") return "";
      return char;
    });
  }
}
