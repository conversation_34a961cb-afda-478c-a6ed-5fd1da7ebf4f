
interface AddQueriesToGSQLResultInfo {
  name: string;
  errorMessage: string;
}

export interface AddQueriesToGSQLResult {
  success: AddQueriesToGSQLResultInfo[];
  failed: AddQueriesToGSQLResultInfo[];
}

export interface InstallQueriesResult {
  success: string[];
  failedToDeleteDraft: string[];
}

export interface AlgorithmResult {
  name: string;
  errorMessage: string;
  algorithmName: string;
}

export interface InstallQueryTemplatesResult {
  success: AlgorithmResult[];
  failed: AlgorithmResult[];
}
