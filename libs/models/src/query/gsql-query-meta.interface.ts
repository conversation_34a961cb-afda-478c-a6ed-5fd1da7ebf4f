import { QuerySyntax } from "./query-meta.interface";

export interface GsqlParameter {
  type: string;
  index?: number;
  max_count?: number;
  is_id?: "true";
  id_type?: string;
  defaultValue?: any;
  map_entry_types?: { type: string }[];
  [key: string]: any;
}

export interface GsqlQueryMetaParameters {
  [paramName: string]: GsqlParameter;
}

/**
 * Meta information exported from gsql server.
 *
 * @export
 * @interface GsqlQueryMeta
 */
export interface GsqlQueryMeta {
  optimizedLevel: number;
  installed: boolean;
  enabled: boolean;
  callerQueries: string[];
  installing: boolean;
  installMode: string;
  graphUpdate: boolean;
  isHidden: boolean;
  isACLSpecified: boolean;
  endpoint: {
    query?: {
      [graphName: string]: {
        [queryName: string]: {
          "GET/POST": {
            parameters: GsqlQueryMetaParameters;
            [key: string]: any;
          };
        };
      };
    };
  };
  syntax?: QuerySyntax;
  name: string;
  draft: string;
  code: string;
  [key: string]: any;
}
