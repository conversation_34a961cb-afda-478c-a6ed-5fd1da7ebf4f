import { InputQuestion } from './question-input.model';
import { KeyValueQuestion } from './question-key-value.model';
import { UdtQuestion } from './question-udt.model';

describe('KeyValueModel', () => {

  it('should construct a key value question from empty config successfully', () => {
    const questionKeyValue = new KeyValueQuestion();
    expect(questionKeyValue.controlType).toBe('keyValue');
  });

  it('should construct a key value question from a option', () => {
    const options = {
      controlType: 'keyValue',
      mapKey: {
        controlType: 'input',
        value: 'hello',
        variableType: 'string'
      },
      mapValue: {
        controlType: 'input',
        value: 123,
        variableType: 'number',
        numberType: 'integer'
      }
    };
    const questionKeyValue = new KeyValueQuestion(options);
    expect(questionKeyValue.controlType).toBe('keyValue');
    expect(questionKeyValue.mapKey.controlType).toBe('input');
    expect(questionKeyValue.mapKey.value).toBe('hello');
    expect(questionKeyValue.mapValue.controlType).toBe('input');
    expect((<InputQuestion>questionKeyValue.mapValue).numberType).toBe('integer');
    expect(questionKeyValue.mapValue.value).toBe(123);
  });

  it('should construct a key value question from a option whose value type is udt', () => {
    const options = {
      controlType: 'keyValue',
      mapKey: {
        controlType: 'input',
        value: 'a',
        variableType: 'string'
      },
      mapValue: {
        controlType: 'udt'
      }
    };
    const questionKeyValue = new KeyValueQuestion(options);
    expect(questionKeyValue.controlType).toBe('keyValue');
    expect(questionKeyValue.mapKey.controlType).toBe('input');
    expect(questionKeyValue.mapKey.value).toBe('a');
    expect(questionKeyValue.mapValue.controlType).toBe('udt');
    expect(questionKeyValue.mapValue instanceof UdtQuestion).toBeTruthy();
  });
});
