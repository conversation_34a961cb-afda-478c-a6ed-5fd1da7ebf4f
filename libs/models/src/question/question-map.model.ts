import { QuestionBase } from './question-base.model';
import { CheckboxQuestion } from './question-checkbox.model';
import { InputQuestion } from './question-input.model';
import { ListQuestion } from './question-list.model';
import { SelectQuestion } from './question-select.model';

/**
 * Multi map question extends List question and can accept multiple inputs.
 * - type: 'text' | 'number'
 *
 * @export
 * @class ListQuestion
 * @extends {ListQuestion}
 */
export class MapQuestion<K, V> extends QuestionBase<{ key: K, value: V }> {
  controlType = 'map';
  incrementalPtr: number = 0;
  questions: { kid: string; key: QuestionBase<any>; value: QuestionBase<any> }[] = [];
  templateOptions: any;

  constructor(options: any = {}) {
    super(options);
    this.templateOptions = {
      keyOptions: options.keyOptions,
      valueOptions: options.valueOptions,
    }
  }

  addQuestion() {
    const newQuestion = this.constructMapItemQuestion(this.templateOptions);
    // Need assign new key to the question, we assign an integer
    // between 0 and questions length, which is not occupied yet.
    this.questions.push(newQuestion);
  }

  constructMapItemQuestion(templateOptions) {
    const keyQuestion = this.constructQuestion(templateOptions.keyOptions);
    const valueQuestion = this.constructQuestion(templateOptions.valueOptions);
    // Need assign new key to the question, we assign an integer
    // between 0 and questions length, which is not occupied yet.
    const kid = (this.incrementalPtr++).toString();
    keyQuestion.key = `${kid}.${keyQuestion.key}`;
    valueQuestion.key = `${kid}.${valueQuestion.key}`;

    return { kid, key: keyQuestion, value: valueQuestion  };
  }

  protected constructQuestion(option: any): QuestionBase<any> {
    switch (option.controlType) {
      case 'input': {
        return new InputQuestion(option);
      }
      case 'checkbox': {
        return new CheckboxQuestion(option);
      }
      case 'select': {
        return new SelectQuestion(option);
      }
    }
    return new QuestionBase(option);
  }
}
