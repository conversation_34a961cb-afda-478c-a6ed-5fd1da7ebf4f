import { ListQuestion } from './question-list.model';
import { SelectQuestion } from './question-select.model';

describe('QuestionListModel', () => {

  it('should construct a list of questions from empty config successfully', () => {
    const questionList = new ListQuestion();
    expect(questionList.controlType).toBe('list');
  });

  it('should successfully add multiple questions of the given type into the question list', () => {
    // input list
    const questionInputList = new ListQuestion({
      templateOption: {
        controlType: 'input'
      }
    });
    expect(questionInputList.controlType).toBe('list');
    questionInputList.addQuestion();
    questionInputList.addQuestion();
    questionInputList.addQuestion();
    expect(questionInputList.questions.length).toBe(3);
    expect(questionInputList.questions[0].controlType).toBe('input');
    expect(questionInputList.questions[1].controlType).toBe('input');
    expect(questionInputList.questions[2].controlType).toBe('input');
    expect(questionInputList.questions[0].key).toBe('0');
    expect(questionInputList.questions[1].key).toBe('1');
    expect(questionInputList.questions[2].key).toBe('2');

    // checkbox list
    const questionCheckboxList = new ListQuestion({
      templateOption: {
        controlType: 'checkbox'
      }
    });
    expect(questionCheckboxList.controlType).toBe('list');
    questionCheckboxList.addQuestion();
    questionCheckboxList.addQuestion();
    expect(questionCheckboxList.questions.length).toBe(2);
    expect(questionCheckboxList.questions[0].controlType).toBe('checkbox');
    expect(questionCheckboxList.questions[1].controlType).toBe('checkbox');
    expect(questionCheckboxList.questions[0].key).toBe('0');
    expect(questionCheckboxList.questions[1].key).toBe('1');

    // select list
    const questionSelectList = new ListQuestion({
      templateOption: {
        controlType: 'select',
        options: [{ key: 'k1', value: 'v1' }, { key: 'k2', value: 'v2' }]
      }
    });
    expect(questionSelectList.controlType).toBe('list');
    questionSelectList.addQuestion();
    questionSelectList.addQuestion();
    expect(questionSelectList.questions.length).toBe(2);
    expect(questionSelectList.questions[0].controlType).toBe('select');
    expect((<SelectQuestion>questionSelectList.questions[0]).options[1].key).toBe('k2');
    expect((<SelectQuestion>questionSelectList.questions[0]).options[0].value).toBe('v1');
    expect(questionSelectList.questions[1].controlType).toBe('select');
    expect(questionSelectList.questions[0].key).toBe('0');
    expect(questionSelectList.questions[1].key).toBe('1');

    // vertex list
    const questionVertexList = new ListQuestion({
      templateOption: {
        controlType: 'vertex'
      }
    });
    expect(questionVertexList.controlType).toBe('list');
    questionVertexList.addQuestion();
    questionVertexList.addQuestion();
    questionVertexList.addQuestion();
    expect(questionVertexList.questions.length).toBe(3);
    expect(questionVertexList.questions[0].controlType).toBe('vertex');
    expect(questionVertexList.questions[1].controlType).toBe('vertex');
    expect(questionVertexList.questions[2].controlType).toBe('vertex');
    expect(questionVertexList.questions[0].key).toBe('0');
    expect(questionVertexList.questions[1].key).toBe('1');
    expect(questionVertexList.questions[2].key).toBe('2');

    // key-value list
    const questionKeyValueList = new ListQuestion({
      templateOption: {
        controlType: 'keyValue'
      }
    });
    expect(questionKeyValueList.controlType).toBe('list');
    questionKeyValueList.addQuestion();
    expect(questionKeyValueList.questions.length).toBe(1);
    expect(questionKeyValueList.questions[0].controlType).toBe('keyValue');
    expect(questionKeyValueList.questions[0].key).toBe('0');

    // udt list
    const questionUdtList = new ListQuestion({
      templateOption: {
        controlType: 'udt'
      }
    });
    expect(questionUdtList.controlType).toBe('list');
    questionUdtList.addQuestion();
    expect(questionUdtList.questions.length).toBe(1);
    expect(questionUdtList.questions[0].controlType).toBe('udt');
    expect(questionUdtList.questions[0].key).toBe('0');
  });
});
