import { QuestionBase } from './question-base.model';
import { InputQuestion } from './question-input.model';
import { UdtQuestion } from './question-udt.model';

export class KeyValueQuestion<T> extends QuestionBase<T> {
  controlType = 'keyValue';
  map: QuestionBase<any>[];
  mapKey: QuestionBase<any>;
  mapValue: QuestionBase<any>;

  constructor(options: any = {}) {
    super(options);

    this.map = [];
    if (options.mapKey) {
      this.mapKey = new InputQuestion(options.mapKey);
      this.mapKey.key = 'key';
      this.map.push(this.mapKey);
    }
    if (options.mapValue) {
      this.mapValue = options.mapValue.controlType === 'udt' ?
        new UdtQuestion(options.mapValue) :
        new InputQuestion(options.mapValue);
      this.mapValue.key = 'value';
      this.map.push(this.mapValue);
    }
  }
}
