import { CheckboxQuestion } from './question-checkbox.model';

describe('QuestionCheckboxModel', () => {

  it('should construct a checkbox question from empty config successfully', () => {
    const question = new CheckboxQuestion();
    expect(question.controlType).toBe('checkbox');
  });

  it('should construct a checkbox question from config successfully', () => {
    const question = new CheckboxQuestion({});
    expect(question.controlType).toBe('checkbox');
  });
});
