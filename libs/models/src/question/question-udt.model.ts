import { QuestionBase } from './question-base.model';
import { InputQuestion } from './question-input.model';
import { SelectQuestion } from './question-select.model';

/**
 * UDT question has input or select question as its fields.
 *
 * @export
 * @class UdtQuestion
 * @extends {QuestionBase<T>}
 * @template T
 */
export class UdtQuestion<T> extends QuestionBase<T> {
  controlType = 'udt';
  fieldsOptions: QuestionBase<any>[];

  constructor(options: any = {}) {
    super(options);

    this.fieldsOptions = [];

    if (options.fields) {
      options.fields.forEach(field => {
        const newQuestion = this.constructQuestion(field);
        this.fieldsOptions.push(newQuestion);
      });
    }
  }

  private constructQuestion(option: any): QuestionBase<any> {
    switch (option.controlType) {
      case 'input': {
        return new InputQuestion(option);
      }
      case 'select': {
        option.value = option.value.toString();
        return new SelectQuestion(option);
      }
    }
    return new QuestionBase(option);
  }
}
