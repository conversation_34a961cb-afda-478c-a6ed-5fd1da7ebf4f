import { SelectQuestion } from './question-select.model';

describe('QuestionSelectModel', () => {

  it('should construct a select question from empty config successfully', () => {
    const question = new SelectQuestion();
    expect(question.controlType).toBe('select');
  });

  it('should construct a select question from config successfully', () => {
    const question = new SelectQuestion({});
    expect(question.controlType).toBe('select');
  });
});
