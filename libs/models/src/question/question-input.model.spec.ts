import { InputQuestion } from './question-input.model';

describe('QuestionInputModel', () => {

  it('should construct an input question from empty config successfully', () => {
    const question = new InputQuestion();
    expect(question.key).toBe('');
  });

  it('should construct an input question from config successfully', () => {
    const question = new InputQuestion({
      type: 'text',
    });
    expect(question.type).toBe('text');
  });
});
