/**
 * Base class for question.
 *
 * @export
 * @class QuestionBase
 * @template T
 */
export class QuestionBase<T> {
  value: T;
  key: string;
  label: string;
  disabled: boolean;
  required: boolean;
  controlType: string;
  placeholder: string;
  variableType: string;
  usingNullParam: boolean;

  constructor(options: {
    value?: T,
    key?: string,
    label?: string,
    disabled?: boolean,
    required?: boolean,
    controlType?: string,
    placeholder?: string,
    variableType?: string,
    usingNullParam?: boolean
  } = {}) {
    this.value = options.value;
    this.key = options.key || '';
    this.label = options.label || '';
    this.disabled = !!options.disabled;
    this.required = !!options.required;
    this.controlType = options.controlType || '';
    this.placeholder = options.placeholder || '';
    this.variableType = options.variableType || '';
    this.usingNullParam = !!options.usingNullParam;
  }
}
