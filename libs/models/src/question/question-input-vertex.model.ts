import { QuestionBase } from './question-base.model';

/**
 * Vertex type and id information.
 *
 * @export
 * @interface VertexTypeAndId
 */
export interface VertexTypeAndId {
  type: string;
  id: string;
}

/**
 * Input one vertex.
 * - fixedVertexType: whether vertex type is fixed or flexible that need user to provide
 *
 * @export
 * @class InputVertexQuestion
 * @extends {QuestionBase<VertexTypeAndId>}
 */
export class InputVertexQuestion extends QuestionBase<VertexTypeAndId> {
  controlType = 'vertex';
  fixedVertexType: boolean;

  constructor(options: any = {}) {
    super(options);
    this.fixedVertexType = !!options.fixedVertexType;
  }
}
