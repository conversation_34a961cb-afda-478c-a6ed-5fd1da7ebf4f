import { InputVertexQuestion } from './question-input-vertex.model';

describe('QuestionInputVertexModel', () => {

  it('should construct an input vertex question from empty config successfully', () => {
    const question = new InputVertexQuestion();
    expect(question.fixedVertexType).toBeFalsy();
  });

  it('should construct an input vertex question from config successfully', () => {
    const question = new InputVertexQuestion({
      fixedVertexType: true
    });
    expect(question.fixedVertexType).toBeTruthy();
  });
});
