import { UdtQuestion } from './question-udt.model';

describe('UdtQuestionModel', () => {
  it('should construct a udt question from empty config successfully', () => {
    const question = new UdtQuestion();
    expect(question.controlType).toBe('udt');
  });

  it('should construct a udt questions from an option', () => {
    const option = {
      key: 'testAttr',
      controlType: 'udt',
      variableType: 'testTuple',
      fields: [
        {
          key: 'field1',
          controlType: 'input',
        },
        {
          key: 'field2',
          controlType: 'select',
          value: 'true'
        }
      ]
    };
    const question = new UdtQuestion(option);
    expect(question.controlType).toBe('udt');
    expect(question.key).toEqual('testAttr');
    expect(question.variableType).toEqual('testTuple');
    expect(question.fieldsOptions.length).toBe(2);
    expect(question.fieldsOptions[0].controlType).toBe('input');
    expect(question.fieldsOptions[1].controlType).toBe('select');
  });
});
