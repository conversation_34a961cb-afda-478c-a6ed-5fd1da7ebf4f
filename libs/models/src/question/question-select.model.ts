import { QuestionBase } from './question-base.model';

/**
 * Select question allows users to select a value from a list of values.
 *
 * @export
 * @class SelectQuestion
 * @extends {QuestionBase<string>}
 */
export class SelectQuestion extends QuestionBase<string> {
  multiple: boolean;
  controlType = 'select';
  options: { key: string, value: string }[];

  constructor(options: any = {}) {
    super(options);

    this.multiple = !!options.multiple;
    this.options = options.options || [];
  }
}
