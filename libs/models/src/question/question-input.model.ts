import { QuestionBase } from './question-base.model';

/**
 * Input question allows users to input multiple HTML5 types.
 * - type: 'text' | 'number'
 * - numberType: 'integer' | 'real'
 *
 * @export
 * @class InputQuestion
 * @extends {(QuestionBase<string | number>)}
 */
export class InputQuestion extends QuestionBase<string | number> {
  type: string;
  min: number;
  max: number;
  minLength: number;
  maxLength: number;
  numberType: string;
  isDateType: boolean;
  controlType = 'input';

  constructor(options: any = {}) {
    super(options);

    this.type = options.type || '';
    this.min = options.min;
    this.max = options.max;
    this.minLength = options.minLength;
    this.maxLength = options.maxLength;
    this.isDateType = options.isDateType;
    this.numberType = options.numberType;
  }
}
