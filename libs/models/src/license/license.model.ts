export enum LicenseStatus {
  Valid = 'Valid',
  Invalid = 'Invalid',
  Expired = 'Expired',
  NoLicense = 'No License'
}

/**
 * TigerGraph license information.
 *
 * @export
 * @interface License
 */
export interface License {
  Issuer: string;
  Audience: string;
  EndTime: number;
  IssueTime: number;
  Edition: string;
  Host: {
    MaxCPUCore: number;
    MaxPhysicalMemoryBytes: number;
    MaxClusterNodeNumber: number;
  };
  Topology: {
    MaxVertexNumber: number;
    MaxEdgeNumber: number;
    MaxGraphNumber: number;
    MaxTopologyBytes: number;
  };
  GST: {
    Enable: boolean;
    ZoomChartsLicense: string;
  };
  RuntimeMemory: {
    MaxUserResidentSetBytes: number;
  };
  Status: LicenseStatus;
}
