import { GSQLDataSourceLogic } from './gsql-data-source-logic';
import { LoadingJobPrefix } from './loading-job.model';

describe('GSQLDataSourceLogic', () => {
  it('should check Kafka loading job correctly', () => {
    expect(GSQLDataSourceLogic.isKafkaLoadingJob(LoadingJobPrefix.FILE + 'jobName')).toBeFalsy();
    expect(GSQLDataSourceLogic.isKafkaLoadingJob(LoadingJobPrefix.GCS + 'jobName1')).toBeTruthy();
    expect(GSQLDataSourceLogic.isKafkaLoadingJob(LoadingJobPrefix.S3 + 'jobName2')).toBeTruthy();
    expect(GSQLDataSourceLogic.isKafkaLoadingJob(LoadingJobPrefix.ABS + 'jobName3')).toBeTruthy();
  });
});
