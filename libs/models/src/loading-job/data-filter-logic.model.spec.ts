import { BaseCondition, ComparisonCondition, LoadingVariable, PredicateOperator,
  RelationalOperator, StringConstant
} from '../expression';
import { JSONDataFieldSchema } from './data-set.interface';
import { DataFilterLogic } from './data-filter-logic.model';

describe('LoadingStatementLogic', () => {

  it('should process the exprModel correctly', () => {
    // Data filter string: (($0 == "UTF-7") OR ($2 == "10"))
    // GSQL where clause: WHERE ($"encoding" == "UTF-7" OR $"indent":"length" == "10")
    const currentDataSchema = <JSONDataFieldSchema[]>[
      {
        'level': 0,
        'name': 'encoding',
        'path': [
          'encoding'
        ],
        'type': 'string'
      },
      {
        'level': 0,
        'name': 'indent',
        'path': [
          'indent'
        ],
        'type': 'object'
      },
      {
        'level': 1,
        'name': 'length',
        'path': [
          'indent',
          'length'
        ],
        'type': 'number'
      },
      {
        'level': 1,
        'name': 'use_space',
        'path': [
          'indent',
          'use_space'
        ],
        'type': 'null'
      },
      {
        'level': 0,
        'name': 'plug-ins',
        'path': [
          'plug-ins'
        ],
        'type': 'array'
      }
    ];
    const OrConditionForExpressionModel = <BaseCondition>{
      'operands': [
         new ComparisonCondition([
          new LoadingVariable(0),
          <StringConstant>{
            'value': 'UTF-7',
            'valueType': 'String'
          }
        ],
        RelationalOperator.Equal
        ),
        new ComparisonCondition([
          new LoadingVariable(2),
          <StringConstant>{
            'value': '10',
            'valueType': 'String'
          }
        ],
        RelationalOperator.Equal
        )
      ],
      'operator': PredicateOperator.Or,
      'operandsTypes': [[]]
    };
    const firstLoadingVariable = new LoadingVariable(0);
    firstLoadingVariable.value = `"encoding"`;
    const secondLoadingVariable = new LoadingVariable(2);
    secondLoadingVariable.value = `"indent":"length"`;
    const OrConditionForGSQL = <BaseCondition>{
      'operands': [
         new ComparisonCondition([
          firstLoadingVariable,
          <StringConstant>{
            'value': 'UTF-7',
            'valueType': 'String'
          }
        ],
        RelationalOperator.Equal
        ),
        new ComparisonCondition([
          secondLoadingVariable,
          <StringConstant>{
            'value': '10',
            'valueType': 'String'
          }
        ],
        RelationalOperator.Equal
        )
      ],
      'operator': PredicateOperator.Or,
      'operandsTypes': [[]]
    };

    DataFilterLogic.convertJSONExprModelForGSQL(OrConditionForExpressionModel, currentDataSchema);
    expect((<LoadingVariable>(<ComparisonCondition>OrConditionForExpressionModel.operands[0]).operands[0]).value).toBe(`"encoding"`);
    expect((<LoadingVariable>(<ComparisonCondition>OrConditionForExpressionModel.operands[1]).operands[0]).value).toBe(`"indent":"length"`);
    DataFilterLogic.convertJSONExprModelForVisualExprForm(OrConditionForGSQL, currentDataSchema);
    expect((<LoadingVariable>(<ComparisonCondition>OrConditionForGSQL.operands[0]).operands[0]).value).toBe(0);
    expect((<LoadingVariable>(<ComparisonCondition>OrConditionForGSQL.operands[1]).operands[0]).value).toBe(2);
  });
});
