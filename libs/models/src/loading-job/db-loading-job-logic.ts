import { isEqual } from 'lodash';
import { DBLoadingJobJson } from './db-loading-job.interface';

export class LoadingJobStyleLogic {

  /**
   * Mark the status on the unsaved loading jobs that have updated styles.
   *
   * @param {DBLoadingJobJson[]} savedDBLoadingJobStyles
   * @param {DBLoadingJobJson[]} inMemoryDBLoadingJobStyles
   * @param {LoadingJobData[]} loadingJobs
   */
  static getUnsavedLoadingJobStyleNames(
    savedDBLoadingJobStyles: DBLoadingJobJson[],
    inMemoryDBLoadingJobStyles: DBLoadingJobJson[],
  ): string[] {
    const unsavedJobNames: string[] = [];

    inMemoryDBLoadingJobStyles.forEach(inMemoryDBLoadingJobStyle => {
      const oldJobStyle = savedDBLoadingJobStyles.find(job => {
        return job.loadingJobName === inMemoryDBLoadingJobStyle.loadingJobName;
      });

      if (oldJobStyle !== undefined &&
        !isEqual(
          oldJobStyle.loadingStatementsStyle,
          inMemoryDBLoadingJobStyle.loadingStatementsStyle
        )
      ) {
        unsavedJobNames.push(inMemoryDBLoadingJobStyle.loadingJobName);
      }
    });

    return unsavedJobNames;
  }
}
