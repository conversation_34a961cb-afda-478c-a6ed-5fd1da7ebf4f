import { DBLoadingStatementStyle } from './db-loading-job.interface';
import { LoadToEntityLogic } from './load-to-entity-logic.model';

describe('GSQLLoadToEntityModel', () => {
  let dbLoadingStatementStyle: DBLoadingStatementStyle;

  beforeEach(() => {
    dbLoadingStatementStyle = {
      sourcePosition: {
        x: 0.2, y: 0.5
      },
      targetPosition: {
        x: 0.8, y: 0.5
      },
      middlePositions: [
        {
          x: 0.5, y: 0.5
        },
        {
          x: 0.5, y: 0.6
        }
      ]
    };
  });

  it('should success validation check', () => {
    const mockGsqlLoadToVertexJson2 = {
      Type: 'Vertex',
      UsingClauses: {},
      Mappings: [
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                },
                {
                  Type: 'SrcColName',
                  Value: 'pid'
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person',
      DataSource: {
        Type: 'FileVar',
        Value: 'f'
      }
    };

    const validationCheck = LoadToEntityLogic.validationCheck(mockGsqlLoadToVertexJson2);
    expect(validationCheck.success).toBeTruthy();
  });

  it('should fail validation check cuz loading mapping type contains unsupported fields', () => {
    const mockGsqlLoadToVertexJson2 = {
      Type: 'Vertex',
      UsingClauses: {},
      Mappings: [
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                },
                {
                  Type: 'SrcColIndex',
                  FlattenTokenTupleSeparator: '|',
                  FlattenTokenFieldSeparator: '',
                  Value: 1,
                  FlattenTokenFieldNumber: 2,
                  IsFlatten: true
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person',
      DataSource: {
        Type: 'FileVar',
        Value: 'f'
      }
    };

    const validationCheck = LoadToEntityLogic
      .validationCheck(mockGsqlLoadToVertexJson2);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message)
      .toBe('"FlattenTokenTupleSeparator" is not supported in Graph Studio.');
  });

  it('should fail validation check because using clause is not empty', () => {
    const mockGsqlLoadToVertexJson2 = {
      Type: 'Vertex',
      UsingClauses: {
        SEPARATOR: ',',
        HEADER: 'true'
      },
      Mappings: [
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person',
      DataSource: {
        Type: 'FileVar',
        Value: 'f'
      }
    };

    const validationCheck = LoadToEntityLogic
      .validationCheck(mockGsqlLoadToVertexJson2);
    expect(validationCheck.success).toBeTruthy();
  });

  it('should fail validation check because contains options', () => {
    const mockGsqlLoadToVertexJson2 = {
      Type: 'Vertex',
      UsingClauses: { },
      Options: ' OPTION(vertex_insertion_off) ',
      Mappings: [
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person',
      DataSource: {
        Type: 'FileVar',
        Value: 'f'
      }
    };

    const validationCheck = LoadToEntityLogic
      .validationCheck(mockGsqlLoadToVertexJson2);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message)
      .toBe('"Options" is not supported in Graph Studio.');
  });

});
