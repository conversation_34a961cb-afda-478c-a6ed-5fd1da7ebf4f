import { parseExpr<PERSON><PERSON> } from '../expression';
import { <PERSON>raph, Edge } from '../topology';
import { HelperFunctions } from '../utils';

import { DBPositionStyle, DBLoadingStatementStyle, DBMappingWidgetIndex } from './db-loading-job.interface';
import { GSQLLoadToEdgeJson, GSQLDataSourceJson } from './gsql-loading-job.interface';
import {
  LoadingMappingLogic, OneColumnMapping,
  TokenFunction, SourceType, MapWidget, TupleWidget
} from './loading-mapping.model';
import { LoadToEntityLogic } from './load-to-entity-logic.model';
import { DataFieldSchema, DataFormat } from './data-set.interface';
import { cloneDeep } from 'lodash';

/**
 * Describes one load to edge statement.
 * - edgeName: loading target vertex type
 * - mappingWidgets: the mapping widgets (UDF, Map, Tuple, etc) used in the loading statement
 * - mappings: the loading mapping of from vertex, to vertex and attributes. 1st one is
 *             from vertex mapping. 2nd one is to vertex mapping.
 * - style: records the positions (and other styles in the future) of data source, target and
 *          middle items like token functions, etc for rendering in rhs panel.
 * - usingClauses: the using clauses.
 * - dataSource: the data source.
 *
 * @export
 * @interface LoadToEdgeData
 */
export interface LoadToEdgeData {
  edgeName: string;
  fromVertexType: string;
  toVertexType: string;
  mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[];
  mappings: OneColumnMapping[];
  style: DBLoadingStatementStyle;
  usingClauses: {[propName: string]: string};
  dataSource: GSQLDataSourceJson;
}

/**
 * Provides APIs to handle load to edge related business logic.
 *
 * @export
 * @class LoadToVertexLogic
 */
export class LoadToEdgeLogic {
  /**
   * Clone load to edge data.
   *
   * @static
   * @param {LoadToEdgeData} data
   * @returns {LoadToEdgeData}
   * @memberof LoadToEdgeLogic
   */
  static clone(data: LoadToEdgeData): LoadToEdgeData {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   * Load information from GSQL load to edge json and database json into load to edge data.
   * Should do validation check before invoking this function.
   *
   * @static
   * @param {GSQLLoadToEdgeJson} gsqlLoadToEdgeJson
   * @param {DBLoadingStatementStyle} loadingStatementStyle
   * @param {DataFieldSchema[]} dataSchema
   * @returns {LoadToEdgeData}
   * @memberof LoadToEdgeLogic
   */
  static loadFromGSQLAndDB(
    gsqlLoadToEdgeJson: GSQLLoadToEdgeJson,
    loadingStatementStyle: DBLoadingStatementStyle,
    dataSchema: DataFieldSchema[],
  ): LoadToEdgeData {
    const loadToEdgeData: LoadToEdgeData = {
      edgeName: gsqlLoadToEdgeJson.TargetName,
      style: loadingStatementStyle,
      fromVertexType: gsqlLoadToEdgeJson.FromVertexType,
      toVertexType: gsqlLoadToEdgeJson.ToVertexType,
      mappings: [],
      mappingWidgets: [],
      usingClauses: gsqlLoadToEdgeJson.UsingClauses,
      dataSource: gsqlLoadToEdgeJson.DataSource,
    };

    if (loadingStatementStyle.mappingWidgetIndices) {
      let maxIndex = 0;
      const visited = new Set<number>();
      const tempMiddlePositions = [];
      for (let { index } of loadingStatementStyle.mappingWidgetIndices) {
        if (visited.has(index)) {
          continue;
        }
        visited.add(index);
        maxIndex = Math.max(maxIndex, index);
        tempMiddlePositions.push(loadingStatementStyle.middlePositions[index]);
      }
      for (let i = 0; i <= maxIndex; i++) {
        if (!visited.has(i)) {
          loadingStatementStyle.mappingWidgetIndices.forEach(item => {
            if (item.index > i) {
              item.index--;
            }
          })
        }
      }
      loadingStatementStyle.middlePositions = tempMiddlePositions;
    }

    for (const oneColumnMapping of gsqlLoadToEdgeJson.Mappings) {
      LoadingMappingLogic.loadMappingFromGSQLMappingJson(
        oneColumnMapping,
        loadToEdgeData.mappings,
        loadToEdgeData.mappingWidgets,
        dataSchema,
        cloneDeep(loadingStatementStyle.mappingWidgetIndices || []),
      );
    }

    return loadToEdgeData;
  }

  /**
   * Dump in-memory load-to-edge object into GSQL load-to-edge json and database style json.
   *
   * @static
   * @param {LoadToEdgeData} loadToEdge
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @returns {[GSQLLoadToEdgeJson, DBLoadingStatementStyle]}
   * @memberof LoadToEdgeLogic
   */
  static dumpToGSQLAndDB(
    loadToEdge: LoadToEdgeData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
  ): [GSQLLoadToEdgeJson, DBLoadingStatementStyle] {
    // Init
    const gsqlLoadToEdge: GSQLLoadToEdgeJson = {
      Type: 'Edge',
      TargetName: loadToEdge.edgeName,
      FromVertexType: loadToEdge.fromVertexType,
      ToVertexType: loadToEdge.toVertexType,
      DataSource: loadToEdge.dataSource,
      Mappings: [],
      UsingClauses: loadToEdge.usingClauses,
    };

    // Collect where clause for gsql if it is defined.
    if (loadToEdge.style.whereClauseJson) {
      gsqlLoadToEdge.WhereClause = parseExprJson(loadToEdge.style.whereClauseJson).toString();
    }

    // Collect loading mapping
    const tokenFunctionPositions: DBPositionStyle[] = [];
    const mappingWidgetIndices: DBMappingWidgetIndex[] = [];
    loadToEdge.mappings.forEach(mapping => {
      LoadingMappingLogic.dumpToGSQLLoadingMapping(
        gsqlLoadToEdge.Mappings,
        mapping,
        loadToEdge.mappingWidgets,
        loadToEdge.style,
        tokenFunctionPositions,
        mappingWidgetIndices,
        dataFormat,
        dataSchema,
      );
    });

    const loadingStyle: DBLoadingStatementStyle = {
      sourcePosition: loadToEdge.style.sourcePosition,
      targetPosition: loadToEdge.style.targetPosition,
      middlePositions: tokenFunctionPositions,
      mappingWidgetIndices: mappingWidgetIndices,
    };

    // Collect where clause styles if it is defined.
    if (loadToEdge.style.whereClauseJson) {
      loadingStyle.whereClauseJson = loadToEdge.style.whereClauseJson;
    }

    return [
      gsqlLoadToEdge, loadingStyle
    ];
  }

  /**
   * Semantic check if the load to edge statement has semantic errors.
   *
   * @static
   * @param {LoadToEdgeData} loadToEdge
   * @param {string[]} header
   * @param {Graph} schema
   * @returns {{
   *     success: boolean,
   *     message: string
   *   }}
   * @memberof LoadToEdgeLogic
   */
  static semanticCheck(
    loadToEdge: LoadToEdgeData,
    header: string[],
    schema: Graph
  ): {
    success: boolean;
    message: string;
  } {
    let found = false;
    let mappingNumber = 2;
    let fromToEdgePair: { from: string, to: string }[] = [];
    let attributes = [];
    schema.edgeTypes.forEach(edgeType => {
      if (edgeType.name === loadToEdge.edgeName) {
        found = true;
        mappingNumber += edgeType.attributes.length;
        fromToEdgePair = edgeType.fromToVertexTypePairs;
        attributes = edgeType.attributes;
      }
    });

    // 1. Edge name not exist in schema
    if (!found) {
      return {
        success: false,
        message: `Edge type "${loadToEdge.edgeName}" doesn't exist in graph schema.`
      };
    }

    // 2. Loading mapping must with same number as edge attributes plus from vertex and to vertex
    if (loadToEdge.mappings.length !== mappingNumber) {
      return {
        success: false,
        message: `Data loading mapping number and edge attribute number don't match.`
      };
    }

    // 3. From vertex, to vertex and discriminator attributes cannot be default
    for (let i = 0; i < mappingNumber; i++) {
      if (
        loadToEdge.mappings[i].sourceType !== SourceType.Default ||
        (i >= 2 && !attributes[i - 2].isDiscriminator)
      ) {
        continue;
      }

      switch (i) {
        case 0:
          return {
            success: false,
            message: 'Need to map data column to edge source vertex.'
          };
        case 1:
          return {
            success: false,
            message: 'Need to map data column to edge target vertex.'
          };
        default:
          return {
            success: false,
            message: 'Need to map data column to discriminator attributes.'
          };
      }
    }

    // 4. Loading to edge from vertex type and to vertex type should be consistent with schema.
    if (!fromToEdgePair.find(pair =>
      pair.from === loadToEdge.fromVertexType && pair.to === loadToEdge.toVertexType
    )) {
      return {
        success: false,
        message:
          'Load to edge statement source vertex type and target vertex type '
          + 'is not consistent with graph schema.'
      };
    }

    // Do common semantic checks same with loadToVertex
    return LoadToEntityLogic.semanticCheck(loadToEdge, header);
  }

  /**
   * Create one load-to-edge statement.
   *
   * @static
   * @param {LoadingJobData} loadingJobData
   * @param {Edge} edge
   * @param {string} fromVertexType
   * @param {string} toVertexType
   * @param {{[propName: string]: string}} usingClauses
   * @returns {LoadToEdgeData}
   * @memberof LoadToEdgeLogic
   */
  static createLoadToEdge(
    edge: Edge,
    fromVertexType: string,
    toVertexType: string,
    usingClauses: {[propName: string]: string}
  ): LoadToEdgeData {
    // For brand new load-to-edge statement, set data mapping to source vertex type,
    // target vertex type and attributes all as 'Default'
    const mappings: OneColumnMapping[] = [];
    for (let i = 0; i < edge.attributes.length + 2; i++) {
      mappings.push({
        sourceType: SourceType.Default
      });
    }
    // Construct the load-to-edge statement
    return {
      edgeName: edge.name,
      fromVertexType: fromVertexType,
      toVertexType: toVertexType,
      mappingWidgets: [],
      mappings: mappings,
      style: {
        sourcePosition: { x: 30, y: 300 },
        targetPosition: { x: 650, y: 300 },
        middlePositions: []
      },
      dataSource: {
        Type: 'FileVar',
        Value: HelperFunctions.dataFileName
      },
      usingClauses: usingClauses,
    };
  }
}
