/**
 * Data source types.
 *
 * @export
 * @enum {string}
 */
export enum DataSourceType {
  None = '',
  File = 'file',
  S3 = 's3',
  GoogleCloudStorage = 'gcs',
  Kafka = 'kafka',
  AzureBlobStorage = 'abs',
  BigQuery = 'bq',
  DeltaLake = 'deltaLake',
  Spark = 'spark',
  Iceberg = 'iceberg',
  Snowflake = 'snowflake',
  Postgres = 'postgres'
}

/**
 * Data source.
 *
 * @export
 * @interface DataSource
 */
export interface DataSource {
  name?: string;
  type: DataSourceType;
  isLocal: boolean;
}
