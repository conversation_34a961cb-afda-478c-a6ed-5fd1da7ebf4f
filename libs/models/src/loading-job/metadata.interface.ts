/**
 * Table column metadata
 *
 * @export
 * @interface Metadata
 */
export interface ColumnMetadata {
  name: string;
  type: string;
  nullable: boolean;
}

/**
 * Table metadata
 *
 * @export
 * @interface Metadata
 */
export interface TableMetadata {
  name: string;
  columns: ColumnMetadata[];
}

/**
 * Metadata
 *
 * @export
 * @interface Metadata
 */
export interface Metadata {
  databaseName: string;
  schemaName: string;
  tableNames: string[];
  tables: TableMetadata[];
}
