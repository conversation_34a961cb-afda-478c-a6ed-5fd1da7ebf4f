import { DBPositionStyle } from './db-loading-job.interface';

// ========================= File parsing =========================

/**
 * File format.
 *
 * @export
 * @enum {string}
 */
export enum FileFormat {
  None = 'none',
  ZIP = 'zip',
  GZIP = 'gzip',
  TAR = 'tar',
}

/**
 * File parsing options.
 * - format:
 * - parsing configurations.
 *
 * @export
 * @interface ParsingOptions
 */
export interface ParsingOptions {
  fileFormat: FileFormat;
  [propName: string]: string | number | boolean;
}

/**
 * Tabular data parsing options.
 * - separator: field separator of one row
 * - eol: end of line char
 * - header: 'true' | 'false', whether the 1st row is header
 * - quote?: separator / eol within matched quote char will be ignored
 *
 * @export
 * @interface TabularParsingOptions
 * @extends {ParsingOptions}
 */
export interface TabularParsingOptions extends ParsingOptions {
  separator: string;
  eol: string;
  header: boolean;
  quote?: string;
}

/**
 * JSON data parsing options.
 * - eol: end of line char
 *
 * @export
 * @interface JSONParsingOptions
 * @extends {ParsingOptions}
 */
export interface JSONParsingOptions extends ParsingOptions {
  eol: string;
}

/**
 * data parser base class with default values
 */
class DataParser {
  format = FileFormat.None;
  eol = '\\n';
}

/**
 * CSV parser with default value.
 *
 * @export
 * @class TabularParser
 */
export class TabularParser extends DataParser {
  separator = ',';
  header = true;
  quote = '';
  json = false;
}

/**
 * JSON parser with default value.
 *
 * @export
 * @class JSONParser
 */
export class JSONParser extends DataParser {
  json = true;
}

// ========================= Data format =========================

/**
 * Data format.
 *
 * @export
 * @enum {string}
 */
export enum DataFormat {
  None = 'none',
  CSV = 'csv',
  TSV = 'tsv',
  JSON = 'json',

  // TODO: Support more data formats in the future.
  // AVRO = 'avro',
  // PARQUET = 'parquet',
}

// ========================= Data schema =========================

/**
 * Tabular data type.
 *
 * @export
 * @enum {string}
 */
export enum TabularDataType {
  String = 'string',
}

/**
 * JSON data type.
 *
 * @export
 * @enum {string}
 */
export enum JSONDataType {
  Null = 'null',
  String = 'string',
  Number = 'number',
  Integer = 'integer',
  Boolean = 'boolean',
  Object = 'object',
  Array = 'array',
}

/**
 * The schema of a data field.
 *
 * @export
 * @interface DataFieldSchema
 */
export interface DataFieldSchema {
  name: string;
}

/**
 * The tabular schema of a data field.
 * - type: its data type.
 *
 * @export
 * @interface TabularDataFieldSchema
 * @extends DataFieldSchema
 */
export interface TabularDataFieldSchema extends DataFieldSchema {
  type?: TabularDataType;
}

/**
 * The flattened JSON schema of a data field.
 * - type: its data type.
 * - level: its number of level in the JSON schema of the data set.
 * - path: the path from its ancester to itself in the JSON schema of the data set.
 *
 * @export
 * @interface JSONDataFieldSchema
 * @extends DataFieldSchema
 */
export interface JSONDataFieldSchema extends DataFieldSchema {
  type: JSONDataType;
  level: number;
  path: string[];
}

// ========================= File info =========================

/**
 * File info.
 *
 * @export
 * @interface FileInfo
 */
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  isDir: boolean;
  modTime: number;
}

// ========================= Data set =========================

/**
 * Data set.
 *
 * @export
 * @interface DataSet
 */
export interface DataSet {
  uri: string;
  parsingOptions: ParsingOptions;
  dataFormat: DataFormat;
  dataSchema: DataFieldSchema[];

  // TODO: Deprecate sample data in the future due to user data privacy concern.
  sampleData?: string[][];

  // For UI purpose only.
  position?: DBPositionStyle;
  info?: FileInfo;
}
