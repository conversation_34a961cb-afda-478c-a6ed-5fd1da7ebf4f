/**
 * Structure of an abstract ast in loading job. The actual element structure
 * depends on Type.
 *
 * @export
 * @interface GSQLLoadingAstJson
 */
export interface GSQLLoadingAstJson {
  Type: string;
  [propName: string]: any;
}

/**
 * GSQL interface representing one loading job.
 * - Type: 'Online' | 'Offline'
 * - JobName: loading job name
 * - GraphName: graph schema name
 * - Headers: user defined headers map. Key is header name, value is header column names array
 * - Filters: user defined filters that will be used in line rejection rules
 * - LoadingStatements: loading to vertex | edge | temp table, delete vertex | edge
 *
 * @export
 * @interface GSQLLoadingJobJson
 */
export interface GSQLLoadingJobJson {
  FileNames?: { [fileVarName: string]: string };
  Type: string;
  JobName: string;
  GraphName: string;
  Headers: {[headerName: string]: string[]};
  Filters: string[];
  LoadingStatements: GSQLLoadingAstJson[];
}

/**
 * GSQL interface representing one loading to vertex statement.
 * - TargetName: target vertex type name
 * - DataSource: loading data source
 * - Mappings: loading mapping IPEs
 * - WhereClause?: loading condition filter
 * - UsingClauses: loading using clauses
 * - Options?: loading options like ignore if exist, etc
 *
 * @export
 * @interface GSQLLoadToVertexJson
 */
export interface GSQLLoadToVertexJson extends GSQLLoadingAstJson {
  TargetName: string;
  DataSource: GSQLDataSourceJson;
  Mappings: GSQLLoadingAstJson[];
  WhereClause?: string;
  UsingClauses: {[propName: string]: string};
  Options?: string;
}

/**
 * GSQL interface representing one loading to edge statement.
 * - TargetName: target vertex type name
 * - DataSource: loading data source
 * - Mappings: loading mapping IPEs
 * - WhereClause?: loading condition filter
 * - FromVertexType: loading to edge source vertex type
 * - ToVertexType: loading to edge target vertex type
 * - UsingClauses: loading using clauses
 * - Options?: loading options like ignore if exist, etc
 *
 * @export
 * @interface GSQLLoadToEdgeJson
 */
export interface GSQLLoadToEdgeJson extends GSQLLoadingAstJson {
  TargetName: string;
  DataSource: GSQLDataSourceJson;
  Mappings: GSQLLoadingAstJson[];
  WhereClause?: string;
  FromVertexType: string;
  ToVertexType: string;
  UsingClauses: {[propName: string]: string};
  Options?: string;
}

/**
 * GSQL interface representing one loading to temp table statement.
 * - TargetName: target vertex type name
 * - DataSource: loading data source
 * - Mappings: loading mapping IPEs
 * - WhereClause?: loading condition filter
 * - ColumnNames: temp table defined column names
 * - UsingClauses: loading using clauses
 * - Options?: loading options like ignore if exist, etc
 *
 * @export
 * @interface GSQLLoadToTempTableJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLLoadToTempTableJson extends GSQLLoadingAstJson {
  TargetName: string;
  DataSource: GSQLDataSourceJson;
  Mappings: GSQLLoadingAstJson[];
  WhereClause?: string;
  ColumnNames: string[];
  UsingClauses: {[propName: string]: string};
  Options?: string;
}

/**
 * GSQL interface representing one deleting vertex statement.
 * - VertexType: vertex type to delete
 * - PrimaryId: loading mapping of deleted vertex id
 * - TempTableName: temp table from where to get data source
 * - WhereClause?: loading condition filter
 * - UsingClauses: loading using clauses
 *
 * @export
 * @interface GSQLDeleteVertexJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLDeleteVertexJson extends GSQLLoadingAstJson {
  VertexType: string;
  PrimaryId: GSQLLoadingAstJson;
  TempTableName?: string;
  WhereClause?: string;
  UsingClauses: {[propName: string]: string};
}

/**
 * GSQL interface representing one deleting edge statement.
 * - EdgeType: edge type to delete
 * - FromVertexType: source vertex type of the edge to be deleted
 * - FromVertexPrimaryId: loading mapping of source vertex id of the edge to be deleted
 * - ToVertexType?: target vertex type of the edge to be deleted
 * - ToVertexPrimaryId?: loading mapping of target vertex id of the edge to be deleted
 * - TempTableName: temp table from where to get data source
 * - WhereClause?: loading condition filter
 * - UsingClauses: loading using clauses
 *
 * @export
 * @interface GSQLDeleteEdgeJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLDeleteEdgeJson extends GSQLLoadingAstJson {
  VertexType: string;
  FromVertexType: string;
  FromVertexPrimaryId: GSQLLoadingAstJson;
  ToVertexType?: string;
  ToVertexPrimaryId?: GSQLLoadingAstJson;
  TempTableName?: string;
  WhereClause?: string;
  UsingClauses: {[propName: string]: string};
}

/**
 * GSQL interface representing data source.
 * - Type: 'TempTable' | 'Online' | 'File'
 * - Value?: temp table name, empty, or file path
 *
 * @export
 * @interface GSQLDataSourceJson
 */
export interface GSQLDataSourceJson {
  Type: string;
  Value?: string;
}

// Currently only support basic loading mapping.
export const supportedLoadingMappingTypes: string[] = [
  'SrcColIndex', 'SrcColName', 'Literal', 'Default', 'UDF', 'UDT', 'KvPair'
];

export const SUPPORTED_PROPNAMES: string[] = [
  'Type', 'Key', 'Value', 'Params', 'UdfName', 'UdtName', 'Fields', 'SrcKeyNames'
];

/**
 * Loading mapping refer by column name.
 * - Value: column name
 * - SrcKeyNames: an array of column names to refer to nested attribute in json.
 *
 * @export
 * @interface GSQLSrcColNameJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLSrcColNameJson extends GSQLLoadingAstJson {
  Value: string;
  SrcKeyNames?: string[];
}

/**
 * Loading mapping refer by column index.
 * - Value: column index
 *
 * @export
 * @interface GSQLSrcColIndexJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLSrcColIndexJson extends GSQLLoadingAstJson {
  Value: number;
}

/**
 * Loading mapping of string literal.
 * - Value: string literal value
 *
 * @export
 * @interface GSQLLiteralJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLLiteralJson extends GSQLLoadingAstJson {
  Value: string;
}

/**
 * Loading mapping default.
 *
 * @export
 * @interface GSQLDefaultJson
 */
export interface GSQLDefaultJson {
  Type: string;
}

/**
 * Loading mapping of UDF.
 * - UdfName: UDF function name
 * - Params: parameters of UDF call
 *
 * @export
 * @interface GSQLUDFJson
 */
export interface GSQLUDFJson extends GSQLLoadingAstJson {
  UdfName: string;
  Params: GSQLLoadingAstJson[];
}

/**
 * Loading mapping of Map.
 * - Key: loading mapping to the Map key
 * - Value: loading mapping to the Map val
 *
 * @export
 * @interface GSQLKVPairJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLKVPairJson extends GSQLLoadingAstJson {
  Key: GSQLLoadingAstJson;
  Value: GSQLLoadingAstJson;
}

/**
 * Loading mapping of UDT.
 * - UdtName: UDT tuple name
 * - Fields: loading mapping to the UDT fields
 *
 * @export
 * @interface GSQLUDTMappingJson
 * @extends {GSQLLoadingAstJson}
 */
export interface GSQLUDTMappingJson extends GSQLLoadingAstJson {
  UdtName: string;
  Fields: GSQLLoadingAstJson[];
}
