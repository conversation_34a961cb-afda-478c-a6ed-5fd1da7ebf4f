import { cloneDeep, differenceWith, isEqual, unionWith } from 'lodash';
import { GSQLLoadingJobJson } from './gsql-loading-job.interface';

export interface GSQLLoadingJobChange {
  isChanged: boolean;
  loadingJobNamesToDelete: string[];
  loadingJobsToAdd: GSQLLoadingJobJson[];
}

/**
 * Provides APIs to handle GSQL loading job related business logic.
 *
 * @export
 * @class GSQLLoadingJobLogic
 */
export class GSQLLoadingJobLogic {

  /**
   * If, by user, a loading job logic is
   * - added:     we delete it from GSQL (swallow the error), then add it to GSQL.
   * - updated:   we delete it from GSQL, then add it to GSQL.
   * - deleted:   we delete it from GSQL.
   * - untouched: we do nothing.
   *
   * Namely, when the user touches a loading job logic, we
   * - first delete the touched GSQL loading job.
   * - then add the GSQL loading job if it's added or updated by the user.
   *
   * @static
   * @param {GSQLLoadingJob<PERSON>son} savedJobs
   * @param {GSQLLoadingJobJson} inMemoryJobs
   * @returns {GSQLLoadingJobJson}
   * @memberof GSQLLoadingJobLogic
   */
  static getGSQLLoadingJobChange(
    savedGSQLLoadingJobs: GSQLLoadingJobJson[],
    inMemoryGSQLLoadingJobs: GSQLLoadingJobJson[]
  ): GSQLLoadingJobChange {
    if (isEqual(savedGSQLLoadingJobs, inMemoryGSQLLoadingJobs)) {
      return {
        isChanged: false,
        loadingJobNamesToDelete: [],
        loadingJobsToAdd: []
      };
    }

    // FIXME: GSQL needs to change the WHERE clause export format to be the same as what it received.
    const copyOfsavedGSQLLoadingJobs = GSQLLoadingJobLogic.handleWhereClause(savedGSQLLoadingJobs);
    const copyOfInMemoryGSQLLoadingJobs = GSQLLoadingJobLogic.handleWhereClause(inMemoryGSQLLoadingJobs);

    const loadingJobsAddedInMemory: GSQLLoadingJobJson[] = differenceWith(
      copyOfInMemoryGSQLLoadingJobs, copyOfsavedGSQLLoadingJobs, isEqual
    );
    const loadingJobsDeletedFromMemory: GSQLLoadingJobJson[] = differenceWith(
      copyOfsavedGSQLLoadingJobs, copyOfInMemoryGSQLLoadingJobs, isEqual
    );

    const loadingJobsToAdd = inMemoryGSQLLoadingJobs.filter(inMemoryGSQLJob =>
      loadingJobsAddedInMemory.find(jobAddedInMemory => inMemoryGSQLJob.JobName === jobAddedInMemory.JobName)
    );
    const loadingJobsToDelete: GSQLLoadingJobJson[] = unionWith(
      loadingJobsDeletedFromMemory,
      loadingJobsAddedInMemory,
      (job1: GSQLLoadingJobJson, job2: GSQLLoadingJobJson) => job1.JobName === job2.JobName
    );
    const loadingJobNamesToDelete = loadingJobsToDelete.map(job => job.JobName);

    return {
      isChanged: true,
      loadingJobNamesToDelete: loadingJobNamesToDelete,
      loadingJobsToAdd: loadingJobsToAdd
    };
  }

  /**
   * Handle the where clause in the GSQL loading job load statements.
   *
   * @static
   * @param {GSQLLoadingJobJson[]} gsqlLoadingJobs
   * @returns {GSQLLoadingJobJson[]}
   * @memberof GSQLLoadingJobLogic
   */
  static handleWhereClause(gsqlLoadingJobs: GSQLLoadingJobJson[]): GSQLLoadingJobJson[] {
    gsqlLoadingJobs = cloneDeep(gsqlLoadingJobs);

    gsqlLoadingJobs.forEach(gsqlLoadingJob =>
      gsqlLoadingJob.LoadingStatements.forEach(statement => {
        if ((<any>statement).WhereClause) {
          // Remove parenthesis from the where clause.
          (<any>statement).WhereClause = ((<any>statement).WhereClause)
            .replace(/\(/g, '').replace(/\)/g, '').replace(/ /g, '');
        }
      })
    );

    return gsqlLoadingJobs;
  }

  /**
   * Get the unsaved loading jobs that are new or updated.
   *
   * @param {GSQLLoadingJobJson[]} savedGSQLLoadingJobs
   * @param {GSQLLoadingJobJson[]} inMemoryGSQLLoadingJobs
   * @param {LoadingJobData[]} loadingJobs
   */
  static getUnsavedGSQLLoadingJobNames(
    savedGSQLLoadingJobs: GSQLLoadingJobJson[],
    inMemoryGSQLLoadingJobs: GSQLLoadingJobJson[],
  ): string[] {
    const unsavedJobNames: string[] = [];

    inMemoryGSQLLoadingJobs.forEach((inMemoryGSQLLoadingJob) => {
      const oldJob = savedGSQLLoadingJobs.find(job => {
        return job.JobName === inMemoryGSQLLoadingJob.JobName;
      });

      if (oldJob === undefined || !isEqual(oldJob, inMemoryGSQLLoadingJob)) {
        // Add new or updated loading jobs.
        unsavedJobNames.push(inMemoryGSQLLoadingJob.JobName);
      }
    });

    return unsavedJobNames;
  }

}
