import { Condition<PERSON><PERSON> } from '../expression';
import { DataFieldSchema, DataFormat, ParsingOptions } from './data-set.interface';

/**
 * Style representation in backend server.
 * Recording the coordinate of the one elemnt shown in the panel.
 *
 * @export
 * @interface DBPositionStyle
 */
export interface DBPositionStyle {
  x: number;
  y: number;
}

/**
 * Mapping widget information in backend server.
 * Recording the index and name of the a mapping widget shown in the panel.
 *
 * @export
 * @interface DBMappingWidgetIndex
 */
export interface DBMappingWidgetIndex {
  index: number;
  name?: string;
}

/**
 * Data source representation in backend server.
 * - Data source
 *   - type: the data source type.
 *   - dataSourceName?: the data source name in GSQL.
 * - Data set
 *   - uri: the data source uri (file absolute path for file).
 *   - options: the file format and the parsing options.
 *   - position: the data set icon position on the canvas.
 *
 * @export
 * @interface DBDataSourceJson
 */
export interface DBDataSourceJson {
  // Data source.
  type: string;
  dataSourceName?: string;

  // Data set.
  uri: string;
  options: ParsingOptions;
  position: DBPositionStyle;

  // Not used.
  isChanged?: boolean;
  auth?: any;
}

/**
 * Data set representation in backend server.
 *
 * @export
 * @interface DBDataSetJson
 */
export interface DBDataSetJson {
  dataFormat: DataFormat;
  dataSchema: DataFieldSchema[];
}

/**
 * Records the position of each single loading statement.
 * - sourcePosition: loading statement source position to render in rhs panel
 * - targetPosition: loading statement target position to render in rhs panel
 * - middlePositions: the positions of elements in the middle (token function, etc)
 *                    following DFS order of the loading mapping array
 * - whereClauseJson: the json representation of WHERE clause. Optional.
 *
 * @export
 * @interface DBLoadingStatementStyle
 */
export interface DBLoadingStatementStyle {
  sourcePosition: DBPositionStyle;
  targetPosition: DBPositionStyle;
  middlePositions: DBPositionStyle[];
  whereClauseJson?: ConditionJson;
  mappingWidgetIndices?: DBMappingWidgetIndex[];
}

/**
 * Records the additional information of a loading job that is not saved in tigergraph system.
 * - graphName: to which graph the loading job belongs
 * - loadingJobName: used to identify the loading job
 * - loadingStatementsStyle: style of each loading statement used to render in rhs panel
 * - dataSourceJson: representation of data source for the loading job. Currently we always
 *                   bind one data source for each loading job. In the future might de-couple
 * - dataSetJson?: representation of data source for the loading job. Currently we always
 *                   bind one data source for each loading job. In the future might de-couple
 *
 * - header: the header of data source represented as a string array
 * - sampleData: the first several rows of data
 *
 * @export
 * @interface DBLoadingJobJson
 */
export interface DBLoadingJobJson {
  loadingJobName: string;
  loadingStatementsStyle: DBLoadingStatementStyle[];
  dataSourceJson: DBDataSourceJson;
  dataSetJson?: DBDataSetJson;

  // For tabular data.
  // TODO: Deprecate them and use data schema in the future.
  header?: string[];
  sampleData?: string[][];
}
