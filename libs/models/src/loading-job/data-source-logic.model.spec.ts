import { DataSourceLogic } from './data-source-logic.model';
import { DataSourceType } from './data-source.interface';

describe('DataSourceLogicModel', () => {

  describe('should create data source successfully', () => {
    it('with local file', () => {
      const dataSource = DataSourceLogic.createDataSource(
        undefined,
        DataSourceType.File,
      );
      expect(dataSource.name).toBeUndefined();
      expect(dataSource.type).toEqual(DataSourceType.File);
      expect(dataSource.isLocal).toEqual(true);
    });

    it('with s3', () => {
      const dataSource = DataSourceLogic.createDataSource(
        'myS3Connection',
        DataSourceType.S3,
      );
      expect(dataSource.name).toEqual('myS3Connection');
      expect(dataSource.type).toEqual(DataSourceType.S3);
      expect(dataSource.isLocal).toEqual(true);
    });

    it('with gcs', () => {
      const dataSource = DataSourceLogic.createDataSource(
        'myGCSConnection',
        DataSourceType.GoogleCloudStorage,
      );
      expect(dataSource.name).toEqual('myGCSConnection');
      expect(dataSource.type).toEqual(DataSourceType.GoogleCloudStorage);
      expect(dataSource.isLocal).toEqual(true);
    });

    it('with abs', () => {
      const dataSource = DataSourceLogic.createDataSource(
        'myABSConnection',
        DataSourceType.AzureBlobStorage,
      );
      expect(dataSource.name).toEqual('myABSConnection');
      expect(dataSource.type).toEqual(DataSourceType.AzureBlobStorage);
      expect(dataSource.isLocal).toEqual(true);
    });
  });

  it('should get data source type correctly', () => {
    expect(DataSourceLogic.getDataSourceType('')).toEqual(DataSourceType.None);
    expect(DataSourceLogic.getDataSourceType('file')).toEqual(DataSourceType.File);
    expect(DataSourceLogic.getDataSourceType('s3')).toEqual(DataSourceType.S3);
    expect(DataSourceLogic.getDataSourceType('gcs')).toEqual(DataSourceType.GoogleCloudStorage);
    expect(DataSourceLogic.getDataSourceType('abs')).toEqual(DataSourceType.AzureBlobStorage);
    expect(DataSourceLogic.getDataSourceType('kafka')).toEqual(DataSourceType.Kafka);
  });

  it('ABS connection string parser should parse the input string correctly', () => {
    const connectionStringSamples = [
      'DefaultEndpointsProtocol=https;AccountName=test1;AccountKey=theTesting1key=/k====;EndpointSuffix=core.windows.net',
      'AccountName=test2;AccountKey=theTesting2key=/k====;EndpointSuffix=core.windows.net',
      'AccountName=test3;AccountKey=theTesting3key=/k====',
      'DefaultEndpointsProtocol=https;AccountName=test4;AccountKey=theTest=ing4key=/k===dfs;EndpointSuffix=core.windows.net'
    ];

    let parsingRes = DataSourceLogic.getABSCredentials(connectionStringSamples[0]);
    expect(parsingRes.accountName).toBe('test1');
    expect(parsingRes.accountKey).toBe('theTesting1key=/k====');

    parsingRes = DataSourceLogic.getABSCredentials(connectionStringSamples[1]);
    expect(parsingRes.accountName).toBe('test2');
    expect(parsingRes.accountKey).toBe('theTesting2key=/k====');

    parsingRes = DataSourceLogic.getABSCredentials(connectionStringSamples[2]);
    expect(parsingRes.accountName).toBe('test3');
    expect(parsingRes.accountKey).toBe('theTesting3key=/k====');

    parsingRes = DataSourceLogic.getABSCredentials(connectionStringSamples[3]);
    expect(parsingRes.accountName).toBe('test4');
    expect(parsingRes.accountKey).toBe('theTest=ing4key=/k===dfs');
  });

  it('should get URI name correctly', () => {
    expect(DataSourceLogic.getURIName('')).toEqual('');
    expect(DataSourceLogic.getURIName(DataSourceType.File)).toEqual('');
    expect(DataSourceLogic.getURIName(DataSourceType.S3)).toEqual('S3 URI');
    expect(DataSourceLogic.getURIName(DataSourceType.GoogleCloudStorage)).toEqual('gsutil URI');
    expect(DataSourceLogic.getURIName(DataSourceType.AzureBlobStorage)).toEqual('Blob URL');
  });

  it('should get URI prefix correctly', () => {
    expect(DataSourceLogic.getURIPrefixPlaceholder('')).toEqual('');
    expect(DataSourceLogic.getURIPrefixPlaceholder(DataSourceType.File)).toEqual('');
    expect(DataSourceLogic.getURIPrefixPlaceholder(DataSourceType.S3)).toEqual('s3://');
    expect(DataSourceLogic.getURIPrefixPlaceholder(DataSourceType.GoogleCloudStorage)).toEqual('gs://');
    expect(DataSourceLogic.getURIPrefixPlaceholder(DataSourceType.AzureBlobStorage)).toEqual('https://');
  });

  it('should get URI guide link', () => {
    expect(DataSourceLogic.getGuideLink(DataSourceType.S3))
      .toEqual('https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-bucket-intro.html#accessing-a-bucket-using-S3-format');
    expect(DataSourceLogic.getGuideLink(DataSourceType.GoogleCloudStorage))
      .toEqual('https://cloud.google.com/storage/docs/gsutil#syntax');
    expect(DataSourceLogic.getGuideLink(DataSourceType.AzureBlobStorage))
      .toEqual('https://learn.microsoft.com/en-us/rest/api/storageservices/copy-blob-from-url#request');
  });

  it('should check if data source is remote data source type', () => {
    expect(DataSourceLogic.isRemoteDataSourceType(DataSourceType.File)).toBeFalsy();
    expect(DataSourceLogic.isRemoteDataSourceType(DataSourceType.S3)).toBeTruthy();
    expect(DataSourceLogic.isRemoteDataSourceType(DataSourceType.GoogleCloudStorage)).toBeTruthy();
    expect(DataSourceLogic.isRemoteDataSourceType(DataSourceType.AzureBlobStorage)).toBeTruthy();
  });
});
