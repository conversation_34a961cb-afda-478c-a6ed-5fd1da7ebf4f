import { parseExpr<PERSON><PERSON> } from '../expression';
import { Graph, Vertex } from '../topology';
import { HelperFunctions } from '../utils';

import { DBPositionStyle, DBLoadingStatementStyle, DBMappingWidgetIndex } from './db-loading-job.interface';
import { GSQLLoadToVertexJson, GSQLDataSourceJson } from './gsql-loading-job.interface';
import {
  LoadingMappingLogic, OneColumnMapping,
  SourceType, TokenFunction, MapWidget, TupleWidget
} from './loading-mapping.model';
import { LoadToEntityLogic } from './load-to-entity-logic.model';
import { DataFieldSchema, DataFormat } from './data-set.interface';
import { cloneDeep } from 'lodash';

/**
 * Describes one load to vertex statement.
 * - vertexName: loading target vertex type
 * - usingClauses: the using clauses
 * - hasOptions: the loading job has options or not
 * - options: if the loading job has options, record the options as a string here
 * - mappingWidgets: the mapping widgets (UDF, Map, Tuple, etc) used in the loading statement
 * - mappings: the loading mapping of primary id and attributes. 1st one is primary id mapping
 * - style: records the positions (and other styles in the future) of data source, target and
 *          middle items like token functions, etc for rendering in rhs panel.
 * - usingClauses: the using clauses.
 * - dataSource: the data source.
 *
 * @export
 * @interface LoadToVertexData
 */
export interface LoadToVertexData {
  vertexName: string;
  // NOTE: these props might be supported in the future.
  // hasOptions: boolean;
  // options: string;
  mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[];
  mappings: OneColumnMapping[];
  style: DBLoadingStatementStyle;
  usingClauses: {[propName: string]: string};
  dataSource: GSQLDataSourceJson;
}

/**
 * Provides APIs to handle load to vertex related business logic.
 *
 * @export
 * @class LoadToVertexLogic
 */
export class LoadToVertexLogic {
  /**
   * Clone load to vertex data.
   *
   * @static
   * @param {LoadToVertexData} data
   * @returns {LoadToVertexData}
   * @memberof LoadToVertexLogic
   */
  static clone(data: LoadToVertexData): LoadToVertexData {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   * Load information from GSQL load to vertex json and database json into load to vertex data.
   * Should do validation check before invoking this function.
   *
   * @static
   * @param {GSQLLoadToVertexJson} gsqlLoadToVertexJson
   * @param {DBLoadingStatementStyle} loadingStatementStyle
   * @param {DataFieldSchema[]} dataSchema
   * @returns {LoadToVertexData}
   * @memberof LoadToVertexLogic
   */
  static loadFromGSQLAndDB(
    gsqlLoadToVertexJson: GSQLLoadToVertexJson,
    loadingStatementStyle: DBLoadingStatementStyle,
    dataSchema: DataFieldSchema[]
  ): LoadToVertexData {
    const loadToVertexData: LoadToVertexData = {
      vertexName: gsqlLoadToVertexJson.TargetName,
      style: loadingStatementStyle,
      mappings: [],
      mappingWidgets: [],
      usingClauses: gsqlLoadToVertexJson.UsingClauses,
      dataSource: gsqlLoadToVertexJson.DataSource,
    };

    if (loadingStatementStyle.mappingWidgetIndices) {
      let maxIndex = 0;
      const visited = new Set<number>();
      const tempMiddlePositions = [];
      for (let { index } of loadingStatementStyle.mappingWidgetIndices) {
        if (visited.has(index)) {
          continue;
        }
        visited.add(index);
        maxIndex = Math.max(maxIndex, index);
        tempMiddlePositions.push(loadingStatementStyle.middlePositions[index]);
      }
      for (let i = 0; i <= maxIndex; i++) {
        if (!visited.has(i)) {
          loadingStatementStyle.mappingWidgetIndices.forEach(item => {
            if (item.index > i) {
              item.index--;
            }
          })
        }
      }
      loadingStatementStyle.middlePositions = tempMiddlePositions;
    }

    for (const oneColumnMapping of gsqlLoadToVertexJson.Mappings) {
      LoadingMappingLogic.loadMappingFromGSQLMappingJson(
        oneColumnMapping,
        loadToVertexData.mappings,
        loadToVertexData.mappingWidgets,
        dataSchema,
        cloneDeep(loadingStatementStyle.mappingWidgetIndices || []),
      );
    }

    return loadToVertexData;
  }

  /**
   * Dump in-memory load-to-vertex object into GSQL load-to-vertex json and database style json.
   *
   * @static
   * @param {LoadToVertexData} loadToVertex
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @returns {[GSQLLoadToVertexJson, DBLoadingStatementStyle]}
   * @memberof LoadToVertexLogic
   */
  static dumpToGSQLAndDB(
    loadToVertex: LoadToVertexData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
  ): [GSQLLoadToVertexJson, DBLoadingStatementStyle] {
    // Init
    const gsqlLoadToVertex: GSQLLoadToVertexJson = {
      Type: 'Vertex',
      TargetName: loadToVertex.vertexName,
      DataSource: loadToVertex.dataSource,
      Mappings: [],
      UsingClauses: loadToVertex.usingClauses,
    };

    // Collect where clause for gsql if it is defined.
    if (loadToVertex.style.whereClauseJson) {
      gsqlLoadToVertex.WhereClause = parseExprJson(loadToVertex.style.whereClauseJson).toString();
    }

    // Collect loading mapping
    const tokenFunctionPositions: DBPositionStyle[] = [];
    const mappingWidgetIndices: DBMappingWidgetIndex[] = [];
    loadToVertex.mappings.forEach(mapping => {
      LoadingMappingLogic.dumpToGSQLLoadingMapping(
        gsqlLoadToVertex.Mappings,
        mapping,
        loadToVertex.mappingWidgets,
        loadToVertex.style,
        tokenFunctionPositions,
        mappingWidgetIndices,
        dataFormat,
        dataSchema,
      );
    });

    const loadingStyle: DBLoadingStatementStyle = {
      sourcePosition: loadToVertex.style.sourcePosition,
      targetPosition: loadToVertex.style.targetPosition,
      middlePositions: tokenFunctionPositions,
      mappingWidgetIndices: mappingWidgetIndices,
    };

    // Collect where clause styles if it is defined.
    if (loadToVertex.style.whereClauseJson) {
      loadingStyle.whereClauseJson = loadToVertex.style.whereClauseJson;
    }

    return [
      gsqlLoadToVertex,
      loadingStyle
    ];
  }

  /**
   * Semantic check if the load to vertex statement has semantic errors.
   *
   * @static
   * @param {LoadToVertexData} loadToVertex
   * @param {string[]} header
   * @param {Graph} schema
   * @returns {{
   *     success: boolean,
   *     message: string
   *   }}
   * @memberof LoadToVertexLogic
   */
  static semanticCheck(
    loadToVertex: LoadToVertexData,
    header: string[],
    schema: Graph
  ): {
    success: boolean;
    message: string;
  } {
    let found = false;
    let mappingNumber = 1;
    schema.vertexTypes.forEach(vertexType => {
      if (vertexType.name === loadToVertex.vertexName) {
        found = true;
        mappingNumber += vertexType.attributes.length;
      }
    });

    // 1. Vertex name not exist in schema
    if (!found) {
      return {
        success: false,
        message: `Vertex type "${loadToVertex.vertexName}" doesn't exist in graph schema.`
      };
    }

    // 2. Loading mapping must with same number as vertex attributes plus primary id
    if (loadToVertex.mappings.length !== mappingNumber) {
      return {
        success: false,
        message: `Data loading mapping number and vertex attribute number don't match.`
      };
    }

    // 3. Primary id cannot be default
    if (loadToVertex.mappings[0].sourceType === SourceType.Default) {
      return {
        success: false,
        message: 'Need to map data column to vertex primary id.'
      };
    }

    // Do common semantic checks same with loadToEdge
    return LoadToEntityLogic.semanticCheck(loadToVertex, header);
  }

  /**
   * Create one load-to-vertex statement.
   *
   * @static
   * @param {Vertex} vertex
   * @param {{[propName: string]: string}} usingClauses
   * @returns {LoadToVertexData}
   * @memberof LoadToVertexLogic
   */
  static createLoadToVertex(
    vertex: Vertex,
    usingClauses: {[propName: string]: string}
  ): LoadToVertexData {
    // For brand new load-to-vertex statement, set data mapping to primary id and
    // attributes all as 'Default'
    const mappings: OneColumnMapping[] = [];
    for (let i = 0; i < vertex.attributes.length + 1; i++) {
      mappings.push({
        sourceType: SourceType.Default
      });
    }
    // Construct the load-to-vertex statement
    return {
      vertexName: vertex.name,
      mappingWidgets: [],
      mappings: mappings,
      style: {
        sourcePosition: { x: 30, y: 300 },
        targetPosition: { x: 650, y: 300 },
        middlePositions: []
      },
      dataSource: {
        Type: 'FileVar',
        Value: HelperFunctions.dataFileName
      },
      usingClauses: usingClauses,
    };
  }
}
