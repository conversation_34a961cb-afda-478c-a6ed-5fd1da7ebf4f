import { isEqual } from 'lodash';
import { DBGraphStyleJson } from '../topology';
import { LoadingJobStyleLogic } from './db-loading-job-logic';
import { DBLoadingJobJson } from './db-loading-job.interface';
import { GSQLLoadingJobLogic } from './gsql-loading-job-logic.model';
import { GSQLLoadingJobJson } from './gsql-loading-job.interface';

/**
 * Check data mapping change, whether there is no change, style change only
 * or data mapping is changed.
 *
 * @enum {number}
 */
export const enum LoadingJobChangeStatus {
  NoChange = 0,
  StyleChange = 1,
  DataMappingChange = 2,
}

export interface LoadingJobChange {
  status: LoadingJobChangeStatus;
  jobNames: string[];
}

export class LoadingJobChangeLogic {

  /**
   * Get the loading job change.
   *
   * @static
   * @param {GSQLLoadingJobJson[]} savedLoadingJobs
   * @param {GSQLLoadingJobJson[]} inMemoryGSQLLoadingJobs
   * @param {DBLoadingJobJson[]} savedLoadingJobsStyle
   * @param {DBLoadingJobJson[]} inMemoryLoadingJobsStyle
   * @param {DBGraphStyleJson} savedGraphStyle
   * @param {DBGraphStyleJson} inMemoryGraphStyle
   * @returns {LoadingJobChange}
   * @memberof LoadingJobChangeLogic
   */
  static getLoadingJobChange(
    savedLoadingJobs: GSQLLoadingJobJson[],
    inMemoryGSQLLoadingJobs: GSQLLoadingJobJson[],
    savedLoadingJobsStyle: DBLoadingJobJson[],
    inMemoryLoadingJobsStyle: DBLoadingJobJson[],
    savedGraphStyle: DBGraphStyleJson,
    inMemoryGraphStyle: DBGraphStyleJson
  ): LoadingJobChange {
    // FIXME: GSQL need change export format of WHERE clause same as it receives.
    const savedGSQLLoadingJobs = GSQLLoadingJobLogic.handleWhereClause(savedLoadingJobs);
    inMemoryGSQLLoadingJobs = GSQLLoadingJobLogic.handleWhereClause(inMemoryGSQLLoadingJobs);

    let status = LoadingJobChangeStatus.NoChange;
    const jobNames = [];

    if (!isEqual(savedGraphStyle, inMemoryGraphStyle)) {
      status = LoadingJobChangeStatus.StyleChange;
    }

    if (!isEqual(savedLoadingJobsStyle, inMemoryLoadingJobsStyle)) {
      jobNames.push(...LoadingJobStyleLogic.getUnsavedLoadingJobStyleNames(
        savedLoadingJobsStyle, inMemoryLoadingJobsStyle
      ));
      status = LoadingJobChangeStatus.StyleChange;
    }

    if (!isEqual(savedGSQLLoadingJobs, inMemoryGSQLLoadingJobs)) {
      jobNames.push(...GSQLLoadingJobLogic.getUnsavedGSQLLoadingJobNames(
        savedGSQLLoadingJobs, inMemoryGSQLLoadingJobs
      ));
      status = LoadingJobChangeStatus.DataMappingChange;
    }

    return {
      status: status,
      jobNames: jobNames
    };
  }
}
