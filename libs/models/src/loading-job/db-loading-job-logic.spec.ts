import { cloneDeep } from 'lodash';
import { DBLoadingJobJson } from '.';
import { FileFormat } from './data-set.interface';
import { LoadingJobStyleLogic } from './db-loading-job-logic';

const mockDBLoadingJobA: DBLoadingJobJson = {
  loadingJobName: 'load_job',
  loadingStatementsStyle: [
    {
      sourcePosition: {
        x: 0.2, y: 0.5
      },
      targetPosition: {
        x: 0.8, y: 0.5
      },
      middlePositions: []
    }
  ],
  dataSourceJson: {
    type: 'file',
    uri: '/resources/data_set/gsql/persons.csv',
    options: {
      fileFormat: FileFormat.None,
      eol: '\n',
      separator: ',',
      header: 'true'
    },
    position: {
      x: 0.5,
      y: 0.2
    }
  },
  header: ['name', 'age', 'col1', 'col2'],
  sampleData: [['src', '25', '', ''], ['src2', '26', '', '']]
};

describe('LoadingJobStyleLogic', () => {
  it('should get unsaved loading job style names correctly', () => {
    const savedStyles = [mockDBLoadingJobA];

    const mockDBLoadingJobA1 = cloneDeep(mockDBLoadingJobA);
    mockDBLoadingJobA1.loadingStatementsStyle[0].sourcePosition = {
      x: 0.5, y: 0.2
    };
    const inMemoryStyles = [mockDBLoadingJobA1];

    const jobNames = LoadingJobStyleLogic.getUnsavedLoadingJobStyleNames(
      savedStyles, inMemoryStyles
    );

    expect(jobNames.length).toEqual(1);
    expect(jobNames).toContain('load_job');
  });
});
