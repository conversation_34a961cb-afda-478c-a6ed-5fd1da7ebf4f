import {
  TabularDataFieldSchema,
  TabularParsingOptions, JSONParsingOptions,
  JSONDataFieldSchema,
  JSONDataType,
  TabularParser,
  JSONParser,
} from './data-set.interface';
import { DataSourceType } from './data-source.interface';
import { DataSourceLogic } from './data-source-logic.model';

import { json } from 'generate-schema';
import { cloneDeep } from 'lodash';

export const INVALID_JSON_SAMPLE_DATA_IDENTIFIER = '__GSQL_parsing_failed__';

export class DataSetLogic {


  /**
   * Create local file sample data tabular CSV parsing options.
   *
   * @static
   * @param {TabularParser} dataParser with CSV parsing options
   * @returns {TabularParsingOptions}
   * @memberof DataSetLogic
   */
  static createLocalFileSampleDataTabularParsingOptions(
    dataParser: TabularParser
  ): TabularParsingOptions {
    return {
      fileFormat: dataParser.format,
      separator: DataSourceLogic.separators.find(sep => sep[1] === dataParser.separator)[0],
      eol: DataSourceLogic.eols.find(eol => eol[1] === dataParser.eol)[0],
      header: dataParser.header,
      quote: dataParser.quote === ''
        ? undefined : DataSourceLogic.quotes.find(quote => quote[1] === dataParser.quote)[0],
    };
  }

  /**
   * Create CSV parsing options.
   *
   * @static
   * @param {TabularParser} dataParser with CSV parsing options
   * @returns {TabularParsingOptions}
   * @memberof DataSetLogic
   */
  static createTabularParsingOptions(
    dataParser: TabularParser
  ): TabularParsingOptions {
    return {
      fileFormat: dataParser.format,
      separator: dataParser.separator,
      eol: dataParser.eol,
      header: dataParser.header,
      quote: dataParser.quote,
    };
  }

  /**
   * Create JSON parsing options.
   *
   * @static
   * @param {JSONParser} dataParser with JSON parsing options
   * @returns {JSONParsingOptions}
   * @memberof DataSetLogic
   */
  static createJSONParsingOptions(
    dataParser: JSONParser
  ): JSONParsingOptions {
    return {
      fileFormat: dataParser.format,
      eol: dataParser.eol,
    };
  }

  /**
   * Create a tabular data schema.
   *
   * @static
   * @param {string[]} names
   * @returns {TabularDataFieldSchema[]}
   * @memberof DataSetLogic
   */
  static createTabularDataSchema(
    names: string[],
  ): TabularDataFieldSchema[] {
    return names.map(name => ({ name: name }));
  }

  /**
   * Create a data set label.
   *
   * @static
   * @param {boolean} isChanged
   * @param {DataSourceType} dataSourceType
   * @param {string} uri
   * @returns {string}
   * @memberof DataSetLogic
   */
  static createDataSetLabel(
    isChanged: boolean,
    uri: string,
  ): string {
    const changed = isChanged ? '* ' : '';
    return `${changed}${uri}`;
  }

  /**
   * Convert the JSON sample data to the standard JSON schema.
   *
   * @static
   * @param {any[]} jsonSampleData
   * @returns {any}
   * @memberof DataSetLogic
   */
  static convertSampleDataToStandardSchema(jsonSampleData: any[]): any {
    let standardJSONSchema = {
      $schema: '',
      type: JSONDataType.Object,
      properties: {}
    };

    jsonSampleData.forEach(data => {
      if (Object.keys(standardJSONSchema['properties']).length === 0 &&
        !data[INVALID_JSON_SAMPLE_DATA_IDENTIFIER]
      ) {
        // Generate the json schema based on the valid json sample data.
        standardJSONSchema = json(data);
      }
    });

    return standardJSONSchema;
  }

  /**
   * Convert the standard JSON schema to the in-memory JSON schema.
   *
   * @static
   * @param {any} standardJSONSchema
   * @returns {JSONDataFieldSchema[]}
   * @memberof DataSetLogic
   */
  static convertStandardSchemaToInMemorySchema(standardJSONSchema: any): JSONDataFieldSchema[] {
    const path: string[] = [];
    const jsonSchema: JSONDataFieldSchema[] = [];

    Object.keys(standardJSONSchema.properties).forEach(key => {
      DataSetLogic.convertSchemaHelper(
        key,
        path,
        jsonSchema,
        standardJSONSchema.properties[key],
      );
    });

    return jsonSchema;
  }

  /**
   * Get the file Uri pattern for different remote data source.
   *
   * @static
   * @param {string} dataSourceType
   * @param {string} fileUri
   * @returns {RegExpMatchArray}
   * @memberof DataSetLogic
   */
  static isValidRemoteUri(dataSourceType: string, remoteUri: string): boolean {
    let regex = '';
    switch (dataSourceType) {
      case DataSourceType.S3:
        regex = `^(s|S)3://(([^/]+)/?)+$`;
        break;
      case DataSourceType.GoogleCloudStorage:
        regex = `^gs://(([^/]+)/?)+$`;
        break;
      case DataSourceType.AzureBlobStorage:
        regex = `^(https|http)://(([^/]+)/?)+$`;
        break;
      case DataSourceType.Snowflake:
        regex = `^jdbc:snowflake:\/\/.+$`;
        break;
    }
    // Ignore case;
    return new RegExp(regex, 'i').test(remoteUri);
  }


  /**
   * Reconstruct the uri given by the user to get the
   * uri we need to create a connector.
   *
   * @param {string} uri
   * @param {string} dataSourceType
   * @returns {string}
   * @memberof DataSetLogic
   */
  static getFileUriForConnector(uri: string, dataSourceType: string): string {
    switch (dataSourceType) {
      case DataSourceType.S3:
        return uri.replace('s3://', 's3a://');
      case DataSourceType.AzureBlobStorage:
        return DataSetLogic.convertABSInputPathToFileUri(uri);
      default:
        // TODO: Add more data source types.
        return uri;
    }
  }

  /**
   * Parse the Azure Blob Storage input file URL to proper uri needed by kafka connector.
   * INPUT: https://abstest1.blob.core.windows.net/container1/invalidTest.json
   * OUTPUT: abfss://<EMAIL>/invalidTest.json
   *
   * @static
   * @param {string} filePath
   * @returns {string}
   * @memberof DataSetLogic
   */
  static convertABSInputPathToFileUri(filePath: string): string {
    let fileUri = 'abfss://';
    // Split the protocol and link;
    const pathWithoutProtocal = filePath.split('://')[1];
    const linkSegments = pathWithoutProtocal.split('/');
    // Split from '/' to get the container name from second segment.
    fileUri += linkSegments[1] + '@';
    // Split the first segment with '.' to get account name.
    fileUri += linkSegments[0].split('.')[0] + '.dfs.core.windows.net/';
    // The rest will be the file path. In case there's multiple layer, we will restore the '/'.
    fileUri += linkSegments.slice(2).join('/');

    return fileUri;
  }

  /**
   * A dfs helper to convert the standard JSON schema to the in-memory JSON schema.
   *
   * @private
   * @static
   * @param {string} key
   * @param {string[]} path
   * @param {JSONDataFieldSchema[]} jsonSchema
   * @param {any} standardJSONSchema
   * @memberof DataSetLogic
   */
  private static convertSchemaHelper(
    name: string,
    path: string[],
    jsonSchema: JSONDataFieldSchema[],
    standardJSONSchema: any,
  ) {
    path.push(name);
    jsonSchema.push(
      {
        name: name,
        type: DataSetLogic.getJSONDataType(standardJSONSchema.type),
        level: path.length - 1,
        path: path.slice(),
      }
    );

    if (DataSetLogic.getJSONDataType(standardJSONSchema.type) === JSONDataType.Object) {
      Object.keys(standardJSONSchema.properties).forEach(propertyName => {
        this.convertSchemaHelper(
          propertyName,
          path,
          jsonSchema,
          standardJSONSchema.properties[propertyName],
        );
      });
    }
    path.pop();
  }

  /**
   * Get JSON data type.
   *
   * @static
   * @param {string} type
   * @return {JSONDataType}
   * @memberof DataSourceLogic
   * @memberof DataSetLogic
   */
  private static getJSONDataType(type: string): JSONDataType {
    switch (type) {
      case JSONDataType.Boolean:
      case JSONDataType.Number:
      case JSONDataType.Integer:
      case JSONDataType.String:
      case JSONDataType.Object:
      case JSONDataType.Array:
        return type;
      default:
        return JSONDataType.Null;
    }
  }
}
