import { DataSetLogic } from './data-set-logic.model';
import { FileFormat, JSONDataFieldSchema, JSONDataType } from './data-set.interface';
import { DataSourceType } from './data-source.interface';

const mockJSONSampleData = [
  {
    '__GSQL_parsing_failed__': '{a: }',
  },
  {
    'encoding': 'UTF-7',
    'indent': {
        'length': 30,
        'use_space': true,
        'wrapper': {
          'size': 1.0
        }
    },
    'plug-ins': [
        'c'
    ]
  },
];

const mockStandardJSONSchema: any = {
  '$schema': 'http://json-schema.org/draft-04/schema#',
  'type': 'object',
  'properties': {
    'encoding': {
      'type': 'string'
    },
    'indent': {
      'type': 'object',
      'properties': {
        'length': {
          'type': 'number'
        },
        'use_space': {
          'type': 'boolean'
        },
        'wrapper': {
          'type': 'object',
          'properties': {
            'size': {
              'type': 'number'
            }
          }
        }
      }
    },
    'plug-ins': {
      'type': 'array',
      'items': {
        'type': 'string'
      }
    }
  }
};

const mockJSONSchema: JSONDataFieldSchema[] = [
  {
    name: 'encoding',
    type: JSONDataType.String,
    level: 0,
    path: [
      'encoding'
    ]
  },
  {
    name: 'indent',
    type: JSONDataType.Object,
    level: 0,
    path: [
      'indent'
    ]
  },
  {
    name: 'length',
    type: JSONDataType.Number,
    level: 1,
    path: [
      'indent',
      'length'
    ]
  },
  {
    name: 'use_space',
    type: JSONDataType.Boolean,
    level: 1,
    path: [
      'indent',
      'use_space'
    ]
  },
  {
    name: 'wrapper',
    type: JSONDataType.Object,
    level: 1,
    path: [
      'indent',
      'wrapper'
    ]
  },
  {
    name: 'size',
    type: JSONDataType.Number,
    level: 2,
    path: [
      'indent',
      'wrapper',
      'size'
    ]
  },
  {
    name: 'plug-ins',
    type: JSONDataType.Array,
    level: 0,
    path: [
      'plug-ins'
    ]
  }
];

describe('DataSetLogic', () => {

  describe('should create data parsing options correctly', () => {

    describe('with CSV', () => {
      describe('local files', () => {
        it('with , as separator and \\n as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: ',',
              eol: '\\n',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual(',');
          expect(options.eol).toEqual('\n');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual(undefined);
        });

        it('with \\2 as separator and \\1 as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: '\\2',
              eol: '\\1',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual(String.fromCharCode(2));
          expect(options.eol).toEqual(String.fromCharCode(1));
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual(undefined);
        });

        it('with \\" as separator and \\\\ as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: '\\"',
              eol: '\\\\',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual('"');
          expect(options.eol).toEqual('\\');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual(undefined);
        });

        it('with \\t as separator and \\1 as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.TAR,
              separator: '\\t',
              eol: '\\1',
              header: true,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.TAR);
          expect(options.separator).toEqual('\t');
          expect(options.eol).toEqual(String.fromCharCode(1));
          expect(options.header).toEqual(true);
          expect(options.quote).toEqual(undefined);
        });

        it('with \\\\ as separator and \\u0020 as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.ZIP,
              separator: '\\\\',
              eol: '\\u0020',
              header: false,
              quote: 'double',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.ZIP);
          expect(options.separator).toEqual('\\');
          expect(options.eol).toEqual(' ');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual(`"`);
        });

        it('with | as separator and \\" as eol', () => {
          const options = DataSetLogic.createLocalFileSampleDataTabularParsingOptions(
            {
              format: FileFormat.GZIP,
              separator: '|',
              eol: '\\"',
              header: false,
              quote: 'single',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.GZIP);
          expect(options.separator).toEqual('|');
          expect(options.eol).toEqual('"');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual(`'`);
        });
      });

      describe('remote files', () => {
        it('with , as separator and \\n as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: ',',
              eol: '\\n',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual(',');
          expect(options.eol).toEqual('\\n');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual('');
        });

        it('with \\2 as separator and \\1 as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: '\\2',
              eol: '\\1',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual('\\2');
          expect(options.eol).toEqual('\\1');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual('');
        });

        it('with \\" as separator and \\\\ as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.None,
              separator: '\\"',
              eol: '\\\\',
              header: false,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.None);
          expect(options.separator).toEqual('\\"');
          expect(options.eol).toEqual('\\\\');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual('');
        });

        it('with \\t as separator  and \\1 as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.TAR,
              separator: '\\t',
              eol: '\\1',
              header: true,
              quote: '',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.TAR);
          expect(options.separator).toEqual('\\t');
          expect(options.eol).toEqual('\\1');
          expect(options.header).toEqual(true);
          expect(options.quote).toEqual('');
        });

        it('with \\\\ as separator and \\u0020 as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.ZIP,
              separator: '\\\\',
              eol: '\\u0020',
              header: false,
              quote: 'double',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.ZIP);
          expect(options.separator).toEqual('\\\\');
          expect(options.eol).toEqual('\\u0020');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual('double');
        });

        it('with | as separator and \\" as eol', () => {
          const options = DataSetLogic.createTabularParsingOptions(
            {
              format: FileFormat.GZIP,
              separator: '|',
              eol: '\\"',
              header: false,
              quote: 'single',
              json: false
            }
          );
          expect(options.fileFormat).toEqual(FileFormat.GZIP);
          expect(options.separator).toEqual('|');
          expect(options.eol).toEqual('\\"');
          expect(options.header).toEqual(false);
          expect(options.quote).toEqual('single');
        });
      });
    });

    it('with JSON', () => {
      const options = DataSetLogic.createJSONParsingOptions(
        {
          format: FileFormat.None,
          eol: '\\n',
          json: true
        }
      );
      expect(options.fileFormat).toEqual(FileFormat.None);
      expect(options.eol).toEqual('\\n');
    });
  });

  describe('should create data schema correctly', () => {
    it('with csv schema', () => {
      const dataSchema = DataSetLogic.createTabularDataSchema(
        ['column1', 'column2'],
      );
      expect(dataSchema.length).toBe(2);
      expect(dataSchema[0].name).toBe('column1');
      expect(dataSchema[1].name).toBe('column2');
    });
  });

  describe('should get data set label correctly', () => {
    it('with local file', () => {
      const label = DataSetLogic.createDataSetLabel(
        false,
        'some-file.csv',
      );
      expect(label).toBe('some-file.csv');
    });

    it('with s3', () => {
      const label = DataSetLogic.createDataSetLabel(
        false,
        's3a://s3-bucket/s3-file.csv',
      );
      expect(label).toBe('s3a://s3-bucket/s3-file.csv');
    });

    it('with gcs', () => {
      const label = DataSetLogic.createDataSetLabel(
        false,
        'gs://gcs-bucket/gcs-file.csv',
      );
      expect(label).toBe('gs://gcs-bucket/gcs-file.csv');
    });

    it('with abs', () => {
      const label = DataSetLogic.createDataSetLabel(
        false,
        'abfss://<EMAIL>/persondata.csv',
      );
      expect(label).toBe('abfss://<EMAIL>/persondata.csv');
    });
  });

  it('should get JSON data type correctly', () => {
    expect((<any>DataSetLogic).getJSONDataType('null')).toEqual(JSONDataType.Null);
    expect((<any>DataSetLogic).getJSONDataType('number')).toEqual(JSONDataType.Number);
    expect((<any>DataSetLogic).getJSONDataType('integer')).toEqual(JSONDataType.Integer);
    expect((<any>DataSetLogic).getJSONDataType('boolean')).toEqual(JSONDataType.Boolean);
    expect((<any>DataSetLogic).getJSONDataType('string')).toEqual(JSONDataType.String);
    expect((<any>DataSetLogic).getJSONDataType('object')).toEqual(JSONDataType.Object);
    expect((<any>DataSetLogic).getJSONDataType('array')).toEqual(JSONDataType.Array);
  });

  it('should convert the json sample data to the standard json schema correctly', () => {
    const standardJSONSchema = DataSetLogic.convertSampleDataToStandardSchema(
      mockJSONSampleData,
    );
    expect(standardJSONSchema).toEqual(mockStandardJSONSchema);
  });

  it('should convert the standard json schema to the in-memory json schema correctly', () => {
    const jsonSchema = DataSetLogic.convertStandardSchemaToInMemorySchema(
      mockStandardJSONSchema,
    );
    expect(jsonSchema).toEqual(mockJSONSchema);
  });

  it('should conver the ABS url input correctly', () => {
    expect(DataSetLogic.convertABSInputPathToFileUri(
      'https://abstest1.blob.core.windows.net/container1/invalidTest.json'
    )).toEqual('abfss://<EMAIL>/invalidTest.json');
  });

  it('should reconstruct file uri correctly', () => {
    expect(DataSetLogic.getFileUriForConnector('s3://bucket/object', DataSourceType.S3))
      .toEqual('s3a://bucket/object');
    expect(DataSetLogic.getFileUriForConnector('gs://bucket/object', DataSourceType.GoogleCloudStorage))
      .toEqual('gs://bucket/object');
    expect(DataSetLogic.getFileUriForConnector(
      'https://abstest1.blob.core.windows.net/container1/invalidTest.json',
      DataSourceType.AzureBlobStorage
    )).toEqual('abfss://<EMAIL>/invalidTest.json');
  });

  describe('should check if remote object uri has valid format', () => {
    let dataSourceType: string;

    /**
     * Test isValidUri function.
     *
     * @param dataSourceType
     * @param prefix
     */
    const testUriFormat = (dsType: string, prefix: string) => {
      expect(DataSetLogic.isValidRemoteUri(dsType, '')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, 'bucket')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, '/bucket/')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, '/bucket/object')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://`)).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/object`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/object/`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/folder/object`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/folder/object/`)).toBeTruthy();
    };

    it('should check if S3 uri has the correct format', () => {
      dataSourceType = DataSourceType.S3;
      // Test isValidRemoteUri function with S3 data source type and 's3' prefix.
      testUriFormat(dataSourceType, 's3');
      // Test isValidRemoteUri function with S3 data source type and 'S3' prefix.
      testUriFormat(dataSourceType, 'S3');
    });

    it('should check if gsutil uri has the correct format', () => {
      dataSourceType = DataSourceType.GoogleCloudStorage;
      // Test isValidRemoteUri function with GCS data source type and 'gs' prefix.
      testUriFormat(dataSourceType, 'gs');
    });

    it('should check if ABS uri has the correct format', () => {
      dataSourceType = DataSourceType.AzureBlobStorage;
      // Test isValidRemoteUri function with ABS data source type and 'http' prefix.
      testUriFormat(dataSourceType, 'http');
      // Test isValidRemoteUri function with ABS data source type and 'https' prefix.
      testUriFormat(dataSourceType, 'https');
    });
  });

  describe('should check if remote object uri has valid format', () => {
    let dataSourceType: string;

    /**
     * Test isValidUri function.
     *
     * @param dataSourceType
     * @param prefix
     */
    const testUriFormat = (dsType: string, prefix: string) => {
      expect(DataSetLogic.isValidRemoteUri(dsType, '')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, 'bucket')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, '/bucket/')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, '/bucket/object')).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://`)).toBeFalsy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/object`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/object/`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/folder/object`)).toBeTruthy();
      expect(DataSetLogic.isValidRemoteUri(dsType, `${prefix}://bucket/folder/object/`)).toBeTruthy();
    };

    it('should check if S3 uri has the correct format', () => {
      dataSourceType = DataSourceType.S3;
      // Test isValidRemoteUri function with S3 data source type and 's3' prefix.
      testUriFormat(dataSourceType, 's3');
      // Test isValidRemoteUri function with S3 data source type and 'S3' prefix.
      testUriFormat(dataSourceType, 'S3');
    });

    it('should check if gsutil uri has the correct format', () => {
      dataSourceType = DataSourceType.GoogleCloudStorage;
      // Test isValidRemoteUri function with GCS data source type and 'gs' prefix.
      testUriFormat(dataSourceType, 'gs');
    });

    it('should check if ABS uri has the correct format', () => {
      dataSourceType = DataSourceType.AzureBlobStorage;
      // Test isValidRemoteUri function with ABS data source type and 'http' prefix.
      testUriFormat(dataSourceType, 'http');
      // Test isValidRemoteUri function with ABS data source type and 'https' prefix.
      testUriFormat(dataSourceType, 'https');
    });
  });
});
