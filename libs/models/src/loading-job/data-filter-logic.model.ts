import { BaseCondition, LoadingVariable } from '../expression';
import { DataFieldSchema, JSONDataFieldSchema } from './data-set.interface';

export class DataFilterLogic {
  /**
   * Convert ExprModel's LoadingVariable operand value for for GSQL.
   *
   * @param {BaseCondition} exprJson
   * @param {DataFieldSchema[]} currentDataSchema
   * @memberof DataFilterLogic
   */
  static convertJSONExprModelForGSQL(
    exprModel: BaseCondition,
    currentDataSchema: DataFieldSchema[]
  ) {
    if (!exprModel.operands || exprModel.operands.length === 0) {
      return;
    }

    exprModel.operands.forEach(operand => {
      if (operand instanceof LoadingVariable && typeof operand.value === 'number') {
        const index = operand.value;
        const value = (<JSONDataFieldSchema>currentDataSchema[index]).path.map(item => `"${item}"`).join(':');
        operand.value = value;
      }

      if (operand instanceof BaseCondition) {
        this.convertJSONExprModelForGSQL(operand, currentDataSchema);
      }
    });
  }

  /**
   * Convert ExprModel's LoadingVariable operand value for ExpressionForm.
   *
   * @param {BaseCondition} exprJson
   * @param {DataFieldSchema[]} currentDataSchema
   * @memberof DataFilterLogic
   */
  static convertJSONExprModelForVisualExprForm(
    exprModel: BaseCondition,
    currentDataSchema: DataFieldSchema[]
  ) {
    if (!exprModel || !exprModel.operands || exprModel.operands.length === 0) {
      return;
    }

    exprModel.operands.forEach(operand => {
      if (operand instanceof LoadingVariable && typeof operand.value === 'string') {
        const value = operand.value.split(':').map(item => item.replace('"', '').replace('"', ''));
        const index = currentDataSchema.map(dataFieldSchema =>
          JSON.stringify((<JSONDataFieldSchema>dataFieldSchema).path)
        ).indexOf(JSON.stringify(value));
        operand.value = index;
      }

      if (operand instanceof BaseCondition) {
        this.convertJSONExprModelForVisualExprForm(operand, currentDataSchema);
      }
    });
  }
}
