import { LoadingJobPrefix } from './loading-job.model';

export interface KafkaDataSourceConfig {
  broker: string;
}

export interface S3DataSourceConfig {
  'file.reader.settings.fs.s3a.access.key': string;
  'file.reader.settings.fs.s3a.secret.key': string;
}

export interface GSQLDataSource {
  name: string;
  type: string;
  config: KafkaDataSourceConfig | S3DataSourceConfig;
}

export class GSQLDataSourceLogic {
  /**
   * Check if loading job is a Kafka loading job.
   *
   * @param {string} jobName
   * @returns {boolean}
   */
  static isKafkaLoadingJob(jobName: string): boolean {
    // TODO: Support more data source types in the future.
    return [
      LoadingJobPrefix.GCS,
      LoadingJobPrefix.S3,
      LoadingJobPrefix.ABS,
      LoadingJobPrefix.SNOWFLAKE,
    ].some(
      prefix => jobName.startsWith(prefix)
    );
  }
}
