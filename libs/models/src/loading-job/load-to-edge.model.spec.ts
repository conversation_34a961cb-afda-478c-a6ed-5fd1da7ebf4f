import { Vertex, Edge, Graph, Attribute } from '../topology';

import { DBLoadingStatementStyle } from './db-loading-job.interface';
import { GSQLLoadToEdgeJson } from './gsql-loading-job.interface';
import { LoadingJobLogic } from './loading-job.model';
import { SourceType, TokenFunction } from './loading-mapping.model';
import { LoadToEdgeLogic } from './load-to-edge.model';
import { FileFormat } from './data-set.interface';

describe('GSQLLoadToEdgeModel', () => {
  let dbLoadingStatementStyle: DBLoadingStatementStyle;
  let mockGsqlLoadToEdgeJson: GSQLLoadToEdgeJson;

  beforeEach(() => {
    dbLoadingStatementStyle = {
      sourcePosition: {
        x: 0.2,
        y: 0.5
      },
      targetPosition: {
        x: 0.8,
        y: 0.5
      },
      middlePositions: [
        {
          x: 0.5,
          y: 0.5
        },
        {
          x: 0.5,
          y: 0.6
        }
      ]
    };

    mockGsqlLoadToEdgeJson = {
      Type: 'Edge',
      UsingClauses: {},
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        },
        {
          Type: 'SrcColIndex',
          Value: 1
        },
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                },
                {
                  Type: 'Default'
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person_movie',
      FromVertexType: 'person',
      ToVertexType: 'movie',
      DataSource: {
        Type: 'Online'
      }
    };
  });

  it('should clone load to edge successfully', () => {
    const loadToEdgeOrigin = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );
    const loadToEdge = LoadToEdgeLogic.clone(loadToEdgeOrigin);
    loadToEdgeOrigin.edgeName = 'e2';
    expect(loadToEdgeOrigin.edgeName).toBe('e2');
    expect(loadToEdge.edgeName).toBe('person_movie');
    expect(loadToEdge.fromVertexType).toBe('person');
    expect(loadToEdge.toVertexType).toBe('movie');
    expect(loadToEdge.style.sourcePosition.x).toBe(0.2);
    expect(loadToEdge.style.targetPosition.y).toBe(0.5);
    expect(loadToEdge.mappings.length).toBe(3);
    expect(loadToEdge.mappings[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadToEdge.mappings[0].index).toBe(0);
    expect(loadToEdge.mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadToEdge.mappings[1].index).toBe(1);
    expect(loadToEdge.mappings[2].sourceType).toBe(SourceType.MappingWidget);
    expect(loadToEdge.mappings[2].index).toBe(1);
    expect(loadToEdge.mappingWidgets.length).toBe(2);
    expect((loadToEdge.mappingWidgets[0] as TokenFunction).funcName).toBe('gsql_concat');
    expect(loadToEdge.mappingWidgets[0].params.length).toBe(3);
    expect(loadToEdge.mappingWidgets[0].params[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToEdge.mappingWidgets[0].params[0].index).toBe(2);
    expect(loadToEdge.mappingWidgets[0].params[1].sourceType).toBe(
      SourceType.Literal
    );
    expect(loadToEdge.mappingWidgets[0].params[1].literal).toBe('a');
    expect(loadToEdge.mappingWidgets[0].params[2].sourceType).toBe(
      SourceType.Default
    );
    expect((loadToEdge.mappingWidgets[1] as TokenFunction).funcName).toBe('gsql_concat');
    expect(loadToEdge.mappingWidgets[1].params.length).toBe(2);
    expect(loadToEdge.mappingWidgets[1].params[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToEdge.mappingWidgets[1].params[0].index).toBe(1);
    expect(loadToEdge.mappingWidgets[1].params[1].sourceType).toBe(
      SourceType.MappingWidget
    );
    expect(loadToEdge.mappingWidgets[1].params[1].index).toBe(0);
  });

  it('should fail semantic check because edge is not in schema', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'v1';
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to edge
    const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );

    const semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Edge type "person_movie" doesn\'t exist in graph schema.'
    );
  });

  it(
    'should fail semantic check because data mapping number and edge attribute number ' +
      'not match',
    () => {
      // Prepare the schema
      const schema = new Graph();
      const vertex = new Vertex();
      vertex.name = 'person';
      const edge = new Edge();
      edge.name = 'person_movie';
      edge.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      edge.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
      schema.vertexTypes.push(vertex);
      schema.edgeTypes.push(edge);

      // Prepare the load to edge
      const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
        mockGsqlLoadToEdgeJson,
        dbLoadingStatementStyle,
        [],
      );

      const semanticCheckResult = LoadToEdgeLogic.semanticCheck(
        loadToEdge,
        ['name', 'age'],
        schema
      );
      expect(semanticCheckResult.success).toBeFalsy();
      expect(semanticCheckResult.message).toBe(
        `Data loading mapping number and edge attribute number don't match.`
      );
    }
  );

  it('should fail semantic check because edge source vertex mapping cannot be empty', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to edge
    const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );
    loadToEdge.mappings[0].sourceType = SourceType.Default;

    const semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Need to map data column to edge source vertex.'
    );
  });

  it('should fail semantic check because edge target vertex mapping cannot be empty', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to edge
    const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );
    loadToEdge.mappings[1].sourceType = SourceType.Default;

    const semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Need to map data column to edge target vertex.'
    );
  });

  it('should fail semantic check because edge discriminator attributes mapping can not be empty', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        },
        IsDiscriminator: true
      })
    );
    edge.discriminatorCount = 1;
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to edge
    const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );
    loadToEdge.mappings[2].sourceType = SourceType.Default;

    const semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Need to map data column to discriminator attributes.'
    );
  });

  it('should fail semantic check because load to edge from | to vertex type conflict with schema', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'movie', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to edge
    const loadToEdge = LoadToEdgeLogic.loadFromGSQLAndDB(
      mockGsqlLoadToEdgeJson,
      dbLoadingStatementStyle,
      [],
    );

    let semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Load to edge statement source vertex type and target vertex type '
      + 'is not consistent with graph schema.'
    );

    edge.fromToVertexTypePairs[0].from = 'person';
    semanticCheckResult = LoadToEdgeLogic.semanticCheck(
      loadToEdge,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Load to edge statement source vertex type and target vertex type '
      + 'is not consistent with graph schema.'
    );
  });

  it('should create one load-to-edge statement correctly', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'STRING'
        }
      })
    );
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);

    // database loading job json
    const dbLoadingJobJson = {
      graphName: 'MyGraph',
      loadingJobName: 'load_job',
      loadingStatementsStyle: [],
      dataSourceJson: {
        type: 'file',
        uri: '/resources/data_set/gsql/persons.csv',
        options: {
          fileFormat: FileFormat.None,
          eol: '\n',
          separator: ',',
          header: 'true'
        },
        position: {
          x: 0.5,
          y: 0.2
        }
      },
      header: ['name', 'age', 'col1', 'col2'],
      sampleData: [['src', '25', '', ''], ['src2', '26', '', '']]
    };

    // Load loading job information
    const loadingJobData = LoadingJobLogic.loadFromGSQLAndDB(
      undefined,
      dbLoadingJobJson
    );

    // Create one load-to-edge statement
    const loadToEdge = LoadToEdgeLogic.createLoadToEdge(
      schema.getEdge('person_movie'),
      'person',
      'movie',
      {
        HEADER: 'FALSE',
        EOL: '\n',
        SEPARATOR: ','
      }
    );

    expect(loadToEdge.edgeName).toBe('person_movie');
    expect(loadToEdge.fromVertexType).toBe('person');
    expect(loadToEdge.toVertexType).toBe('movie');
    expect(loadToEdge.mappingWidgets.length).toBe(0);
    expect(loadToEdge.mappings.length).toBe(3);
    loadToEdge.mappings.forEach(mapping => {
      expect(mapping.sourceType).toBe(SourceType.Default);
    });
    expect(loadToEdge.style.sourcePosition.x).toBe(30);
    expect(loadToEdge.style.sourcePosition.y).toBe(300);
    expect(loadToEdge.style.targetPosition.x).toBe(650);
    expect(loadToEdge.style.targetPosition.y).toBe(300);
    expect(loadToEdge.style.middlePositions.length).toBe(0);
  });
});
