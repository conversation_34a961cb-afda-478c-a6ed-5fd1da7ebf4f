import {
  GS<PERSON><PERSON><PERSON><PERSON><PERSON>son, GSQLLiteral<PERSON>son,
  GSQLLoadingAstJson, GSQLSrcColIndexJson,
  GSQLSrcColNameJson, GSQLUDFJson
} from './gsql-loading-job.interface';

describe('GSQLLoadingJobInterface', () => {

  // ========================= Loading Mapping types ===========================
  it('should access SrcColIndex properties correctly.', () => {
    const mockMappingJson: GSQLLoadingAstJson = {
      Type: 'SrcColIndex',
      Value: 2
    };

    const mockSrcColIndexJson: GSQLSrcColIndexJson = <GSQLSrcColIndexJson>mockMappingJson;
    expect(mockSrcColIndexJson.Type).toBe('SrcColIndex');
    expect(mockSrcColIndexJson.Value).toBe(2);
  });

  it('should access SrcColName properties correctly.', () => {
    const mockMappingJson1: GSQLLoadingAstJson = {
      Type: 'SrcColName',
      Value: 'Name'
    };

    const mockSrcColNameJson1: GSQLSrcColNameJson = <GSQLSrcColNameJson>mockMappingJson1;
    expect(mockSrcColNameJson1.Type).toBe('SrcColName');
    expect(mockSrcColNameJson1.Value).toBe('Name');
    expect(mockSrcColNameJson1.SrcKeyNames).toBeUndefined();

    const mockMappingJson2: GSQLLoadingAstJson = {
      Type: 'SrcColName',
      Value: 'Name',
      SrcKeyNames: ['c1', 'c2']
    };

    const mockSrcColNameJson2: GSQLSrcColNameJson = <GSQLSrcColNameJson>mockMappingJson2;
    expect(mockSrcColNameJson2.Type).toBe('SrcColName');
    expect(mockSrcColNameJson2.Value).toBe('Name');
    expect(mockSrcColNameJson2.SrcKeyNames.length).toBe(2);
    expect(mockSrcColNameJson2.SrcKeyNames[0]).toBe('c1');
    expect(mockSrcColNameJson2.SrcKeyNames[1]).toBe('c2');

    mockSrcColNameJson2.SrcKeyNames.splice(0, 1);
    expect(mockMappingJson2['SrcKeyNames'].length).toBe(1);
  });

  it('should access Literal properties correctly.', () => {
    const mockMappingJson: GSQLLoadingAstJson = {
      Type: 'Literal',
      Value: 'src'
    };

    const mockLiteralJson: GSQLLiteralJson = <GSQLLiteralJson>mockMappingJson;
    expect(mockLiteralJson.Type).toBe('Literal');
    expect(mockLiteralJson.Value).toBe('src');
  });

  it('should access Default properties correctly.', () => {
    const mockMappingJson: GSQLLoadingAstJson = {
      Type: 'Default'
    };

    const mockDefaultJson: GSQLDefaultJson = <GSQLDefaultJson>mockMappingJson;
    expect(mockDefaultJson.Type).toBe('Default');
  });

  it('should access UDF properties correctly.', () => {
    const mockMappingJson: GSQLLoadingAstJson = {
      Type: 'UDF',
      UdfName: 'gsql_concat',
      Params: [
        {
          Type: 'UDF',
          UdfName: 'to_int',
          Params: [
            {
              Type: 'Literal',
              Value: '123'
            },
          ]
        },
        {
          Type: 'Default'
        },
        {
          Type: 'SrcColIndex',
          Value: 3
        }
      ]
    };

    const mockUDFJson: GSQLUDFJson = <GSQLUDFJson>mockMappingJson;
    expect(mockUDFJson.Type).toBe('UDF');
    expect(mockUDFJson.UdfName).toBe('gsql_concat');
    expect(mockUDFJson.Params.length).toBe(3);
    const mockUDFJson2: GSQLUDFJson = <GSQLUDFJson>mockUDFJson.Params[0];
    expect(mockUDFJson2.Type).toBe('UDF');
    expect(mockUDFJson2.UdfName).toBe('to_int');
    expect(mockUDFJson2.Params.length).toBe(1);
    const mockLiteral: GSQLLiteralJson = <GSQLLiteralJson>mockUDFJson2.Params[0];
    expect(mockLiteral.Type).toBe('Literal');
    expect(mockLiteral.Value).toBe('123');
    const mockDefault: GSQLDefaultJson = <GSQLDefaultJson>mockUDFJson.Params[1];
    expect(mockDefault.Type).toBe('Default');
    const mockSrcColIndex: GSQLSrcColIndexJson = <GSQLSrcColIndexJson>mockUDFJson.Params[2];
    expect(mockSrcColIndex.Type).toBe('SrcColIndex');
    expect(mockSrcColIndex.Value).toBe(3);
  });
});
