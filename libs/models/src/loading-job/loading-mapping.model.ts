import { Condition<PERSON><PERSON> } from "../expression";
import { GraphStudioError } from "../error";
import { Attribute, Edge, Graph, GSQLUdtJson, Vertex } from "../topology";
import { HelperFunctions } from "../utils";

import {
  DBPositionStyle,
  DBLoadingStatementStyle,
  DBMappingWidgetIndex,
} from "./db-loading-job.interface";
import {
  supportedLoadingMappingTypes,
  GSQLLoadingAstJson,
  GSQLLiteralJson,
  GSQLDefaultJson,
  GSQLSrcColIndexJson,
  GSQLUDFJson,
  GSQLKVPairJson,
  GSQLUDTMappingJson,
  GSQLSrcColNameJson,
  SUPPORTED_PROPNAMES,
} from "./gsql-loading-job.interface";
import { LoadingJobData } from "./loading-job.model";
import { LoadToEdgeData } from "./load-to-edge.model";
import { LoadToVertexData } from "./load-to-vertex.model";
import {
  DataFieldSchema,
  DataFormat,
  JSONDataFieldSchema,
  JSONDataType,
} from "./data-set.interface";

/**
 * Describing the data source type of each single loading item.
 * - DataSourceColumn: one column in column-wise parsed data source
 * - TokenFunction: a token function
 * - Map: a map
 * - Tuple: a UDT
 * - Default: use default value
 * - Literal: use string literal
 * - ...
 *
 * @export
 * @enum {number}
 */
export const enum SourceType {
  DataSourceColumn,
  MappingWidget,
  Default,
  Literal,
  // In the future, might support json_flatten_array, reduce function, split, etc.
}

/**
 * One column mapping source of DataSourceColumn type.
 * - type: SourceType.DataSourceColumn
 * - columnIndex: index of data source column
 *
 * @export
 * @interface OneColumnMappingDataSourceColumnSource
 */
export interface OneColumnMappingDataSourceColumnSource {
  type: SourceType;
  columnIndex: number;
}

/**
 * One column mapping source of TokenFunction type.
 * - type: SourceType.MappingWidget
 * - mappingWidgetIndex: index of mapping widget array
 *
 * @export
 * @interface OneColumnMappingMappingWidgetSource
 */
export interface OneColumnMappingMappingWidgetSource {
  type: SourceType;
  mappingWidgetIndex: number;
}

/**
 * Describing one column mapping's target type.
 * - GraphEntity: vertex primary id, edge source | target vertex, attributes
 * - MappingWidget: mapping widget parameter
 *
 * @export
 * @enum {number}
 */
export const enum OneColumnMappingTargetType {
  GraphEntity,
  MappingWidget,
  // In the future, might support json_flatten_array, reduce function, split, etc.
}

/**
 * One column mapping target of graph entity type.
 * - type: OneColumnMappingTargetType.GraphEntity
 * - mappingIndex: index of mapping array
 *
 * @export
 * @interface OneColumnMappingTarget
 */
export interface OneColumnMappingGraphEntityTarget {
  type: OneColumnMappingTargetType;
  mappingIndex: number;
}

/**
 * One column mapping target of mapping widget type.
 * - type: OneColumnMappingTargetType.MapingWidget
 * - mappingWidgetIndex: index of mapping widget array
 * - paramIndex: index of the mapping widget's parameter array
 *
 * @export
 * @interface OneColumnMappingTokenFunctionTarget
 */
export interface OneColumnMappingMappingWidgetTarget {
  type: OneColumnMappingTargetType;
  mappingWidgetIndex: number;
  paramIndex: number;
}

/**
 * Describes loading mapping of one mapping widget.
 * - params: the loading source for each parameter of the mapping widget.
 *
 * @export
 * @interface MappingWidget
 */
export interface MappingWidget {
  params: OneColumnMapping[];
  name?: string;
}

/**
 * Describes loading mapping of one token function.
 * - funcName: token function's name, like gsql_concat, gsql_to_epoch, etc
 *
 * @export
 * @interface TokenFunction
 */
export interface TokenFunction extends MappingWidget {
  funcName: string;
}

/**
 * Describes loading mapping of one Map widget.
 * - keyType: map key data type, INT, STRING, etc
 * - valType: map val data type, INT, STRING, udt name, etc
 *
 * @export
 * @interface MapWidget
 * @extends {MappingWidget}
 */
export interface MapWidget extends MappingWidget {
  keyType: string;
  valType: string;
  udtType: string;
}

/**
 * Describes loading mapping of one UDT widget.
 * - udtName: the udt name.
 * - udt: the UDT.
 *
 * @export
 * @interface TupleWidget
 * @extends {MappingWidget}
 */
export interface TupleWidget extends MappingWidget {
  udtName: string;
  udt: GSQLUdtJson;
}

/**
 * Describes loading mapping of one user defined token function.
 * - name: token function's name, like gsql_concat, gsql_to_epoch, etc
 * - returnType: return type of function
 * - paramNumber: number of parameters taken (undefined means any amount)
 * - paramNumberFixed: if parameter number is predetermined
 * - doc: includes the description and samples
 * - code: actual implementation of token function
 *
 * @export
 * @interface TokenFunctionSignature
 */
export interface TokenFunctionSignature {
  name: string;
  returnType: string[];
  paramNumber: number;
  paramNumberFixed: boolean;
  doc?: {
    description: string;
    samples: {
      sampleInput: string[];
      sampleOutput: string;
    }[];
  };
  code?: string;
}

/**
 * Describes loading mapping of one user defined token function in GSQL response.
 * - name: token function's name, like gsql_concat, gsql_to_epoch, etc
 * - returnType: return type of function
 * - code: actual implementation of token function
 * - paramNumber: number of parameters taken (undefined means any amount)
 *
 * @export
 * @interface GSQLTokenFunction
 */
export interface GSQLTokenFunction {
  name: string;
  returnType: string;
  code: string;
  paramNumber?: number;
}

// Combination of builtin and user defined token functions
export const combinedTokenFunctionList: TokenFunctionSignature[] = [];

// Supported token functions
export const builtinTokenFunctionNames: string[] = [
  "gsql_concat",
  "gsql_reverse",
  "gsql_to_bool",
  "gsql_to_int",
  "gsql_to_uint",
  "gsql_current_time_epoch",
  "gsql_ts_to_epoch_seconds",
  "gsql_is_true",
  "gsql_is_false",
  "gsql_is_not_empty_string",
  "gsql_token_equal",
  "gsql_token_ignore_case_equal",
  "gsql_upper",
  "gsql_lower",
  "gsql_trim",
  "gsql_ltrim",
  "gsql_rtrim",
  "gsql_year",
  "gsql_month",
  "gsql_day",
  "gsql_year_epoch",
  "gsql_month_epoch",
  "gsql_day_epoch",
  "gsql_find",
  "gsql_length",
  "gsql_replace",
  "gsql_regex_match",
  "gsql_regex_replace",
  "gsql_substring",
  "gsql_uuid_v4",
];

// Each token function's compatible attribute types
const stringTypeList: string[] = ["STRING", "STRING COMPRESS"];
const intTypeList: string[] = ["INT"];
const uintTypeList: string[] = ["UINT"];
const boolTypeList: string[] = ["BOOL"];
export const builtinTokenFunctionReturnType: Map<string, string[]> = new Map([
  ["gsql_concat", stringTypeList],
  ["gsql_reverse", stringTypeList],
  ["gsql_to_bool", boolTypeList],
  ["gsql_to_int", intTypeList],
  ["gsql_to_uint", uintTypeList],
  ["gsql_current_time_epoch", uintTypeList],
  ["gsql_ts_to_epoch_seconds", uintTypeList],
  ["gsql_is_true", boolTypeList],
  ["gsql_is_false", boolTypeList],
  ["gsql_is_not_empty_string", boolTypeList],
  ["gsql_token_equal", boolTypeList],
  ["gsql_token_ignore_case_equal", boolTypeList],
  ["gsql_upper", stringTypeList],
  ["gsql_lower", stringTypeList],
  ["gsql_trim", stringTypeList],
  ["gsql_ltrim", stringTypeList],
  ["gsql_rtrim", stringTypeList],
  ["gsql_year", intTypeList],
  ["gsql_month", intTypeList],
  ["gsql_day", intTypeList],
  ["gsql_year_epoch", intTypeList],
  ["gsql_month_epoch", intTypeList],
  ["gsql_day_epoch", intTypeList],
  ["gsql_find", intTypeList],
  ["gsql_length", uintTypeList],
  ["gsql_replace", stringTypeList],
  ["gsql_regex_match", boolTypeList],
  ["gsql_regex_replace", stringTypeList],
  ["gsql_substring", stringTypeList],
  ["gsql_uuid_v4", stringTypeList],
]);

// Each token function's parameter number, undefined means user can specify any number of params
export const builtinTokenFunctionParamNumber: Map<string, number> = new Map([
  ["gsql_concat", undefined],
  ["gsql_reverse", 1],
  ["gsql_to_bool", 1],
  ["gsql_to_int", 1],
  ["gsql_to_uint", 1],
  ["gsql_current_time_epoch", 1],
  ["gsql_ts_to_epoch_seconds", 1],
  ["gsql_is_true", 1],
  ["gsql_is_false", 1],
  ["gsql_is_not_empty_string", 1],
  ["gsql_token_equal", 2],
  ["gsql_token_ignore_case_equal", 2],
  ["gsql_upper", 1],
  ["gsql_lower", 1],
  ["gsql_trim", 1],
  ["gsql_ltrim", 1],
  ["gsql_rtrim", 1],
  ["gsql_year", 1],
  ["gsql_month", 1],
  ["gsql_day", 1],
  ["gsql_year_epoch", 1],
  ["gsql_month_epoch", 1],
  ["gsql_day_epoch", 1],
  ["gsql_find", 2],
  ["gsql_length", 1],
  ["gsql_replace", 4],
  ["gsql_regex_match", 2],
  ["gsql_regex_replace", 3],
  ["gsql_substring", 3],
  ["gsql_uuid_v4", 1],
]);

// Each token function's description, sample input and sample output
export const builtinTokenFunctionDocument: Map<
  string,
  {
    description: string;
    samples: {
      sampleInput: string[];
      sampleOutput: string;
    }[];
  }
> = new Map([
  [
    "gsql_concat",
    {
      description:
        "Returns a string which is the concatenation of all the input strings. " +
        "User need specify how many strings he/she wants to concatenate.",
      samples: [
        {
          sampleInput: ["Kevin", "#", "US"],
          sampleOutput: '"Kevin#US"',
        },
      ],
    },
  ],
  [
    "gsql_reverse",
    {
      description: "Returns a string with the characters in the reverse order of the input string.",
      samples: [
        {
          sampleInput: ["Kevin"],
          sampleOutput: '"niveK"',
        },
      ],
    },
  ],
  [
    "gsql_to_bool",
    {
      description:
        'Returns true if the input is either "t" or "true", with case insensitive ' +
        "checking. Returns false otherwise.",
      samples: [
        {
          sampleInput: ["True"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["t"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["F"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_to_int",
    {
      description:
        "If input is the string representation of an int, the function returns " +
        "that integer. If input is the string representation of a float, the function returns " +
        "that number cast as an int.",
      samples: [
        {
          sampleInput: ["4"],
          sampleOutput: "4",
        },
        {
          sampleInput: ["-5"],
          sampleOutput: "-5",
        },
        {
          sampleInput: ["4.2"],
          sampleOutput: "4",
        },
      ],
    },
  ],
  [
    "gsql_to_uint",
    {
      description:
        "If input is the string representation of an unsigned int, the function returns " +
        "that integer. If input is the string representation of a nonnegative float, the function " +
        "returns that number cast as an unsigned int.",
      samples: [
        {
          sampleInput: ["4"],
          sampleOutput: "4",
        },
        {
          sampleInput: ["4.2"],
          sampleOutput: "4",
        },
      ],
    },
  ],
  [
    "gsql_current_time_epoch",
    {
      description: "Returns the current time in Unix epoch seconds.",
      samples: [
        {
          sampleInput: [],
          sampleOutput: "1500336217",
        },
      ],
    },
  ],
  [
    "gsql_ts_to_epoch_seconds",
    {
      description:
        "Converts a timestamp in canonical string format to Unix epoch time, which is " +
        "the int number of seconds since Jan. 1, 1970. The main_string should be in one of " +
        'the following 3 formats:\n"%Y-%m-%d %H:%M:%S"\n"%Y/%m/%d %H:%M:%S"\n"%Y-%m-%dT%H:%M:%S.000z"' +
        "  // text after . is ignored",
      samples: [
        {
          sampleInput: ["2017-07-17 12:30:13"],
          sampleOutput: "1500294613",
        },
      ],
    },
  ],
  [
    "gsql_is_true",
    {
      description:
        'Returns true if input is either "t" or "true" (case insensitive). ' +
        "Returns false otherwise.",
      samples: [
        {
          sampleInput: ["T"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["True"],
          sampleOutput: "true",
        },
      ],
    },
  ],
  [
    "gsql_is_false",
    {
      description:
        'Returns true if input is either "f" or "false" (case insensitive). ' +
        "Returns false otherwise.",
      samples: [
        {
          sampleInput: ["F"],
          sampleOutput: "false",
        },
        {
          sampleInput: ["False"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_is_not_empty_string",
    {
      description:
        "Returns true if input is empty after removing white space. " + "Returns false otherwise.",
      samples: [
        {
          sampleInput: [""],
          sampleOutput: "true",
        },
        {
          sampleInput: [" Something"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_token_equal",
    {
      description:
        "Returns true if 1st input is exactly the same (case sensitive) as 2nd input. " +
        "Returns false otherwise.",
      samples: [
        {
          sampleInput: ["abc", "abc"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["Abc", "abc"],
          sampleOutput: "false",
        },
        {
          sampleInput: ["a", "b"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_token_ignore_case_equal",
    {
      description:
        "Returns true if 1st input is exactly the same (case insensitive) as 2nd input. " +
        "Returns false otherwise.",
      samples: [
        {
          sampleInput: ["abc", "abc"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["Abc", "abc"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["a", "b"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_upper",
    {
      description: "Converts a string to all uppercase letters.",
      samples: [
        {
          sampleInput: ["Hello World!"],
          sampleOutput: '"HELLO WORLD!"',
        },
      ],
    },
  ],
  [
    "gsql_lower",
    {
      description: "Converts a string to all lowercase letters.",
      samples: [
        {
          sampleInput: ["Hello World!"],
          sampleOutput: '"hello world!"',
        },
      ],
    },
  ],
  [
    "gsql_trim",
    {
      description: "Trims whitespace and table characters of both sides of the string.",
      samples: [
        {
          sampleInput: ["  Hello World!  "],
          sampleOutput: '"Hello World!"',
        },
      ],
    },
  ],
  [
    "gsql_ltrim",
    {
      description: "Trims heading whitespace and table characters of the string.",
      samples: [
        {
          sampleInput: ["  Hello World!  "],
          sampleOutput: '"Hello World!"  ',
        },
      ],
    },
  ],
  [
    "gsql_rtrim",
    {
      description: "Trims tailing whitespace and table characters of the string.",
      samples: [
        {
          sampleInput: ["  Hello World!  "],
          sampleOutput: '  "Hello World!"',
        },
      ],
    },
  ],
  [
    "gsql_year",
    {
      description: "Returns 4-digit year from timestamp.",
      samples: [
        {
          sampleInput: ["2010-02-14 15:32:10"],
          sampleOutput: "2010",
        },
      ],
    },
  ],
  [
    "gsql_month",
    {
      description: "Returns month (1-12) from timestamp.",
      samples: [
        {
          sampleInput: ["2010-02-14 15:32:10"],
          sampleOutput: "2",
        },
      ],
    },
  ],
  [
    "gsql_day",
    {
      description: "Returns day (1-31) from timestamp.",
      samples: [
        {
          sampleInput: ["2010-02-14 15:32:10"],
          sampleOutput: "14",
        },
      ],
    },
  ],
  [
    "gsql_year_epoch",
    {
      description: "Returns 4-digit year from epoch.",
      samples: [
        {
          sampleInput: ["1266190330"],
          sampleOutput: "2010",
        },
      ],
    },
  ],
  [
    "gsql_month_epoch",
    {
      description: "Returns month (1-12) from epoch.",
      samples: [
        {
          sampleInput: ["1266190330"],
          sampleOutput: "2",
        },
      ],
    },
  ],
  [
    "gsql_day_epoch",
    {
      description: "Returns day (1-31) from epoch.",
      samples: [
        {
          sampleInput: ["1266190330"],
          sampleOutput: "14",
        },
      ],
    },
  ],
  [
    "gsql_find",
    {
      description:
        "Returns the start index of the first occurrence of the specified substring " +
        "within the input string, or -1 if the substring is not found.",
      samples: [
        {
          sampleInput: ["State:CA", "CA"],
          sampleOutput: "6",
        },
        {
          sampleInput: ["State:AZ", "CA"],
          sampleOutput: "-1",
        },
      ],
    },
  ],
  [
    "gsql_length",
    {
      description: "Returns the length of the input string.",
      samples: [
        {
          sampleInput: ["Kevin"],
          sampleOutput: "5",
        },
      ],
    },
  ],
  [
    "gsql_replace",
    {
      description:
        "Returns the resulting string when the specified max number of occurrences of the " +
        "specified old token in the input string are replaced with the specified new token.",
      samples: [
        {
          sampleInput: ["TigergraphDB is a graph-based DBMS.", "graph", "Graph", "1"],
          sampleOutput: '"TigerGraphDB is a graph-based DBMS."',
        },
      ],
    },
  ],
  [
    "gsql_regex_match",
    {
      description:
        "Returns true if the given string token matches the given regular expression token " +
        "and false otherwise.",
      samples: [
        {
          sampleInput: ["Kevin", "[A-Z][a-z]+"],
          sampleOutput: "true",
        },
        {
          sampleInput: ["Kevin", "[0-9]+"],
          sampleOutput: "false",
        },
      ],
    },
  ],
  [
    "gsql_regex_replace",
    {
      description:
        "Returns the resulting string by replacing all substrings in the input string " +
        "that match the given regular expression token with the given substitute string.",
      samples: [
        {
          sampleInput: ["1,12,123,1234", "[0-9]+", "number"],
          sampleOutput: '"number,number,number,number"',
        },
      ],
    },
  ],
  [
    "gsql_substring",
    {
      description:
        "Returns a substring of the input string starting at the specified index " +
        "with the specified length.",
      samples: [
        {
          sampleInput: ["State:CA", "6", "2"],
          sampleOutput: '"CA"',
        },
      ],
    },
  ],
  [
    "gsql_uuid_v4",
    {
      description: "Generates a random Version 4 UUID and returns it.",
      samples: [
        {
          sampleInput: [""],
          sampleOutput: '"4493d5ce-a69b-4c90-88e4-b41e9f576169"',
        },
      ],
    },
  ],
]);

/**
 * Loading mapping of one column.
 * - sourceType: one column from data source | token function | default | literal
 * - index: used if sourceType is one column from data source or token function
 * - literal: used if sourceType is literal
 *
 * @export
 * @interface OneColumnMapping
 */
export interface OneColumnMapping {
  sourceType: SourceType;
  index?: number;
  literal?: string;
}

/**
 * Loading mapping related logic.
 *
 * @export
 * @class LoadingMappingLogic
 */
export class LoadingMappingLogic {
  /**
   * Check that the one column mapping is consisted by supported mapping types:
   * - SrcColIndex, Literal, Default, UDF
   *
   * @static
   * @param {GSQLLoadingAstJson} oneColumnMapping
   * @returns {{
   *     success: boolean,
   *     message?: string
   *   }}
   * @memberof LoadingMappingLogic
   */
  static validationCheck(oneColumnMapping: GSQLLoadingAstJson): {
    success: boolean;
    message?: string;
  } {
    // 1. Not supported type
    if (!supportedLoadingMappingTypes.includes(oneColumnMapping.Type)) {
      return {
        success: false,
        message: `"${oneColumnMapping.Type}" is not supported in Graph Studio.`,
      };
    }

    // 2. Supported types but contains advanced information
    for (const propName of Object.keys(oneColumnMapping)) {
      if (!SUPPORTED_PROPNAMES.includes(propName)) {
        return {
          success: false,
          message: `"${propName}" is not supported in Graph Studio.`,
        };
      }
    }

    // 3. Token function recursively check the parameter
    if (oneColumnMapping.Type === "UDF") {
      const parameters = (<GSQLUDFJson>oneColumnMapping).Params;
      for (const param of parameters) {
        const validationCheckParam = LoadingMappingLogic.validationCheck(param);
        if (!validationCheckParam.success) {
          return validationCheckParam;
        }
      }
    }

    // 4. Map recursively check the parameter
    if (oneColumnMapping.Type === "KvPair") {
      const kvPair = <GSQLKVPairJson>oneColumnMapping;
      for (const param of [kvPair.Key, kvPair.Value]) {
        const validationCheckParam = LoadingMappingLogic.validationCheck(param);
        if (!validationCheckParam.success) {
          return validationCheckParam;
        }
      }
    }

    // 5. UDT recursively check the parameter
    if (oneColumnMapping.Type === "UDT") {
      const parameters = (<GSQLUDTMappingJson>oneColumnMapping).Fields;
      for (const param of parameters) {
        const validationCheckParam = LoadingMappingLogic.validationCheck(param);
        if (!validationCheckParam.success) {
          return validationCheckParam;
        }
      }
    }

    // success
    return {
      success: true,
    };
  }

  /**
   * Recursively build the loading mapping.
   * Depending on mapping type, build the mapping into the mapping container.
   * Also, flatten the token functions into the token function container in DFS order.
   * The token function order will be used as index for the mappings input from the token functions.
   *
   * @static
   * @param {GSQLLoadingAstJson} oneColumnMappingAst the mapping target in GSQL loading ast format
   * @param {OneColumnMapping[]} mappingContainer the container to hold result in-memory mappings
   * @param {((TokenFunction | MapWidget | TupleWidget)[])} mappingWidgetContainer the container
   * to hold UDFs in DFS order
   * @param {DataFieldSchema[]} dataSchema the mapping source data schema
   * @returns
   * @memberof LoadToVertexLogic
   */
  static loadMappingFromGSQLMappingJson(
    oneColumnMappingAst: GSQLLoadingAstJson,
    mappingContainer: OneColumnMapping[],
    mappingWidgetContainer: (TokenFunction | MapWidget | TupleWidget)[],
    dataSchema: DataFieldSchema[],
    mappingWidgetIndices: DBMappingWidgetIndex[] = []
  ) {
    switch (oneColumnMappingAst.Type) {
      // Put one default mapping
      case "Default": {
        mappingContainer.push({
          sourceType: SourceType.Default,
        });
        return;
      }
      // Put one literal mapping
      case "Literal": {
        const gsqlLiteral = <GSQLLiteralJson>oneColumnMappingAst;
        mappingContainer.push({
          sourceType: SourceType.Literal,
          literal: gsqlLiteral.Value,
        });
        return;
      }
      // Put one data source column index mapping
      case "SrcColIndex":
        const gsqlSrcColIndex = <GSQLSrcColIndexJson>oneColumnMappingAst;
        mappingContainer.push({
          sourceType: SourceType.DataSourceColumn,
          index: gsqlSrcColIndex.Value,
        });
        break;
      case "SrcColName":
        const gsqlSrcColName = <GSQLSrcColNameJson>oneColumnMappingAst;
        /*
          Sample input
          gsqlSrcColName: GSQLSrcColNameJson = {
            Type: 'SrcColName',
            Value: 'a',
            SrcKeyNames: [
              'a',
              'b'
            ],
          }
          dataSchema: DataFieldSchema[] = [
            {
              name: 'a',
              type: JSONDataType.Object,
              level: 0,
              path: [
                'a'
              ]
            },
            {
              name: 'b',
              type: JSONDataType.String,
              level: 1,
              path: [
                'a',
                'b'
              ]
            },
          ]

          Sample output
          index = 1
         */
        mappingContainer.push({
          sourceType: SourceType.DataSourceColumn,
          index: dataSchema
            .map((dataFieldSchema) => JSON.stringify((<JSONDataFieldSchema>dataFieldSchema).path))
            .indexOf(JSON.stringify(gsqlSrcColName.SrcKeyNames)),
        });
        break;
      // Put one UDF mapping
      case "UDF": {
        const gsqlTokenFunction = <GSQLUDFJson>oneColumnMappingAst;
        // Create the token function
        const tokenFunction: TokenFunction = {
          funcName: gsqlTokenFunction.UdfName,
          params: [],
        };

        // Recursively build the token function's parameters mapping
        for (const oneColumnMapping of gsqlTokenFunction.Params) {
          LoadingMappingLogic.loadMappingFromGSQLMappingJson(
            oneColumnMapping,
            tokenFunction.params,
            mappingWidgetContainer,
            dataSchema,
            mappingWidgetIndices
          );
        }

        // Add the token function into the mapping widget container
        if (mappingWidgetIndices.length > 0) {
          const { index, name } = mappingWidgetIndices.shift();
          tokenFunction.name = name;
          // Add the mapping into the mappingContainer
          mappingContainer.push({
            sourceType: SourceType.MappingWidget,
            index: index,
          });
          while (index >= mappingWidgetContainer.length) {
            mappingWidgetContainer.push({ ...tokenFunction });
          }
          mappingWidgetContainer[index] = tokenFunction;
        } else {
          mappingWidgetContainer.push(tokenFunction);
          // Add the mapping into the mappingContainer
          mappingContainer.push({
            sourceType: SourceType.MappingWidget,
            index: mappingWidgetContainer.length - 1,
          });
        }

        return;
      }
      // Put one Map mapping
      case "KvPair": {
        const gsqlKVPair = <GSQLKVPairJson>oneColumnMappingAst;
        // Create the Map widget. Here the keyType and valType are not assigned.
        // They will be assigned from the vertex or edge attributes directly.
        const mapWidget: MapWidget = {
          keyType: undefined,
          valType: undefined,
          udtType: undefined,
          params: [],
        };

        // Build the map's key and value mapping
        LoadingMappingLogic.loadMappingFromGSQLMappingJson(
          gsqlKVPair.Key,
          mapWidget.params,
          mappingWidgetContainer,
          dataSchema,
          mappingWidgetIndices
        );
        LoadingMappingLogic.loadMappingFromGSQLMappingJson(
          gsqlKVPair.Value,
          mapWidget.params,
          mappingWidgetContainer,
          dataSchema,
          mappingWidgetIndices
        );

        // Add the Map widget into the mapping widget container
        mappingWidgetContainer.push(mapWidget);

        // Add the Map widget into the mappingContainer
        mappingContainer.push({
          sourceType: SourceType.MappingWidget,
          index: mappingWidgetContainer.length - 1,
        });

        return;
      }
      // Put one UDT mapping
      case "UDT": {
        const gsqlUDT = <GSQLUDTMappingJson>oneColumnMappingAst;
        // Create the Tuple widget. Here the fieldTypes is not assigned.
        // It will be filled in from the UDT list.
        const tupleWidget: TupleWidget = {
          udtName: gsqlUDT.UdtName,
          udt: undefined,
          params: [],
        };

        // Recursively build the map's parameters mapping
        for (const oneColumnMapping of gsqlUDT.Fields) {
          LoadingMappingLogic.loadMappingFromGSQLMappingJson(
            oneColumnMapping,
            tupleWidget.params,
            mappingWidgetContainer,
            dataSchema,
            mappingWidgetIndices
          );
        }

        // Add the Tuple widget into the mapping widget container
        mappingWidgetContainer.push(tupleWidget);

        // Add the UDT widget into the mappingContainer
        mappingContainer.push({
          sourceType: SourceType.MappingWidget,
          index: mappingWidgetContainer.length - 1,
        });

        return;
      }
    }
  }

  /**
   * Dump in-memory loading mapping to GSQL loading mapping.
   *
   * @static
   * @param {GSQLLoadingAstJson[]} gsqlMappingsContainer
   * @param {OneColumnMapping} oneMapping
   * @param {((TokenFunction | MapWidget | TupleWidget)[])} mappingWidgets
   * @param {DBLoadingStatementStyle} loadingJobStyle
   * @param {DBPositionStyle[]} middlePositionsContainer
   * @param {number[]} mappingWidgetIndices
   * @param {DataFormat} dataFormat the mapping source data format
   * @param {DataFieldSchema[]} dataSchema the mapping source data schema
   * @memberof LoadingMappingLogic
   */
  static dumpToGSQLLoadingMapping(
    gsqlMappingsContainer: GSQLLoadingAstJson[],
    oneMapping: OneColumnMapping,
    mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[],
    loadingJobStyle: DBLoadingStatementStyle,
    middlePositionsContainer: DBPositionStyle[],
    mappingWidgetIndices: DBMappingWidgetIndex[],
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[]
  ) {
    switch (oneMapping.sourceType) {
      // Default
      case SourceType.Default: {
        const gsqlOneMapping: GSQLDefaultJson = {
          Type: "Default",
        };
        gsqlMappingsContainer.push(gsqlOneMapping);
        break;
      }
      // Literal
      case SourceType.Literal: {
        const gsqlOneMapping: GSQLLiteralJson = {
          Type: "Literal",
          Value: oneMapping.literal,
        };
        gsqlMappingsContainer.push(gsqlOneMapping);
        break;
      }
      // DataSourceColumn
      case SourceType.DataSourceColumn: {
        let gsqlOneMapping: GSQLSrcColIndexJson | GSQLSrcColNameJson;
        switch (dataFormat) {
          case DataFormat.CSV:
          case DataFormat.TSV:
            gsqlOneMapping = {
              Type: "SrcColIndex",
              Value: oneMapping.index,
            };
            break;
          case DataFormat.JSON:
            const jsonSchema = <JSONDataFieldSchema>dataSchema[oneMapping.index];
            gsqlOneMapping = {
              Type: "SrcColName",
              Value: jsonSchema.path[0],
              SrcKeyNames: jsonSchema.path,
            };
            break;
          default:
            // TODO: Support more data formats in the future.
            break;
        }
        gsqlMappingsContainer.push(gsqlOneMapping);
        break;
      }
      // UDF
      case SourceType.MappingWidget: {
        if ("funcName" in mappingWidgets[oneMapping.index]) {
          // Token function.
          const gsqlOneMapping: GSQLUDFJson = {
            Type: "UDF",
            UdfName: (mappingWidgets[oneMapping.index] as TokenFunction).funcName,
            Params: [],
          };
          mappingWidgets[oneMapping.index].params.forEach((param) => {
            LoadingMappingLogic.dumpToGSQLLoadingMapping(
              gsqlOneMapping.Params,
              param,
              mappingWidgets,
              loadingJobStyle,
              middlePositionsContainer,
              mappingWidgetIndices,
              dataFormat,
              dataSchema
            );
          });
          // Collect the token function position in DFS order
          middlePositionsContainer.push(loadingJobStyle.middlePositions[oneMapping.index]);
          mappingWidgetIndices.push({
            index: oneMapping.index,
            name: mappingWidgets[oneMapping.index].name || '',
          });
          gsqlMappingsContainer.push(gsqlOneMapping);
        } else if ("udtName" in mappingWidgets[oneMapping.index]) {
          // Tuple
          const gsqlOneMapping: GSQLUDTMappingJson = {
            Type: "UDT",
            UdtName: (mappingWidgets[oneMapping.index] as TupleWidget).udtName,
            Fields: [],
          };
          mappingWidgets[oneMapping.index].params.forEach((param) => {
            LoadingMappingLogic.dumpToGSQLLoadingMapping(
              gsqlOneMapping.Fields,
              param,
              mappingWidgets,
              loadingJobStyle,
              middlePositionsContainer,
              mappingWidgetIndices,
              dataFormat,
              dataSchema
            );
          });
          // Collect the Tuple widget position in DFS order
          middlePositionsContainer.push(loadingJobStyle.middlePositions[oneMapping.index]);
          mappingWidgetIndices.push({
            index: oneMapping.index,
            name: mappingWidgets[oneMapping.index].name || '',
          });
          gsqlMappingsContainer.push(gsqlOneMapping);
        } else if ("keyType" in mappingWidgets[oneMapping.index]) {
          const gsqlOneMapping: GSQLKVPairJson = {
            Type: "KvPair",
            Key: undefined,
            Value: undefined,
          };
          const tempContainer: GSQLLoadingAstJson[] = [];
          mappingWidgets[oneMapping.index].params.forEach((param) => {
            LoadingMappingLogic.dumpToGSQLLoadingMapping(
              tempContainer,
              param,
              mappingWidgets,
              loadingJobStyle,
              middlePositionsContainer,
              mappingWidgetIndices,
              dataFormat,
              dataSchema
            );
          });
          // Collect the Map widget position in DFS order
          middlePositionsContainer.push(loadingJobStyle.middlePositions[oneMapping.index]);
          mappingWidgetIndices.push({
            index: oneMapping.index,
            name: mappingWidgets[oneMapping.index].name || '',
          });
          gsqlOneMapping.Key = tempContainer[0];
          gsqlOneMapping.Value = tempContainer[1];
          gsqlMappingsContainer.push(gsqlOneMapping);
        }
        break;
      }
    }
  }

  /**
   * Map from one column in data source to one column in vertex | edge.
   * Assumptions:
   * 1. srcColIndex is within data source column array boundary
   * 2. mappingsIndex is within vertex | edge mapping array index boundary
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @param {number} srcColIndex
   * @param {number} mappingsIndex
   * @param {Graph} schema
   * @memberof LoadingMappingLogic
   */
  static mapDataSourceToEntity(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
    srcColIndex: number,
    mappingsIndex: number,
    schema: Graph
  ) {
    switch (dataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        let vertexOrEdgeType: string;
        let graphEntityType: string;
        let attrName: string;
        let attrType: string;
        let attrValueType: string;

        if ("vertexName" in loadToEntity && mappingsIndex > 0) {
          const vertex = schema.getVertex(loadToEntity.vertexName);
          vertexOrEdgeType = vertex.name;
          graphEntityType = "Vertex";
          attrName = vertex.attributes[mappingsIndex - 1].name;
          attrType = vertex.attributes[mappingsIndex - 1].type.name;
          attrValueType = vertex.attributes[mappingsIndex - 1].type.getValueType();
        } else if ("edgeName" in loadToEntity && mappingsIndex > 1) {
          const edge = schema.getEdge(loadToEntity.edgeName);
          vertexOrEdgeType = edge.name;
          graphEntityType = "Edge";
          attrName = edge.attributes[mappingsIndex - 2].name;
          attrType = edge.attributes[mappingsIndex - 2].type.name;
          attrValueType = edge.attributes[mappingsIndex - 2].type.getValueType();
        }

        // Blocks map data to MAP and UDT type attributes or attributes included UDT type value.
        if (attrType && ["MAP", "UDT"].includes(attrType)) {
          throw new GraphStudioError(
            `${graphEntityType} "${vertexOrEdgeType}"'s attribute ` +
              `"${attrName}" expects ` +
              `a(n) ${attrValueType},` +
              ` the given input should be from a ${attrValueType} widget.`
          );
        }
        break;
      case DataFormat.JSON:
        const jsonDatatype: JSONDataType = (<JSONDataFieldSchema[]>dataSchema)[srcColIndex].type;
        if ("vertexName" in loadToEntity) {
          // Load to vertex
          const vertex = schema.getVertex((<LoadToVertexData>loadToEntity).vertexName);
          if (
            mappingsIndex === 0 &&
            !this.jsonAndGSQLTypeMatchCheck(jsonDatatype, vertex.primaryId.type.getValueType())
          ) {
            // primary id data type not match
            throw new GraphStudioError(
              this.updateDataSourceToEntityMappingErrorMessage(
                jsonDatatype,
                `Vertex "${vertex.name}"'s primary id expects ` +
                  `a(n) ${vertex.primaryId.type.getValueType()},` +
                  ` but the given input type is ${jsonDatatype}.`
              )
            );
          } else if (
            mappingsIndex > 0 &&
            !this.jsonAndGSQLTypeMatchCheck(
              jsonDatatype,
              vertex.attributes[mappingsIndex - 1].type.getValueType()
            )
          ) {
            // attribute data type not match
            throw new GraphStudioError(
              this.updateDataSourceToEntityMappingErrorMessage(
                jsonDatatype,
                `Vertex "${vertex.name}"'s attribute ` +
                  `"${vertex.attributes[mappingsIndex - 1].name}" expects ` +
                  `a(n) ${vertex.attributes[mappingsIndex - 1].type.getValueType()},` +
                  ` but the given input type is ${jsonDatatype}.`
              )
            );
          }
        } /* if ('edgeName' in loadToEntity) */ else {
          // Load to edge
          const edge = schema.getEdge((<LoadToEdgeData>loadToEntity).edgeName);
          const fromVertex = schema.getVertex((<LoadToEdgeData>loadToEntity).fromVertexType);
          const toVertex = schema.getVertex((<LoadToEdgeData>loadToEntity).toVertexType);
          if (
            mappingsIndex === 0 &&
            !this.jsonAndGSQLTypeMatchCheck(jsonDatatype, fromVertex.primaryId.type.getValueType())
          ) {
            // source vertex primary id not match
            throw new GraphStudioError(
              this.updateDataSourceToEntityMappingErrorMessage(
                jsonDatatype,
                `Edge "${edge.name}"'s source vertex "${fromVertex.name}"'s primary id ` +
                  `expects a(n) ${fromVertex.primaryId.type.getValueType()},` +
                  ` but the given input type is ${jsonDatatype}.`
              )
            );
          } else if (
            mappingsIndex === 1 &&
            !this.jsonAndGSQLTypeMatchCheck(jsonDatatype, toVertex.primaryId.type.getValueType())
          ) {
            // target vertex primary id not match
            throw new GraphStudioError(
              this.updateDataSourceToEntityMappingErrorMessage(
                jsonDatatype,
                `Edge "${edge.name}"'s target vertex "${toVertex.name}"'s ` +
                  `primary id expects a(n) ${toVertex.primaryId.type.getValueType()},` +
                  ` but the given input type is ${jsonDatatype}.`
              )
            );
          } else if (
            mappingsIndex > 1 &&
            !this.jsonAndGSQLTypeMatchCheck(
              jsonDatatype,
              edge.attributes[mappingsIndex - 2].type.getValueType()
            )
          ) {
            // attribute data type not match
            throw new GraphStudioError(
              this.updateDataSourceToEntityMappingErrorMessage(
                jsonDatatype,
                `Edge "${edge.name}"'s attribute ` +
                  `"${edge.attributes[mappingsIndex - 2].name}" expects ` +
                  `a(n) ${edge.attributes[mappingsIndex - 2].type.getValueType()}` +
                  ` but the given input type is ${jsonDatatype}.`
              )
            );
          }
        }
        break;
    }

    loadToEntity.mappings[mappingsIndex] = {
      sourceType: SourceType.DataSourceColumn,
      index: srcColIndex,
    };
  }

  /**
   * Check if the json input data type matches the GSQL data type in graph entity.
   *
   * @static
   * @param {JSONDataType} jsonType
   * @param {string} gsqlType
   * @returns {boolean}
   * @memberof LoadingMappingLogic
   */
  static jsonAndGSQLTypeMatchCheck(jsonType: JSONDataType, gsqlType: string): boolean {
    switch (jsonType) {
      case JSONDataType.String:
        return gsqlType === "STRING" || gsqlType === "STRING COMPRESS" || gsqlType === "DATETIME";
      case JSONDataType.Number:
      case JSONDataType.Integer:
        return (
          gsqlType === "FLOAT" ||
          gsqlType === "DOUBLE" ||
          gsqlType === "INT" ||
          gsqlType === "UINT" ||
          gsqlType === "STRING"
        );
      case JSONDataType.Boolean:
        return gsqlType === "BOOL";
      default:
        return false;
    }
  }

  /**
   * Return error message based on json data type.
   *
   * @static
   * @private
   * @param {JSONDataType} jsonType
   * @param {string} msg
   * @returns {string}
   * @memberof LoadingMappingLogic
   */
  private static updateDataSourceToEntityMappingErrorMessage(
    jsonType: JSONDataType,
    msg: string
  ): string {
    switch (jsonType) {
      case JSONDataType.String:
      case JSONDataType.Number:
      case JSONDataType.Integer:
      case JSONDataType.Boolean:
        return msg;
      case JSONDataType.Array:
        return (
          `Data mapping from a JSON array to a graph entity ` +
          `attribute is currently not supported in TigerGraph.`
        );
      case JSONDataType.Object:
        return (
          `Data mapping directly from a JSON object to a graph entity attribute is not allowed.` +
          ` Please map from its descendant primitive (STRING, NUMBER, BOOLEAN, etc) properties instead.`
        );
      case JSONDataType.Null:
        return `Data mapping from a JSON null to a graph entity attribute is not allowed.`;
    }
  }

  /**
   * Map from one column in data source to one input param in mapping widget.
   * Assumptions:
   * 1. srcColIndex is within data source column array boundary
   * 2. mappingWidgetIndex is within tokenFunctions array index boundary
   * 3. mappingWidgetParamIndex is within the token function's int param array boundary
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @param {number} srcColIndex  column index of data source
   * @param {number} mappingWidgetIndex mapping widget index in token function array
   * @param {number} mappingWidgetParamIndex mapping target index in mapping widget params
   * @memberof LoadingMappingLogic
   */
  static mapDataSourceToMappingWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
    srcColIndex: number,
    mappingWidgetIndex: number,
    mappingWidgetParamIndex: number
  ) {
    const mappingWidget = loadToEntity.mappingWidgets[mappingWidgetIndex];

    // Cannot map data source column directly to Map's UDT value.
    if (
      "valType" in mappingWidget &&
      mappingWidget.valType === "UDT" &&
      mappingWidgetParamIndex === 1
    ) {
      throw new GraphStudioError(
        `Cannot directly map a column of the data source ` +
          `to a UDT value. Please create a UDT widget first.`
      );
    }

    if (dataFormat === DataFormat.JSON) {
      const jsonDatatype: JSONDataType = (<JSONDataFieldSchema[]>dataSchema)[srcColIndex].type;
      switch (jsonDatatype) {
        case JSONDataType.Array:
          throw new GraphStudioError(
            `Data mapping from a JSON array to a token function input ` +
              `is currently not supported in TigerGraph.`
          );
        case JSONDataType.Object:
          throw new GraphStudioError(
            `Data mapping directly from a JSON object to a token function input is not allowed.` +
              ` Please map from its descendant primitive (STRING, NUMBER, BOOLEAN) properties instead.`
          );
        case JSONDataType.Null:
          throw new GraphStudioError(
            `Data mapping from a JSON null to to a token function input is not allowed.`
          );
        default:
          break;
      }
    }

    mappingWidget.params[mappingWidgetParamIndex] = {
      sourceType: SourceType.DataSourceColumn,
      index: srcColIndex,
    };
  }

  /**
   * Get token function by name.
   *
   * @memberof LoadingMappingLogic
   */
  static getTokenFunction(name: string): TokenFunctionSignature {
    return combinedTokenFunctionList.find((func) => func.name === name);
  }

  /**
   * Map from one mapping widget output to one column in vertex | edge.
   * Assumptions:
   * 1. mappingWidgetIndex is within mappingWidgets array index boundary
   * 2. mappingsIndex is within vertex | edge mapping array index boundary
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingWidgetIndex
   * @param {number} mappingsIndex
   * @param {Graph} schema
   * @memberof LoadingMappingLogic
   */
  static mapMappingWidgetToEntity(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingWidgetIndex: number,
    mappingsIndex: number,
    schema: Graph
  ) {
    // Save the original mapping
    const originalMapping = loadToEntity.mappings[mappingsIndex];
    // Update to the new mapping
    loadToEntity.mappings[mappingsIndex] = {
      sourceType: SourceType.MappingWidget,
      index: mappingWidgetIndex,
    };

    const mappingWidget = loadToEntity.mappingWidgets[mappingWidgetIndex];

    let widgetOutputTypes: string[] = [];
    let widgetTypeMessage: string;

    if ("funcName" in mappingWidget) {
      // Token function
      // Check if the token function exists.
      const tokenFunctionName = mappingWidget.funcName;
      const tokenFunction = this.getTokenFunction(tokenFunctionName);
      if (tokenFunction === undefined) {
        throw new GraphStudioError(
          `Token function ${tokenFunctionName} ` +
            `does not exist, maybe it has been removed from GSQL. Please remove it ` +
            `from Data Mapping and use other token functions.`
        );
      }
      widgetOutputTypes = tokenFunction.returnType;
      widgetTypeMessage = `but the token function's output type is ${tokenFunction.returnType[0]}`;
    } else if ("udtName" in mappingWidget) {
      // UDT
      widgetOutputTypes = [mappingWidget.udtName];
      widgetTypeMessage = `not a UDT ${mappingWidget.udtName}`;
    } /* if ('keyType' in mappingWidget) */ else {
      // Map
      widgetOutputTypes = [
        `MAP<${mappingWidget.keyType}, ${
          mappingWidget.valType === "UDT" ? mappingWidget.udtType : mappingWidget.valType
        }>`,
      ];
      widgetTypeMessage = `not a ${widgetOutputTypes[0]}`;
    }

    // If the mapping widget's output type doesn't match target expected type, rollback
    // to original mapping, and hint user the issue
    if ("vertexName" in loadToEntity) {
      // Load to vertex
      const vertex = schema.getVertex((<LoadToVertexData>loadToEntity).vertexName);
      if (mappingsIndex === 0 && !widgetOutputTypes.includes(vertex.primaryId.type.name)) {
        // primary id data type not match
        loadToEntity.mappings[mappingsIndex] = originalMapping;
        throw new GraphStudioError(
          `Vertex "${vertex.name}"'s primary id expects ` +
            `a(n) ${vertex.primaryId.type.getValueType()}, ${widgetTypeMessage}.`
        );
      } else if (
        mappingsIndex > 0 &&
        !widgetOutputTypes.includes(vertex.attributes[mappingsIndex - 1].type.getValueType())
      ) {
        // attribute data type not match
        loadToEntity.mappings[mappingsIndex] = originalMapping;
        throw new GraphStudioError(
          `Vertex "${vertex.name}"'s attribute ` +
            `"${vertex.attributes[mappingsIndex - 1].name}" expects ` +
            `a(n) ${vertex.attributes[
              mappingsIndex - 1
            ].type.getValueType()}, ${widgetTypeMessage}.`
        );
      }
    } /* if ('edgeName' in loadToEntity) */ else {
      // Load to edge
      const edge = schema.getEdge((<LoadToEdgeData>loadToEntity).edgeName);
      const fromVertex = schema.getVertex((<LoadToEdgeData>loadToEntity).fromVertexType);
      const toVertex = schema.getVertex((<LoadToEdgeData>loadToEntity).toVertexType);
      if (
        mappingsIndex === 0 &&
        !widgetOutputTypes.includes(fromVertex.primaryId.type.getValueType())
      ) {
        // source vertex primary id not match
        loadToEntity.mappings[mappingsIndex] = originalMapping;
        throw new GraphStudioError(
          `Edge "${edge.name}"'s source vertex "${fromVertex.name}"'s primary id ` +
            `expects a(n) ${fromVertex.primaryId.type.getValueType()}, ${widgetTypeMessage}.`
        );
      } else if (
        mappingsIndex === 1 &&
        !widgetOutputTypes.includes(toVertex.primaryId.type.getValueType())
      ) {
        // target vertex primary id not match
        loadToEntity.mappings[mappingsIndex] = originalMapping;
        throw new GraphStudioError(
          `Edge "${edge.name}"'s target vertex "${toVertex.name}"'s ` +
            `primary id expects a(n) ${toVertex.primaryId.type.getValueType()}, ${widgetTypeMessage}.`
        );
      } else if (
        mappingsIndex > 1 &&
        !widgetOutputTypes.includes(edge.attributes[mappingsIndex - 2].type.getValueType())
      ) {
        // attribute data type not match
        loadToEntity.mappings[mappingsIndex] = originalMapping;
        throw new GraphStudioError(
          `Edge "${edge.name}"'s attribute ` +
            `"${edge.attributes[mappingsIndex - 2].name}" expects ` +
            `a(n) ${edge.attributes[mappingsIndex - 2].type.getValueType()}, ${widgetTypeMessage}.`
        );
      }
    }
  }

  /**
   * Map from one mapping widget output to one input param in another mapping widget.
   * Assumptions:
   * 1. sourceMappingWidgetIndex is within mappingWidgets array index boundary
   * 2. targetMappingWidgetIndex is within mappingWidgets array index boundary
   * 3. targetMappingWidgetParamIndex is within the mapping widget's param array boundary
   *
   * If there is circular mapping widget reference, will raise error and rollback to old mapping.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} sourceMappingWidgetIndex
   * @param {number} targetMappingWidgetIndex
   * @param {number} targetMappingWidgetParamIndex
   * @memberof LoadingMappingLogic
   */
  static mapMappingWidgetToMappingWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    sourceMappingWidgetIndex: number,
    targetMappingWidgetIndex: number,
    targetMappingWidgetParamIndex: number
  ) {
    const sourceMappingWidget = loadToEntity.mappingWidgets[sourceMappingWidgetIndex];
    const targetMappingWidget = loadToEntity.mappingWidgets[targetMappingWidgetIndex];

    let sourceWidgetOutputTypes: string[] = [];
    let sourceWidgetTypeMessage: string;
    let targetWidgetInputType: string;
    let targetWidgetTypeMessage: string;

    // Set source mapping widget output type and type message.
    if ("funcName" in sourceMappingWidget) {
      // Source is a token function.
      // Check if source widget token function exists.
      const tokenFunction = this.getTokenFunction(sourceMappingWidget.funcName);
      if (tokenFunction === undefined) {
        throw new GraphStudioError(
          `Token function ${sourceMappingWidget.funcName} ` +
            `does not exist, maybe it has been removed from GSQL. Please remove it ` +
            `from Data Mapping and use other token functions.`
        );
      }
      sourceWidgetOutputTypes = tokenFunction.returnType;
      sourceWidgetTypeMessage = `token function "${tokenFunction.name}" with return type ${sourceWidgetOutputTypes[0]}`;
    } else if ("udtName" in sourceMappingWidget) {
      // Source is a UDT.
      sourceWidgetOutputTypes = [sourceMappingWidget.udtName];
      sourceWidgetTypeMessage = `UDT ${sourceMappingWidget.udtName}`;
    } /* if ('keyType' in sourceMappingWidget) */ else {
      // Source is a Map.
      sourceWidgetOutputTypes = [
        `MAP<${sourceMappingWidget.keyType}, ${
          sourceMappingWidget.valType === "UDT"
            ? sourceMappingWidget.udtType
            : sourceMappingWidget.valType
        }>`,
      ];
      sourceWidgetTypeMessage = sourceWidgetOutputTypes[0];
    }

    // Set target mapping widget input type and type message.
    if ("funcName" in targetMappingWidget) {
      // Target is a token function.
      targetWidgetInputType = "STRING";
      targetWidgetTypeMessage = `a token function's input which accept a STRING`;
    } else if ("udtName" in targetMappingWidget) {
      // Target is a UDT.
      targetWidgetInputType =
        targetMappingWidget.udt.fields[targetMappingWidgetParamIndex].fieldType;
      targetWidgetTypeMessage = `a ${targetWidgetInputType} type UDT field`;
    } /* if ('keyType' in sourceMappingWidget) */ else {
      // Target is a MAP.
      if (targetMappingWidgetParamIndex === 0) {
        // Map's key
        targetWidgetInputType = targetMappingWidget.keyType;
        targetWidgetTypeMessage = `a map's key with ${targetWidgetInputType} type`;
      } /* if (targetMappingWidgetParamIndex === 1) */ else {
        // Map's val
        targetWidgetInputType =
          targetMappingWidget.valType === "UDT"
            ? targetMappingWidget.udtType
            : targetMappingWidget.valType;
        targetWidgetTypeMessage = `a map's value with ${targetWidgetInputType} type`;
      }
    }

    // If the source output type doesn't match the target input type, report error.
    if (!sourceWidgetOutputTypes.includes(targetWidgetInputType)) {
      throw new GraphStudioError(
        `Cannot map ${sourceWidgetTypeMessage} to ${targetWidgetTypeMessage}.`
      );
    }

    // Save the original mapping
    const originalMapping =
      loadToEntity.mappingWidgets[targetMappingWidgetIndex].params[targetMappingWidgetParamIndex];
    // Update to the new mapping
    loadToEntity.mappingWidgets[targetMappingWidgetIndex].params[targetMappingWidgetParamIndex] = {
      sourceType: SourceType.MappingWidget,
      index: sourceMappingWidgetIndex,
    };
    // If mapping widget params have circle reference(s), rollback to original mapping, and
    // hint user the issue.
    if (
      LoadingMappingLogic.mappingWidgetsHasCircularReference(
        loadToEntity.mappingWidgets,
        loadToEntity.mappingWidgets[targetMappingWidgetIndex].params[targetMappingWidgetParamIndex],
        new Set<number>()
      )
    ) {
      loadToEntity.mappingWidgets[targetMappingWidgetIndex].params[targetMappingWidgetParamIndex] =
        originalMapping;
      throw new GraphStudioError(
        "Token functions, Map widgets and UDT widgets " +
          "cannot have circular references between each other."
      );
    }
  }

  /**
   * Remove mappings of given indices. Reset them to be default mapping.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {(OneColumnMappingGraphEntityTarget | OneColumnMappingMappingWidgetTarget)[]} mappingIndicesToBeDeleted
   * @memberof LoadingMappingLogic
   */
  static removeLoadingMappings(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingIndicesToBeDeleted: (
      | OneColumnMappingGraphEntityTarget
      | OneColumnMappingMappingWidgetTarget
    )[]
  ) {
    mappingIndicesToBeDeleted.forEach((mappingIndex) => {
      if (mappingIndex.type === OneColumnMappingTargetType.GraphEntity) {
        // Remove the mapping from graph entity
        LoadingMappingLogic.mapDefaultToEntity(
          loadToEntity,
          (<OneColumnMappingGraphEntityTarget>mappingIndex).mappingIndex
        );
      } /* if (mappingIndex.type === OneColumnMappingTargetType.TokenFunction) */ else {
        // Remove the mapping from token function paramter
        LoadingMappingLogic.mapDefaultToMappingWidget(
          loadToEntity,
          (<OneColumnMappingMappingWidgetTarget>mappingIndex).mappingWidgetIndex,
          (<OneColumnMappingMappingWidgetTarget>mappingIndex).paramIndex
        );
      }
    });
  }

  /**
   * Check if mapping widgets have circular references.
   *
   * @private
   * @static
   * @param {((TokenFunction | MapWidget | TupleWidget)[])} mappingWidgets
   * @param {OneColumnMapping} oneColumnMapping
   * @param {Set<number>} mappingWidgetIndices
   * @returns {boolean}
   * @memberof LoadingMappingLogic
   */
  private static mappingWidgetsHasCircularReference(
    mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[],
    oneColumnMapping: OneColumnMapping,
    mappingWidgetIndices: Set<number>
  ): boolean {
    // Refer back to an already on-traverse-path token function
    if (mappingWidgetIndices.has(oneColumnMapping.index)) {
      return true;
    }

    mappingWidgetIndices.add(oneColumnMapping.index);
    // Recursively check each token function parameter
    for (let i = 0; i < mappingWidgets[oneColumnMapping.index].params.length; i++) {
      const param = mappingWidgets[oneColumnMapping.index].params[i];
      if (param.sourceType === SourceType.MappingWidget) {
        if (
          LoadingMappingLogic.mappingWidgetsHasCircularReference(
            mappingWidgets,
            param,
            mappingWidgetIndices
          )
        ) {
          return true;
        }
      }
    }
    mappingWidgetIndices.delete(oneColumnMapping.index);

    return false;
  }

  /**
   * Add one more token function to the loading statement.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {string} funcName
   * @param {number} paramNumber
   * @memberof LoadingMappingLogic
   */
  static addTokenFunction(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    funcName: string,
    paramNumber: number
  ) {
    const tokenFunction: TokenFunction = {
      funcName: funcName,
      params: Array(paramNumber)
        .fill(1)
        .map(() => {
          return {
            sourceType: SourceType.Default,
          };
        }),
    };
    loadToEntity.mappingWidgets.push(tokenFunction);
    // Assign a position for new token function
    loadToEntity.style.middlePositions.push(
      LoadingMappingLogic.newMiddlePosition(loadToEntity.style)
    );
  }

  /**
   * Add one more Map widget to the loading statement.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {string} keyType
   * @param {string} valType
   * @param {string} udtType
   * @memberof LoadingMappingLogic
   */
  static addMapWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    keyType: string,
    valType: string,
    udtType: string
  ) {
    const mapWidget: MapWidget = {
      keyType: keyType,
      valType: valType,
      udtType: udtType,
      params: [{ sourceType: SourceType.Default }, { sourceType: SourceType.Default }],
    };
    loadToEntity.mappingWidgets.push(mapWidget);
    // Assign a position for new Map widget.
    loadToEntity.style.middlePositions.push(
      LoadingMappingLogic.newMiddlePosition(loadToEntity.style)
    );
  }

  /**
   * Add one more UDT widget to the loading statement.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {string} udtType
   * @memberof LoadingMappingLogic
   */
  static addTupleWidget(loadToEntity: LoadToVertexData | LoadToEdgeData, gsqlUDT: GSQLUdtJson) {
    const tupleWidget: TupleWidget = {
      udtName: gsqlUDT.name,
      udt: gsqlUDT,
      params: gsqlUDT.fields.map((_) => ({
        sourceType: SourceType.Default,
      })),
    };
    loadToEntity.mappingWidgets.push(tupleWidget);
    // Assign a position for new Tuple widget.
    loadToEntity.style.middlePositions.push(
      LoadingMappingLogic.newMiddlePosition(loadToEntity.style)
    );
  }

  /**
   * Get the position of newly added token function, widget, etc.
   *
   * @private
   * @static
   * @param {DBLoadingStatementStyle} style
   * @returns {DBPositionStyle}
   * @memberof LoadingMappingLogic
   */
  private static newMiddlePosition(style: DBLoadingStatementStyle): DBPositionStyle {
    // Put token function in the middle between data source and loading target.
    const x = (style.sourcePosition.x + style.targetPosition.x) / 2.0;
    let y = (style.sourcePosition.y + style.targetPosition.y) / 2.0;

    // If the token function's position overlaps some other token functions, move it down.
    for (let i = 0; i < style.middlePositions.length * 2; i++) {
      let success = true;
      for (const position of style.middlePositions) {
        if (HelperFunctions.twoPointsNearEachOther(x, y, position.x, position.y, 150)) {
          success = false;
          break;
        }
      }
      if (!success) {
        y += 150;
      } else {
        break;
      }
    }

    return {
      x: x,
      y: y,
    };
  }

  /**
   * Remove one mapping widget from the loading statement.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingWidgetIndex
   * @memberof LoadingMappingLogic
   */
  static removeMappingWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingWidgetIndex: number
  ) {
    // All mappings referring to this token function reset to default
    // All mappings referring to afterward token functions reduce the reference index by 1
    loadToEntity.mappings.forEach((mapping) => {
      if (mapping.sourceType === SourceType.MappingWidget) {
        if (mapping.index === mappingWidgetIndex) {
          mapping.sourceType = SourceType.Default;
        } else if (mapping.index > mappingWidgetIndex) {
          mapping.index--;
        }
      }
    });
    loadToEntity.mappingWidgets.forEach((widget) => {
      widget.params.forEach((param) => {
        if (param.sourceType === SourceType.MappingWidget) {
          if (param.index === mappingWidgetIndex) {
            param.sourceType = SourceType.Default;
          } else if (param.index > mappingWidgetIndex) {
            param.index--;
          }
        }
      });
    });
    // Remove the token function
    loadToEntity.mappingWidgets.splice(mappingWidgetIndex, 1);
    loadToEntity.style.middlePositions.splice(mappingWidgetIndex, 1);
  }

  /**
   * Add where clause to the loading statement.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {ConditionJson} exprJson
   * @memberof LoadingMappingLogic
   */
  static addWhereClause(loadToEntity: LoadToVertexData | LoadToEdgeData, exprJson: ConditionJson) {
    if (!exprJson || exprJson.type === "NullCondition") {
      loadToEntity.style.whereClauseJson = undefined;
    } else {
      loadToEntity.style.whereClauseJson = exprJson;
    }
  }

  /**
   * Map from string literal to one column in vertex | edge.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingsIndex
   * @param {string} literal
   * @memberof LoadingMappingLogic
   */
  static mapStringLiteralToEntity(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingsIndex: number,
    literal: string
  ) {
    loadToEntity.mappings[mappingsIndex] = {
      sourceType: SourceType.Literal,
      literal: literal,
    };
  }

  /**
   * Map from string literal to one input of mapping widget.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingWidgetIndex
   * @param {number} mappingWidgetParamIndex
   * @param {string} literal
   * @memberof LoadingMappingLogic
   */
  static mapStringLiteralToMappingWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingWidgetIndex: number,
    mappingWidgetParamIndex: number,
    literal: string
  ) {
    loadToEntity.mappingWidgets[mappingWidgetIndex].params[mappingWidgetParamIndex] = {
      sourceType: SourceType.Literal,
      literal: literal,
    };
  }

  /**
   * Map from default (no mapping) to one column in vertex | edge.
   * This is used when remove mapping from one column in vertex | edge.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingsIndex
   * @memberof LoadingMappingLogic
   */
  static mapDefaultToEntity(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingsIndex: number
  ) {
    loadToEntity.mappings[mappingsIndex] = {
      sourceType: SourceType.Default,
    };
  }

  /**
   * Map from default (no mapping) to one input of mapping widget.
   * This is used when remove mapping from one input of mapping widget.
   *
   * @static
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {number} mappingWidgetIndex
   * @param {number} mappingWidgetParamIndex
   * @memberof LoadingMappingLogic
   */
  static mapDefaultToMappingWidget(
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    mappingWidgetIndex: number,
    mappingWidgetParamIndex: number
  ) {
    loadToEntity.mappingWidgets[mappingWidgetIndex].params[mappingWidgetParamIndex] = {
      sourceType: SourceType.Default,
    };
  }

  /**
   * Build the auto-mapping from data source to graph entity based on name mapping.
   * The auto-mapping depends on name matching (ignore case, ignore any character other than
   * letters and numbers)
   *
   * @static
   * @param {LoadingJobData} loadingJobData
   * @param {(LoadToVertexData | LoadToEdgeData)} loadToEntity
   * @param {DataFormat} dataFormat
   * @param {DataFieldSchema[]} dataSchema
   * @param {Graph} schema
   * @memberof LoadingMappingLogic
   */
  static autoMapping(
    loadingJobData: LoadingJobData,
    loadToEntity: LoadToVertexData | LoadToEdgeData,
    dataFormat: DataFormat,
    dataSchema: DataFieldSchema[],
    schema: Graph
  ) {
    let attributeStart = 0;
    let attributes: Attribute[];
    if ("vertexName" in loadToEntity) {
      // Load to vertex

      attributeStart = 1;
      const vertex = schema.getVertex((<LoadToVertexData>loadToEntity).vertexName);
      attributes = vertex.attributes;
      // Map primary id
      if (loadToEntity.mappings[0].sourceType === SourceType.Default) {
        loadingJobData.dataSet.dataSchema.forEach((dataField, i) => {
          if (
            HelperFunctions.removeConcatCharsAndToLowerCase(dataField.name) ===
            HelperFunctions.removeConcatCharsAndToLowerCase(vertex.primaryId.name)
          ) {
            loadToEntity.mappings[0] = {
              sourceType: SourceType.DataSourceColumn,
              index: i,
            };
          }
        });
      }
    } /* if ('edgeName' in loadToEntity)*/ else {
      // Load to edge
      attributeStart = 2;
      const edge = schema.getEdge((<LoadToEdgeData>loadToEntity).edgeName);
      attributes = edge.attributes;
      // Map source vertex type
      if (loadToEntity.mappings[0].sourceType === SourceType.Default) {
        loadingJobData.dataSet.dataSchema.forEach((dataField, i) => {
          if (
            HelperFunctions.removeConcatCharsAndToLowerCase(dataField.name) ===
            HelperFunctions.removeConcatCharsAndToLowerCase(loadToEntity.fromVertexType)
          ) {
            loadToEntity.mappings[0] = {
              sourceType: SourceType.DataSourceColumn,
              index: i,
            };
          }
        });
      }
      // Map target vertex type
      if (loadToEntity.mappings[1].sourceType === SourceType.Default) {
        loadingJobData.dataSet.dataSchema.forEach((dataField, i) => {
          if (
            HelperFunctions.removeConcatCharsAndToLowerCase(dataField.name) ===
            HelperFunctions.removeConcatCharsAndToLowerCase(loadToEntity.toVertexType)
          ) {
            loadToEntity.mappings[1] = {
              sourceType: SourceType.DataSourceColumn,
              index: i,
            };
          }
        });
      }
    }

    // Auto map attributes
    for (let i = attributeStart; i < loadToEntity.mappings.length; i++) {
      // Skip auto mapping for MAP and UDT type attributes.
      let attributeType: string;
      let attributeValueType: string;
      if ("vertexName" in loadToEntity) {
        attributeType = schema.getVertex(loadToEntity.vertexName).attributes[i - attributeStart]
          .type.name;
        attributeValueType = schema.getVertex(loadToEntity.vertexName).attributes[
          i - attributeStart
        ].type.valueTypeName;
      } else if ("edgeName" in loadToEntity) {
        attributeType = schema.getEdge(loadToEntity.edgeName).attributes[i - attributeStart].type
          .name;
        attributeValueType = schema.getEdge(loadToEntity.edgeName).attributes[i - attributeStart]
          .type.valueTypeName;
      }
      if (attributeType === "MAP" || attributeType === "UDT" || attributeValueType === "UDT") {
        continue;
      }

      if (loadToEntity.mappings[i].sourceType === SourceType.Default) {
        loadingJobData.dataSet.dataSchema.forEach((dataField, j) => {
          if (
            HelperFunctions.removeConcatCharsAndToLowerCase(dataField.name) ===
            HelperFunctions.removeConcatCharsAndToLowerCase(attributes[i - attributeStart].name)
          ) {
            switch (dataFormat) {
              case DataFormat.JSON:
                if (
                  this.jsonAndGSQLTypeMatchCheck(
                    (<JSONDataFieldSchema[]>dataSchema)[j].type,
                    attributes[i - attributeStart].type.getValueType()
                  )
                ) {
                  loadToEntity.mappings[i] = {
                    sourceType: SourceType.DataSourceColumn,
                    index: j,
                  };
                }
                break;
              default:
                loadToEntity.mappings[i] = {
                  sourceType: SourceType.DataSourceColumn,
                  index: j,
                };
                break;
            }
          }
        });
      }
    }
  }
}
