import { cloneDeep } from 'lodash';
import { DBGraphStyleJson } from '../topology';
import { FileFormat } from './data-set.interface';
import { DBLoading<PERSON>ob<PERSON><PERSON> } from './db-loading-job.interface';
import { GSQLLoadingJob<PERSON>son } from './gsql-loading-job.interface';
import { LoadingJobChangeLogic, LoadingJobChangeStatus } from './loading-job-change-logic';

const mockGSQLLoadingJob: GSQLLoadingJobJson = {
  FileNames: {
    MyDataSource: ''
  },
  Type: 'Offline',
  GraphName: 'social',
  JobName: 'load_job',
  Headers: {},
  Filters: [],
  LoadingStatements: [
    {
      Type: 'Vertex',
      TargeName: 'people',
      DataSource: {
        Type: 'FileVar',
        Value: 'MyDataSource'
      },
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        }
      ],
      UsingClauses: {
        EOL: '\\n',
        SEPARATOR: ',',
        HEADER: 'false'
      }
    }
  ]
};
const mockLoadingJobStyle: DBLoadingJob<PERSON>son = {
  loadingJobName: 'load_job',
  loadingStatementsStyle: [
    {
      sourcePosition: {
        x: 0.2, y: 0.5
      },
      targetPosition: {
        x: 0.8, y: 0.5
      },
      middlePositions: []
    }
  ],
  dataSourceJson: {
    type: 'file',
    uri: '/resources/data_set/gsql/person.csv',
    options: {
      fileFormat: FileFormat.None,
      eol: '\n',
      separator: ',',
      header: 'true'
    },
    position: {
      x: 0.5,
      y: 0.2
    }
  },
  header: ['name', 'age', 'col1', 'col2'],
  sampleData: [['src', '25', '', ''], ['src2', '26', '', '']]
};
const mockGraphStyle: DBGraphStyleJson = {
  vertexStyles: {
    person: {
      fillColor: '#2ca02c',
      x: -385,
      y: -66,
      icon: 'icon_274',
      other: {}
    },
    movie: {
      fillColor: '#d62728',
      x: 150,
      y: 0,
      other: {}
    }
  },
  edgeStyles: {
    rate: {
      fillColor: '#ff7f0e',
      other: {}
    }
  }
};

describe('LoadingJobChangeLogic', () => {

  describe('should get the loading job change correctly', () => {
    it('with data mapping change', () => {
      const mockGSQLLoadingJob1 = cloneDeep(mockGSQLLoadingJob);
      mockGSQLLoadingJob1.LoadingStatements[0].UsingClauses.HEADER = 'true';
      const loadingJobChange = LoadingJobChangeLogic.getLoadingJobChange(
        [mockGSQLLoadingJob],
        [mockGSQLLoadingJob1],
        [mockLoadingJobStyle],
        [mockLoadingJobStyle],
        mockGraphStyle,
        mockGraphStyle
      );

      expect(loadingJobChange).toEqual({
        status: LoadingJobChangeStatus.DataMappingChange,
        jobNames: ['load_job']
      });
    });

    it('with style change', () => {
      const mockLoadingJobStyle1 = cloneDeep(mockLoadingJobStyle);
      mockLoadingJobStyle1.loadingStatementsStyle[0].targetPosition = { x: 0.2, y: 0.5 };
      const loadingJobChange = LoadingJobChangeLogic.getLoadingJobChange(
        [mockGSQLLoadingJob],
        [mockGSQLLoadingJob],
        [mockLoadingJobStyle],
        [mockLoadingJobStyle1],
        mockGraphStyle,
        mockGraphStyle
      );

      expect(loadingJobChange).toEqual({
        status: LoadingJobChangeStatus.StyleChange,
        jobNames: ['load_job']
      });
    });

    it('with no change', () => {
      const loadingJobChange = LoadingJobChangeLogic.getLoadingJobChange(
        [mockGSQLLoadingJob],
        [mockGSQLLoadingJob],
        [mockLoadingJobStyle],
        [mockLoadingJobStyle],
        mockGraphStyle,
        mockGraphStyle
      );

      expect(loadingJobChange).toEqual({
        status: LoadingJobChangeStatus.NoChange,
        jobNames: []
      });
    });
  });
});
