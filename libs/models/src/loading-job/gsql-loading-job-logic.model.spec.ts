import { cloneDeep } from 'lodash';
import { GSQLLoadingJobJson } from '.';
import { GSQLDataSource, GSQLDataSourceLogic } from './gsql-data-source-logic';
import { GSQLLoadingJobLogic } from './gsql-loading-job-logic.model';

const mockGSQLLoadingJobA: GSQLLoadingJobJson = {
  FileNames: {
    MyDataSource: ''
  },
  Type: 'Offline',
  GraphName: 'social',
  JobName: 'gcs_load_job_A',
  Headers: {},
  Filters: [],
  LoadingStatements: [
    {
      Type: 'Vertex',
      TargeName: 'people',
      DataSource: {
        Type: 'FileVar',
        Value: 'MyDataSource'
      },
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        }
      ],
      UsingClauses: {
        EOL: '\\n',
        SEPARATOR: ',',
        HEADER: 'false'
      }
    }
  ]
};
const mockGSQLDataSourceB: GSQLDataSource = {
  name: 'gcs_data_source_gcs_load_job_B',
  type: 'kafka',
  config: {
    broker: ''
  }
};
const mockGSQLLoadingJobB: GSQLLoadingJobJson = {
  FileNames: {
    MyDataSource: ''
  },
  Type: 'Offline',
  GraphName: 'social',
  JobName: 'gcs_load_job_B',
  Headers: {},
  Filters: [],
  LoadingStatements: [
    {
      Type: 'Vertex',
      TargeName: 'movie',
      DataSource: {
        Type: 'FileVar',
        Value: 'MyDataSource'
      },
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 1
        }
      ],
      UsingClauses: {
        EOL: '\\n',
        SEPARATOR: ',',
        HEADER: 'false'
      },
    }
  ]
};
const mockGSQLDataSourceC: GSQLDataSource = {
  name: 'gcs_data_source_gcs_load_job_C',
  type: 'kafka',
  config: {
    broker: ''
  }
};
const mockGSQLLoadingJobC: GSQLLoadingJobJson = {
  FileNames: {
    MyDataSource: ''
  },
  Type: 'Offline',
  GraphName: 'social',
  JobName: 'gcs_load_job_C',
  Headers: {},
  Filters: [],
  LoadingStatements: [
    {
      Type: 'Edge',
      TargeName: 'watch',
      FromVertexType: 'people',
      ToVertexType: 'movie',
      DataSource: {
        Type: 'FileVar',
        Value: 'MyDataSource'
      },
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        },
        {
          Type: 'SrcColIndex',
          Value: 1
        }
      ],
      UsingClauses: {
        EOL: '\\n',
        SEPARATOR: ',',
        HEADER: 'false'
      },
    }
  ]
};

const mockGSQLLoadingJobWithIsEmptyFilter = {
  Type: 'Offline',
  GraphName: 'graph',
  JobName: 'load_job_is_empty',
  Headers: {},
  Filters: [],
  LoadingStatements: [
    {
      DataSource: {
        Type: 'FileVar',
        Value: 'MyDataSource'
      },
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        }
      ],
      TargetName: 'vertex_type_1',
      Type: 'Vertex',
      UsingClauses: {
        EOL: '\\n',
        HEADER: 'true',
        SEPARATOR: ',',
      },
      WhereClause: '($1 IS EMPTY)'
    }
  ],
  FileNames: {
    MyDataSource: ''
  }
};

const mockGSQLLoadingJobWithIsNumericFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithIsNumericFilter.JobName = 'load_job_is_numeric';
mockGSQLLoadingJobWithIsNumericFilter.LoadingStatements[0].WhereClause = '($0 IS NUMERIC)';

const mockGSQLLoadingJobWithEqualityFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithEqualityFilter.JobName = 'load_job_equality';
mockGSQLLoadingJobWithEqualityFilter.LoadingStatements[0].WhereClause = '($1 == $0)';

const mockGSQLLoadingJobWithInequalityFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithInequalityFilter.JobName = 'load_job_inequality';
mockGSQLLoadingJobWithInequalityFilter.LoadingStatements[0].WhereClause = '($1 != $0)';

const mockGSQLLoadingJobWithGreaterThanFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithGreaterThanFilter.JobName = 'load_job_greater_than';
mockGSQLLoadingJobWithGreaterThanFilter.LoadingStatements[0].WhereClause = '($1 > $0)';

const mockGSQLLoadingJobWithGreaterThanEqualToFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithGreaterThanEqualToFilter.JobName = 'load_job_greater_than_equal';
mockGSQLLoadingJobWithGreaterThanEqualToFilter.LoadingStatements[0].WhereClause = '($1 >= $0)';

const mockGSQLLoadingJobWithLessThanFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithLessThanFilter.JobName = 'load_job_less_than';
mockGSQLLoadingJobWithLessThanFilter.LoadingStatements[0].WhereClause = '($1 < $0)';

const mockGSQLLoadingJobWithLessThanEqualToFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithLessThanEqualToFilter.JobName = 'load_job_less_than_equal';
mockGSQLLoadingJobWithLessThanEqualToFilter.LoadingStatements[0].WhereClause = '($1 <= $0)';

const mockGSQLLoadingJobWithInFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithInFilter.JobName = 'load_job_in';
mockGSQLLoadingJobWithInFilter.LoadingStatements[0].WhereClause = '($0 IN (\"test\"))';

const mockGSQLLoadingJobWithNotFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithNotFilter.JobName = 'load_job_not';
mockGSQLLoadingJobWithNotFilter.LoadingStatements[0].WhereClause = '(NOT ($0 == $1))';

const mockGSQLLoadingJobWithBetweenFilter = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithBetweenFilter.JobName = 'load_job_between';
mockGSQLLoadingJobWithBetweenFilter.LoadingStatements[0].WhereClause = '($2 BETWEEN \"test\" AND \"test\")';

const mockGSQLLoadingJobWithOrNestedFilter1 = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithOrNestedFilter1.JobName = 'load_job_or_1';
mockGSQLLoadingJobWithOrNestedFilter1.LoadingStatements[0].WhereClause = '(($0 IS NUMERIC) OR ($0 BETWEEN \"12\" AND \"14\"))';

const mockGSQLLoadingJobWithAndNestedFilter1 = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithAndNestedFilter1.JobName = 'load_job_and_1';
mockGSQLLoadingJobWithAndNestedFilter1.LoadingStatements[0].WhereClause = '(($0 IS EMPTY) AND ($0 == $0))';

const mockGSQLLoadingJobWithOrNestedFilter2 = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithOrNestedFilter2.JobName = 'load_job_or_2';
mockGSQLLoadingJobWithOrNestedFilter2.LoadingStatements[0].WhereClause = '(($0 != $1) OR ($1 > $2))';

const mockGSQLLoadingJobWithAndNestedFilter2 = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithAndNestedFilter2.JobName = 'load_job_and_2';
mockGSQLLoadingJobWithAndNestedFilter2.LoadingStatements[0].WhereClause = '(($0 <= $1) AND ($2 >= $3))';

const mockGSQLLoadingJobWithAllFilters1 = cloneDeep(mockGSQLLoadingJobWithIsEmptyFilter);
mockGSQLLoadingJobWithAllFilters1.JobName = 'load_job_all_filters';
mockGSQLLoadingJobWithAllFilters1.LoadingStatements[0].WhereClause = '\
  ((((((((($0 == $1) OR ($0 != $1)) AND ($0 > $1)) OR \
  ($0 >= $1)) AND ($0 < $1)) OR ($0 <= $1)) AND ($0 IS NUMERIC)) OR \
  ($0 IS EMPTY)) AND ((($0 BETWEEN "10" AND "20") OR \
  ($0 IN ("10"))) AND (NOT ((($0 IS NUMERIC) OR ($0 IS EMPTY)) AND \
  ($0 BETWEEN "10" AND "12")))))';

describe('GSQLLoadingJob', () => {
  it('should handle where clause in GSQL loading job load statement correctly', () => {
    const mockGSQLLoadingJobA1 = cloneDeep(mockGSQLLoadingJobA);
    mockGSQLLoadingJobA1.LoadingStatements[0].WhereClause = '($2 < \"2021-01-01\")';
    const mockGSQLLoadingJobs: GSQLLoadingJobJson[] = [mockGSQLLoadingJobA1];

    const gsqlLoadingJobs = GSQLLoadingJobLogic.handleWhereClause(mockGSQLLoadingJobs);
    expect(gsqlLoadingJobs.length).toEqual(1);

    const statement = gsqlLoadingJobs[0].LoadingStatements;
    expect(statement.length).toEqual(1);
    expect(statement[0].WhereClause).toEqual('$2<\"2021-01-01\"');
  });

  describe('should get GSQL loading job change correctly', () => {
    it ('without changes', () => {
      const savedJobs = [mockGSQLLoadingJobA, mockGSQLLoadingJobB, mockGSQLLoadingJobC];
      const inMemoryJobs = [mockGSQLLoadingJobA, mockGSQLLoadingJobB, mockGSQLLoadingJobC];
      const gsqlLoadingJobChange = GSQLLoadingJobLogic.getGSQLLoadingJobChange(
        savedJobs, inMemoryJobs
      );

      expect(gsqlLoadingJobChange).toEqual(
        {
          isChanged: false,
          loadingJobNamesToDelete: [],
          loadingJobsToAdd: []
        }
      );
    });

    it('with changes in Kafka loading job', () => {
      const mockGSQLLoadingJobB1 = cloneDeep(mockGSQLLoadingJobB);
      mockGSQLLoadingJobB1.LoadingStatements[0].UsingClauses.HEADER = 'true';

      // The user deletes loading job A, updates B, creates C.
      const savedJobs = [mockGSQLLoadingJobA, mockGSQLLoadingJobB];
      const inMemoryJobs = [mockGSQLLoadingJobB1, mockGSQLLoadingJobC];
      const gsqlLoadingJobChange = GSQLLoadingJobLogic.getGSQLLoadingJobChange(
        savedJobs, inMemoryJobs
      );

      expect(gsqlLoadingJobChange.isChanged).toBeTruthy();

      // We delete GSQL loading job A, B, and C first.
      const loadingJobNamesToDelete = gsqlLoadingJobChange.loadingJobNamesToDelete;
      expect(loadingJobNamesToDelete.length).toBe(3);
      expect(loadingJobNamesToDelete).toContain(mockGSQLLoadingJobA.JobName);
      expect(loadingJobNamesToDelete).toContain(mockGSQLLoadingJobB.JobName);
      expect(loadingJobNamesToDelete).toContain(mockGSQLLoadingJobC.JobName);

      // Then we add GSQL loading job B1 and C.
      const loadingJobsToAdd = gsqlLoadingJobChange.loadingJobsToAdd;
      expect(loadingJobsToAdd.length).toBe(2);
      expect(loadingJobsToAdd).toContain(mockGSQLLoadingJobB1);
      expect(loadingJobsToAdd).toContain(mockGSQLLoadingJobC);
    });

    it('with changes in non-Kafka loading job', () => {
      const mockGSQLLoadingJobA1 = cloneDeep(mockGSQLLoadingJobA);
      mockGSQLLoadingJobA1.JobName = 'load_job_A';

      // The user creates loading job A.
      const savedJobs = [];
      const inMemoryJobs = [mockGSQLLoadingJobA1];
      const gsqlLoadingJobChange = GSQLLoadingJobLogic.getGSQLLoadingJobChange(
        savedJobs, inMemoryJobs
      );

      expect(gsqlLoadingJobChange.isChanged).toBeTruthy();

      // We delete GSQL loading job A1 first.
      const loadingJobNamesToDelete = gsqlLoadingJobChange.loadingJobNamesToDelete;
      expect(loadingJobNamesToDelete.length).toBe(1);
      expect(loadingJobNamesToDelete).toContain(mockGSQLLoadingJobA1.JobName);

      // Then we add GSQL loading job A1.
      const loadingJobsToAdd = gsqlLoadingJobChange.loadingJobsToAdd;
      expect(loadingJobsToAdd.length).toBe(1);
      expect(loadingJobsToAdd).toContain(mockGSQLLoadingJobA1);
    });

    it('with changes in non-Kafka loading job with data filter', () => {
      // The user creates loading jobs with data filter.
      const savedJobs = [];
      const inMemoryJobs = [
        mockGSQLLoadingJobWithIsEmptyFilter,
        mockGSQLLoadingJobWithIsNumericFilter,
        mockGSQLLoadingJobWithEqualityFilter,
        mockGSQLLoadingJobWithInequalityFilter,
        mockGSQLLoadingJobWithGreaterThanFilter,
        mockGSQLLoadingJobWithGreaterThanEqualToFilter,
        mockGSQLLoadingJobWithLessThanFilter,
        mockGSQLLoadingJobWithLessThanEqualToFilter,
        mockGSQLLoadingJobWithInFilter,
        mockGSQLLoadingJobWithNotFilter,
        mockGSQLLoadingJobWithBetweenFilter,
        mockGSQLLoadingJobWithOrNestedFilter1,
        mockGSQLLoadingJobWithAndNestedFilter1,
        mockGSQLLoadingJobWithOrNestedFilter2,
        mockGSQLLoadingJobWithAndNestedFilter2,
        mockGSQLLoadingJobWithAllFilters1
      ];
      const gsqlLoadingJobChange = GSQLLoadingJobLogic.getGSQLLoadingJobChange(
        savedJobs, inMemoryJobs
      );

      expect(gsqlLoadingJobChange.isChanged).toBeTruthy();

      // We delete GSQL loading job IsEmptyFilter1 and IsNumericFilter1 first.
      const loadingJobNamesToDelete = gsqlLoadingJobChange.loadingJobNamesToDelete;
      expect(loadingJobNamesToDelete.length).toBe(16);
      expect(loadingJobNamesToDelete).toEqual(inMemoryJobs.map(job => job.JobName));

      // Then we add GSQL loading job IsEmptyFilter1 and IsNumericFilter1.
      const loadingJobsToAdd = gsqlLoadingJobChange.loadingJobsToAdd;
      expect(loadingJobsToAdd.length).toBe(16);
      expect(loadingJobsToAdd).toEqual(inMemoryJobs);
    });
  });

  describe ('should get unsaved loading job names correctly', () => {
    it('with new job', () => {
      const savedJobs = [];
      const inMemoryJobs = [mockGSQLLoadingJobA];

      const jobNames = GSQLLoadingJobLogic.getUnsavedGSQLLoadingJobNames(savedJobs, inMemoryJobs);
      expect(jobNames.length).toEqual(1);
      expect(jobNames).toContain('gcs_load_job_A');
    });

    it('with updated job', () => {
      const savedJobs = [mockGSQLLoadingJobA];

      const mockGSQLLoadingJobA1 = cloneDeep(mockGSQLLoadingJobA);
      mockGSQLLoadingJobA1.LoadingStatements[0].UsingClauses.HEADER = 'true';
      const inMemoryJobs = [mockGSQLLoadingJobA1];

      const jobNames = GSQLLoadingJobLogic.getUnsavedGSQLLoadingJobNames(savedJobs, inMemoryJobs);
      expect(jobNames.length).toEqual(1);
      expect(jobNames).toContain('gcs_load_job_A');
    });
  });

});
