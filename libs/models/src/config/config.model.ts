import { UserRole } from '../user';

/**
 * Configurations from server.
 *
 * @export
 * @class GraphStudioConfig
 */
export class GraphStudioConfig {
  error?: boolean;
  webServer?: {
    websocketMaxIdleTime: number;
    clientIdleTimeMS: number;
    googleAnalyticsId: string;
    secureConnection: {
      enable: boolean;
    };
    debug: {
      logLevel: string;
      websocketPort: number;
      enable: boolean;
    };
  };
  userRole?: {
    name: string;
    roles: UserRole;
    isGlobalDesigner: boolean;
    isSuperUser: boolean;
  };
  engine: {
    port: number
  };
}
