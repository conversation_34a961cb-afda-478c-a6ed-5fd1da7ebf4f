export enum GSQL_COMMAND {
  COOKIE = '__GSQL__COOKIES__',
  RETURN_CODE = '__GSQL__RETURN__CODE__',
  MOVE_CURSOR_UP = '__GSQL__MOVE__CURSOR___UP__',
  CLEAN_LINE = '__GSQL__CLEAN__LINE__',
}

export interface ParsedResult {
  ret: string;
  cookie: { [key: string]: any } | null;
  retCode?: number;
}

export function parseRes(ret: string): ParsedResult {
  let cookie = null;
  let retCode: number | undefined;
  // handle cookie firstly, the script may contain multiple commands and
  // each command result will be followed by a cookie line
  let cmdStartIdx: number;
  while ((cmdStartIdx = ret.indexOf(GSQL_COMMAND.COOKIE)) > -1) {
    const cmdNewline = ret.indexOf('\n', cmdStartIdx);
    const cookieLine = ret.slice(cmdStartIdx, cmdNewline + 1);
    const cookieText = cookieLine.slice(GSQL_COMMAND.COOKIE.length + 1);
    try {
      cookie = JSON.parse(cookieText);
    } catch (e) {
      console.log('parse cookie error', e);
    }
    ret = ret.replace(cookieLine, '');
  }

  // after remove all cookie lines, the remain text will be result content and return code
  cmdStartIdx = ret.indexOf(GSQL_COMMAND.RETURN_CODE);
  if (cmdStartIdx > -1) {
    const cmdNewline = ret.indexOf('\n', cmdStartIdx);
    const returnCodeLine = ret.slice(cmdStartIdx, cmdNewline + 1);
    const returnCodeText = returnCodeLine.slice(GSQL_COMMAND.RETURN_CODE.length + 1);
    ret = ret.slice(0, cmdStartIdx);
    retCode = parseInt(returnCodeText);
  }

  return { ret, cookie, retCode };
}

export function handleGSQLReslt(cursorIdx: number, data: string, parsedData: ParsedResult) {
  let result = data;
  const lines = parsedData.ret.split('\n');
  lines.forEach((line: string, lineIdx: number) => {
    if (lineIdx !== lines.length - 1) {
      line += '\n';
    }
    if (line.startsWith(GSQL_COMMAND.MOVE_CURSOR_UP)) {
      let num = parseInt(line.split(',')[1]);
      let idx = cursorIdx;
      while (num && (idx = result.lastIndexOf('\n', idx - 1)) > -1) {
        --num;
      }
      idx = result.lastIndexOf('\n', idx - 1);
      cursorIdx = idx === -1 ? 0 : idx + 1;
    } else if (line.startsWith(GSQL_COMMAND.CLEAN_LINE)) {
      const newLineIdx = result.indexOf('\n', cursorIdx);
      if (newLineIdx > -1) {
        result = result.slice(0, cursorIdx) + result.slice(newLineIdx + 1);
      }
    } else if (line.indexOf('\r') > -1) {
      const lastCarriageIdx = line.lastIndexOf('\r');
      const lastLine = result.lastIndexOf('\n');
      result = result.slice(0, lastLine + 1) + line.slice(lastCarriageIdx + 1);
      cursorIdx = result.length;
    } else {
      result = result.slice(0, cursorIdx) + line + result.slice(cursorIdx);
      cursorIdx += line.length;
    }
  });

  return { cursorIdx, result };
}